/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'root': RouteRecordInfo<'root', '/', Record<never, never>, Record<never, never>>,
    '$error': RouteRecordInfo<'$error', '/:error(.*)', { error: ParamValue<true> }, { error: ParamValue<false> }>,
    'analytics': RouteRecordInfo<'analytics', '/analytics', Record<never, never>, Record<never, never>>,
    'audit-trail': RouteRecordInfo<'audit-trail', '/audit-trail', Record<never, never>, Record<never, never>>,
    'certification-management': RouteRecordInfo<'certification-management', '/certification-management', Record<never, never>, Record<never, never>>,
    'certification-management-id': RouteRecordInfo<'certification-management-id', '/certification-management/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'certification-management-id-certification-cid': RouteRecordInfo<'certification-management-id-certification-cid', '/certification-management/:id/certification/:cid', { id: ParamValue<true>, cid: ParamValue<true> }, { id: ParamValue<false>, cid: ParamValue<false> }>,
    'certification-management-id-certification-cid-year': RouteRecordInfo<'certification-management-id-certification-cid-year', '/certification-management/:id/certification/:cid/:year', { id: ParamValue<true>, cid: ParamValue<true>, year: ParamValue<true> }, { id: ParamValue<false>, cid: ParamValue<false>, year: ParamValue<false> }>,
    'certification-management-id-certification-cid-year-completed': RouteRecordInfo<'certification-management-id-certification-cid-year-completed', '/certification-management/:id/certification/:cid/:year/completed', { id: ParamValue<true>, cid: ParamValue<true>, year: ParamValue<true> }, { id: ParamValue<false>, cid: ParamValue<false>, year: ParamValue<false> }>,
    'certification-management-id-certification-cid-year-started': RouteRecordInfo<'certification-management-id-certification-cid-year-started', '/certification-management/:id/certification/:cid/:year/started', { id: ParamValue<true>, cid: ParamValue<true>, year: ParamValue<true> }, { id: ParamValue<false>, cid: ParamValue<false>, year: ParamValue<false> }>,
    'certification-management-active': RouteRecordInfo<'certification-management-active', '/certification-management/active', Record<never, never>, Record<never, never>>,
    'certification-management-certification-dashboard': RouteRecordInfo<'certification-management-certification-dashboard', '/certification-management/CertificationDashboard', Record<never, never>, Record<never, never>>,
    'change-proposals-history': RouteRecordInfo<'change-proposals-history', '/change-proposals/history', Record<never, never>, Record<never, never>>,
    'change-proposals-new-participants': RouteRecordInfo<'change-proposals-new-participants', '/change-proposals/new-participants', Record<never, never>, Record<never, never>>,
    'change-proposals-new-participants-id': RouteRecordInfo<'change-proposals-new-participants-id', '/change-proposals/new-participants/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'change-proposals-new-participants-review-id': RouteRecordInfo<'change-proposals-new-participants-review-id', '/change-proposals/new-participants/review/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'change-proposals-new-pension-parameters': RouteRecordInfo<'change-proposals-new-pension-parameters', '/change-proposals/new-pension-parameters', Record<never, never>, Record<never, never>>,
    'change-proposals-requested': RouteRecordInfo<'change-proposals-requested', '/change-proposals/requested', Record<never, never>, Record<never, never>>,
    'debug-pusher': RouteRecordInfo<'debug-pusher', '/debug/pusher', Record<never, never>, Record<never, never>>,
    'forgot-password': RouteRecordInfo<'forgot-password', '/forgot-password', Record<never, never>, Record<never, never>>,
    'login': RouteRecordInfo<'login', '/login', Record<never, never>, Record<never, never>>,
    'notifications': RouteRecordInfo<'notifications', '/notifications', Record<never, never>, Record<never, never>>,
    'participant-export-id': RouteRecordInfo<'participant-export-id', '/participant-export/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'participants': RouteRecordInfo<'participants', '/participants', Record<never, never>, Record<never, never>>,
    'participants-id': RouteRecordInfo<'participants-id', '/participants/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'participants-add': RouteRecordInfo<'participants-add', '/participants/add', Record<never, never>, Record<never, never>>,
    'pension-parameters': RouteRecordInfo<'pension-parameters', '/pension-parameters', Record<never, never>, Record<never, never>>,
    'permission-restricted': RouteRecordInfo<'permission-restricted', '/permission-restricted', Record<never, never>, Record<never, never>>,
    'reset-password': RouteRecordInfo<'reset-password', '/reset-password', Record<never, never>, Record<never, never>>,
    'second-page': RouteRecordInfo<'second-page', '/second-page', Record<never, never>, Record<never, never>>,
    'test-certified-data': RouteRecordInfo<'test-certified-data', '/test-certified-data', Record<never, never>, Record<never, never>>,
    'users': RouteRecordInfo<'users', '/users', Record<never, never>, Record<never, never>>,
  }
}
