<script setup lang="ts">
import { computed } from 'vue'

// Types
interface Participant {
  id: string
  personalInfo: {
    firstName: string
    lastName: string
    email?: string
  }
  employmentInfo: {
    department: string
  }
  certificationStatus: string
  changes: number
  risk: string
  lastActivity?: string
  certificationId?: string | null
}

interface Props {
  participants: Participant[]
  loading?: boolean
  selected?: Participant[]
}

interface Emits {
  (e: 'update:selected', value: Participant[]): void
  (e: 'view-participant', participant: Participant): void
  (e: 'approve-participant', participant: Participant): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selected: () => [],
})

const emit = defineEmits<Emits>()

// Local selected state
const localSelected = computed({
  get: () => props.selected,
  set: value => emit('update:selected', value),
})

// Table configuration
const headers = [
  { title: 'Name', key: 'personalInfo', sortable: true },
  { title: 'Status', key: 'certificationStatus', sortable: true },
  { title: 'Changes', key: 'changes', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false },
]

// Utility functions
const getInitials = (personalInfo: any) => {
  return `${personalInfo.firstName.charAt(0)}${personalInfo.lastName.charAt(0)}`
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'started': return 'info'
    case 'completed': return 'success'
    case 'requires-attention': return 'error'
    default: return 'grey'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending': return 'tabler-clock'
    case 'started': return 'tabler-clock-play'
    case 'completed': return 'tabler-check'
    case 'requires-attention': return 'tabler-alert-triangle'
    default: return 'tabler-circle'
  }
}

const getRiskColor = (risk: string) => {
  switch (risk) {
    case 'low': return 'success'
    case 'medium': return 'warning'
    case 'high': return 'error'
    case 'none': return 'primary'
    default: return 'primary'
  }
}

const getRiskText = (risk: string) => {
  switch (risk) {
    case 'low': return 'Low Risk'
    case 'medium': return 'Medium Risk'
    case 'high': return 'High Risk'
    case 'none': return 'No Risk'
    default: return 'No Risk'
  }
}

const formatLastActivity = (lastActivity?: string) => {
  if (!lastActivity)
    return '-'

  return lastActivity
}

// Action methods
const viewParticipant = (participant: Participant) => {
  emit('view-participant', participant)
}

const approveParticipant = (participant: Participant) => {
  emit('approve-participant', participant)
}

const selectAll = () => {
  localSelected.value = [...props.participants]
}

const clearSelection = () => {
  localSelected.value = []
}
</script>

<template>
  <VCard>
    <VCardTitle class="d-flex align-center">
      <span>Participants ({{ participants.length }})</span>
      <VSpacer />
      <div class="text-caption text-medium-emphasis me-4">
        Showing {{ participants.length }} of {{ participants.length }} participants
      </div>
    </VCardTitle>

    <VDataTable
      v-model="localSelected"
      :headers="headers"
      :items="participants"
      :items-per-page="25"
      item-key="id"
      show-select
      hover
      :loading="loading"
      class="elevation-0"
      no-data-text="No participants available for certification"
    >
      <template #item.personalInfo="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            color="primary"
            size="32"
            class="me-3"
          >
            <VIcon icon="tabler-user" />
          </VAvatar>
          <div>
            <div class="font-weight-medium">
              {{ item.personalInfo.firstName }} {{ item.personalInfo.lastName }}
            </div>
            <div class="text-caption text-medium-emphasis">
              {{ item.personalInfo.email || `${item.personalInfo.firstName.toLowerCase()}.${item.personalInfo.lastName.toLowerCase()}@company.com` }}
            </div>
          </div>
        </div>
      </template>

      <template #item.certificationStatus="{ item }">
        <VChip
          :color="getStatusColor(item.certificationStatus)"
          size="small"
          variant="flat"
        >
          <VIcon
            start
            size="16"
          >
            {{ getStatusIcon(item.certificationStatus) }}
          </VIcon>
          {{ item.certificationStatus === 'started' ? 'In Progress'
            : item.certificationStatus === 'requires-attention' ? 'Requires Attention'
              : item.certificationStatus.charAt(0).toUpperCase() + item.certificationStatus.slice(1) }}
        </VChip>
      </template>

      <template #item.changes="{ item }">
        <div class="d-flex align-center">
          <div class="d-flex align-center">
            <VIcon
              icon="tabler-edit-circle"
              size="20"
              class="me-1"
              :color="item.changes > 0 ? 'info' : 'success'"
            />
            <span>{{ item.changes }}</span>
          </div>
        </div>
      </template>

      <template #item.actions="{ item }">
        <div class="d-flex gap-1">
          <VBtn
            size="small"
            variant="text"
            icon="tabler-eye"
            @click="viewParticipant(item)"
          />
        </div>
      </template>
    </VDataTable>
  </VCard>
</template>

<style scoped>
.v-data-table {
  border-radius: 0;
}
</style>
