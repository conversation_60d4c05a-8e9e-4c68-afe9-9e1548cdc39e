<script setup lang="ts">
import { computed, ref } from 'vue'

// Sample stats data
const stats = ref({
  totalParticipants: 0,
  pendingCertifications: 0,
  inProgress: 0,
  completed: 0,
  requiresAttention: 0,
})

const progressPercentage = computed(() => {
  const total = stats.value.totalParticipants
  const completed = stats.value.completed

  return total ? Math.round((completed / total) * 100) : 0
})

// Calculate progress bar segments
const progressSegments = computed(() => {
  const total = stats.value.totalParticipants

  return total
    ? {
      completed: (stats.value.completed / total) * 100,
      inProgress: (stats.value.inProgress / total) * 100,
      pending: (stats.value.pendingCertifications / total) * 100,
      attention: (stats.value.requiresAttention / total) * 100,
      }
    : {
      completed: 0,
      inProgress: 0,
      pending: 0,
      attention: 0,
      };
})
</script>

<template>
  <div class="stats-dashboard">
    <!-- Stats Cards - Equal Width Grid Layout -->
    <VRow
      class="mb-8"
      dense
    >
      <VCol
        v-for="(stat, index) in [
          { key: 'totalParticipants', label: 'Total Participants', icon: 'tabler-users-group', color: 'primary' },
          { key: 'pendingCertifications', label: 'Pending', icon: 'tabler-clock', color: 'warning' },
          { key: 'inProgress', label: 'In Progress', icon: 'tabler-clock-play', color: 'info' },
          { key: 'completed', label: 'Completed', icon: 'tabler-circle-check', color: 'success' },
          { key: 'requiresAttention', label: 'Requires Attention', icon: 'tabler-alert-triangle', color: 'error' },
        ]"
        :key="index"
        class="stat-col"
      >
        <VCard
          class="stats-card"
          elevation="2"
          :class="`bg-${stat.color}--lighten-5`"
          hover
        >
          <VCardText class="pa-6">
            <div class="d-flex align-center justify-space-between">
              <div>
                <div class="text-caption text-medium-emphasis mb-2">
                  {{ stat.label }}
                </div>
                <div
                  class="text-h5 font-weight-semibold"
                  :class="`text-${stat.color}`"
                >
                  {{ stats[stat.key] }}
                </div>
              </div>
              <VIcon
                :icon="stat.icon"
                size="28"
                :class="`text-${stat.color} opacity-80`"
              />
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- Overall Progress Card -->
    <VCard
      elevation="2"
      class="progress-card"
    >
      <VCardTitle class="text-h6 font-weight-semibold">
        Overall Progress
        <VChip
          class="ml-4"
          color="success"
          variant="flat"
          size="small"
        >
          {{ progressPercentage }}%
        </VChip>
      </VCardTitle>

      <VCardText>
        <!-- Single Stacked Progress Bar -->
        <div class="stacked-progress-container mb-4">
          <div class="stacked-progress-bar">
            <div
              class="progress-segment completed-segment"
              :style="{ width: `${progressSegments.completed}%` }"
            />
            <div
              class="progress-segment in-progress-segment"
              :style="{ width: `${progressSegments.inProgress}%` }"
            />
            <div
              class="progress-segment pending-segment"
              :style="{ width: `${progressSegments.pending}%` }"
            />
            <div
              class="progress-segment attention-segment"
              :style="{ width: `${progressSegments.attention}%` }"
            />
          </div>
        </div>

        <!-- Progress Legend -->
        <VRow
          dense
          class="legend-container"
        >
          <VCol
            v-for="(item, index) in [
              { label: `Completed (${stats.completed})`, color: 'success' },
              { label: `In Progress (${stats.inProgress})`, color: 'info' },
              { label: `Pending (${stats.pendingCertifications})`, color: 'warning' },
              { label: `Attention (${stats.requiresAttention})`, color: 'error' },
            ]"
            :key="index"
            cols="6"
            sm="3"
          >
            <div class="d-flex align-center">
              <VAvatar
                :color="item.color"
                size="12"
                class="mr-2"
              />
              <span class="text-caption">{{ item.label }}</span>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
  .stats-dashboard {
    border-radius: 8px;
  }

  /* Equal width columns for stat cards */
  .stat-col {
    flex: 1;
    min-width: 0;
  }

  .stats-card {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    background: linear-gradient(145deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
    backdrop-filter: blur(4px);
    height: 100%;
  }

  .stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  }

  .progress-card {
    background: linear-gradient(145deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
    backdrop-filter: blur(4px);
  }

  /* Stacked Progress Bar Styles */
  .stacked-progress-container {
    width: 100%;
  }

  .stacked-progress-bar {
    display: flex;
    height: 12px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f5f5f5;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
  }

  .progress-segment {
    height: 100%;
    transition: width 0.3s ease;
  }

  .completed-segment {
    background-color: #4caf50;
  }

  .in-progress-segment {
    background-color: #2196f3;
  }

  .pending-segment {
    background-color: #ff9800;
  }

  .attention-segment {
    background-color: #f44336;
  }

  .legend-container {
    padding-top: 8px;
  }

  /* Responsive adjustments */
  @media (max-width: 960px) {
    .stat-col {
      flex: 1 1 calc(50% - 8px);
      max-width: calc(50% - 8px);
    }
  }

  @media (max-width: 600px) {
    .stat-col {
      flex: 1 1 100%;
      max-width: 100%;
    }

    .stats-card {
      margin-bottom: 16px;
    }

    .stats-card .v-card-text {
      padding: 12px !important;
    }

    .text-h5 {
      font-size: 1.25rem !important;
    }
  }
</style>
