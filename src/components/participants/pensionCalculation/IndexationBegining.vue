<script setup lang="ts">
import PensionTableLayout from './PensionTableLayout.vue'
import PensionTableRow from './PensionTableRow.vue'
import PensionsCalcStrip from '@/components/participants/pensionCalculation/PensionsCalcStrip.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'

const pensionStore = usePensionStore()

const {
  shouldShowCertified,
  createExpansionState,
  getColumnYears,
  getStatusClass,
  formatCurrency,
  getColumnData,
} = usePensionBase()

const isIndexationExpanded = createExpansionState(true)

const { leftColumnYear, middleColumnYear, rightColumnYear } = getColumnYears()

const getIndexationData = (year: number | null) => {
  return getColumnData(year, 'certifiedIndexationStartOfYear')
}
</script>

<template>
  <VCardText class="py-0">
    <VExpansionPanels v-model="isIndexationExpanded">
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Indexation beginning of calendar year
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <PensionsCalcStrip :date-selection-disabled="true" />

          <PensionTableLayout>
            <template #header-label>
              DESCRIPTION
            </template>
            <template #header-left>
              <div :class="getStatusClass(leftColumnYear)">
                <VChip
                  v-if="shouldShowCertified(leftColumnYear, pensionStore.leftColumnDate)"
                  color="success"
                  size="small"
                >
                  Certified
                </VChip>
                <VChip
                  v-else
                  color="warning"
                  size="small"
                >
                  Certification pending
                </VChip>
                <div class="text-caption">
                  {{ leftColumnYear }}
                </div>
              </div>
            </template>
            <template #header-middle>
              <div :class="getStatusClass(middleColumnYear)">
                <VChip
                  v-if="shouldShowCertified(middleColumnYear, pensionStore.middleColumnDate)"
                  color="success"
                  size="small"
                >
                  Certified
                </VChip>
                <VChip
                  v-else
                  color="warning"
                  size="small"
                >
                  Certification pending
                </VChip>
                <div class="text-caption">
                  {{ middleColumnYear }}
                </div>
              </div>
            </template>
            <template #header-right>
              <div :class="getStatusClass(rightColumnYear)">
                <VChip
                  v-if="shouldShowCertified(rightColumnYear, pensionStore.rightColumnDate)"
                  color="success"
                  size="small"
                >
                  Certified
                </VChip>
                <VChip
                  v-else
                  color="warning"
                  size="small"
                >
                  Certification pending
                </VChip>
                <div class="text-caption">
                  {{ rightColumnYear }}
                </div>
              </div>
            </template>

            <PensionTableRow
              label="OP-TE"
              :is-alternate="true"
            >
              <template #left>
                {{ formatCurrency(getIndexationData(leftColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getIndexationData(middleColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
              <template #right>
                {{ formatCurrency(getIndexationData(rightColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="WP-TE">
              <template #left>
                {{ formatCurrency(getIndexationData(leftColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getIndexationData(middleColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getIndexationData(rightColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow
              label="ONP-TE"
              :is-alternate="true"
            >
              <template #left>
                {{ formatCurrency(getIndexationData(leftColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getIndexationData(middleColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getIndexationData(rightColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="AOP-TE">
              <template #left>
                {{ formatCurrency(getIndexationData(leftColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getIndexationData(middleColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getIndexationData(rightColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
            </PensionTableRow>
          </PensionTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title {
    min-height: 48px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
