<script setup lang="ts">
import PensionTableLayout from './PensionTableLayout.vue'
import PensionTableRow from './PensionTableRow.vue'
import PensionsCalcStrip from '@/components/participants/pensionCalculation/PensionsCalcStrip.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'
import PensionBaseHeader from '@/components/participants/pensionCalculation/PensionBaseHeader.vue'

const pensionStore = usePensionStore()

const {
  createExpansionState,
  getColumnYears,
  getStatusClass,
  formatCurrency,
  getColumnData,
  getCertificationStatusText,
} = usePensionBase()

// Use the shared expansion state function
const isPreviousYearExpanded = createExpansionState(true)

// Use the shared column years function
const { leftColumnYear, middleColumnYear, rightColumnYear } = getColumnYears()

// Function to get column data with the specific data type for this component
const getPensionInfoData = (year: number | null) => {
  if (year === null)
    return null
  const computedYear = year - 1

  return getColumnData(computedYear, 'certifiedPensionInfo')
}
</script>

<template>
  <PensionBaseHeader />
  <VCardText class="py-0">
    <VExpansionPanels
      v-model="isPreviousYearExpanded"
      class="mb-6"
    >
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Pensions end of previous calendar year
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <PensionsCalcStrip :date-selection-disabled="true" />

          <PensionTableLayout>
            <template #header-label>
              DESCRIPTION
            </template>
            <template #header-left>
              <div :class="getStatusClass(leftColumnYear)">
                <VChip
                  :color="getStatusClass(leftColumnYear).includes('green') ? 'success' : 'warning'"
                  size="small"
                >
                  {{ getCertificationStatusText(leftColumnYear) }}
                </VChip>
                <div class="text-caption">
                  {{ leftColumnYear }}
                </div>
              </div>
            </template>
            <template #header-middle>
              <div :class="getStatusClass(middleColumnYear)">
                <VChip
                  :color="getStatusClass(middleColumnYear).includes('green') ? 'success' : 'warning'"
                  size="small"
                >
                  {{ getCertificationStatusText(middleColumnYear) }}
                </VChip>
                <div class="text-caption">
                  {{ middleColumnYear }}
                </div>
              </div>
            </template>
            <template #header-right>
              <div :class="getStatusClass(rightColumnYear)">
                <VChip
                  :color="getStatusClass(rightColumnYear).includes('green') ? 'success' : 'warning'"
                  size="small"
                >
                  {{ getCertificationStatusText(rightColumnYear) }}
                </VChip>
                <div class="text-caption">
                  {{ rightColumnYear }}
                </div>
              </div>
            </template>
            <PensionTableRow
              label="Code"
              :is-alternate="true"
            >
              <template #left>
                {{ getPensionInfoData(leftColumnYear)?.code ?? 'N/A' }}
              </template>
              <template #middle>
                {{ getPensionInfoData(middleColumnYear)?.code ?? 'N/A' }}
              </template>
              <template #right>
                {{ getPensionInfoData(rightColumnYear)?.code ?? 'N/A' }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="OP-TE">
              <template #left>
                {{ formatCurrency(getPensionInfoData(leftColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionInfoData(middleColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionInfoData(rightColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow
              label="OP-TB"
              :is-alternate="true"
            >
              <template #left>
                {{ formatCurrency(getPensionInfoData(leftColumnYear)?.attainableGrossAnnualOldAgePension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionInfoData(middleColumnYear)?.attainableGrossAnnualOldAgePension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionInfoData(rightColumnYear)?.attainableGrossAnnualOldAgePension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="WP-TE">
              <template #left>
                {{ formatCurrency(getPensionInfoData(leftColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionInfoData(middleColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionInfoData(rightColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow
              label="ONP-TE"
              :is-alternate="true"
            >
              <template #left>
                {{ formatCurrency(getPensionInfoData(leftColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionInfoData(middleColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionInfoData(rightColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="AOP-TE">
              <template #left>
                {{ formatCurrency(getPensionInfoData(leftColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionInfoData(middleColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionInfoData(rightColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
            </PensionTableRow>
          </PensionTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
