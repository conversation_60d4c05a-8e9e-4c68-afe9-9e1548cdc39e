<script setup lang="ts">
import PensionTableLayout from './PensionTableLayout.vue'
import PensionTableRow from './PensionTableRow.vue'
import PensionsCalcStrip from '@/components/participants/pensionCalculation/PensionsCalcStrip.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'

const pensionStore = usePensionStore()

const {
  shouldShowCertified,
  hasCertifiedData,
  createExpansionState,
  getColumnYears,
  getStatusClass,
  formatCurrency,
  getColumnData,
} = usePensionBase()

// Use the shared expansion state function
const isPensionsPerReferenceExpanded = createExpansionState(true)

// Use the shared column years function
const { leftColumnYear, middleColumnYear, rightColumnYear } = getColumnYears()

// Function to get column data with the specific data type for this component
const getPensionReferenceData = (year: number | null) => {
  return getColumnData(year, 'certifiedPensionInfo')
}
</script>

<template>
  <VCardText class="py-0">
    <VExpansionPanels
      v-model="isPensionsPerReferenceExpanded"
      class="mt-6"
    >
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Pensions as per reference date
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <PensionsCalcStrip />

          <PensionTableLayout>
            <template #header-label>
              DESCRIPTION
            </template>
            <template #header-left>
              <div :class="getStatusClass(leftColumnYear)">
                <VChip
                  v-if="shouldShowCertified(leftColumnYear, pensionStore.leftColumnDate)"
                  color="success"
                  size="small"
                >
                  Certified
                </VChip>
                <VChip
                  v-else
                  color="warning"
                  size="small"
                >
                  Certification pending
                </VChip>
                <div class="text-caption">
                  {{ leftColumnYear }}
                </div>
              </div>
            </template>
            <template #header-middle>
              <div :class="getStatusClass(middleColumnYear)">
                <VChip
                  v-if="shouldShowCertified(middleColumnYear, pensionStore.middleColumnDate)"
                  color="success"
                  size="small"
                >
                  Certified
                </VChip>
                <VChip
                  v-else
                  color="warning"
                  size="small"
                >
                  Certification pending
                </VChip>
                <div class="text-caption">
                  {{ middleColumnYear }}
                </div>
              </div>
            </template>
            <template #header-right>
              <div :class="getStatusClass(rightColumnYear)">
                <VChip
                  v-if="shouldShowCertified(rightColumnYear, pensionStore.rightColumnDate)"
                  color="success"
                  size="small"
                >
                  Certified
                </VChip>
                <VChip
                  v-else
                  color="warning"
                  size="small"
                >
                  Certification pending
                </VChip>
                <div class="text-caption">
                  {{ rightColumnYear }}
                </div>
              </div>
            </template>

            <PensionTableRow
              label="Code"
              :is-alternate="true"
            >
              <template #left>
                {{ getPensionReferenceData(leftColumnYear)?.code ?? 'N/A' }}
              </template>
              <template #middle>
                {{ getPensionReferenceData(middleColumnYear)?.code ?? 'N/A' }}
              </template>
              <template #right>
                {{ getPensionReferenceData(rightColumnYear)?.code ?? 'N/A' }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="OP-TE">
              <template #left>
                {{ formatCurrency(getPensionReferenceData(leftColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionReferenceData(middleColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionReferenceData(rightColumnYear)?.accruedGrossAnnualOldAgePension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow
              label="OP-TB"
              :is-alternate="true"
            >
              <template #left>
                {{ formatCurrency(getPensionReferenceData(leftColumnYear)?.attainableGrossAnnualOldAgePension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionReferenceData(middleColumnYear)?.attainableGrossAnnualOldAgePension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionReferenceData(rightColumnYear)?.attainableGrossAnnualOldAgePension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="WP-TE">
              <template #left>
                {{ formatCurrency(getPensionReferenceData(leftColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionReferenceData(middleColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionReferenceData(rightColumnYear)?.accruedGrossAnnualPartnersPension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow
              label="ONP-TE"
              :is-alternate="true"
            >
              <template #left>
                {{ formatCurrency(getPensionReferenceData(leftColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionReferenceData(middleColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionReferenceData(rightColumnYear)?.accruedGrossAnnualSinglesPension) }}
              </template>
            </PensionTableRow>

            <PensionTableRow label="AOP-TE">
              <template #left>
                {{ formatCurrency(getPensionReferenceData(leftColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
              <template #middle>
                {{ formatCurrency(getPensionReferenceData(middleColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
              <template #right>
                {{ formatCurrency(getPensionReferenceData(rightColumnYear)?.grossAnnualDisabilityPension) }}
              </template>
            </PensionTableRow>
          </PensionTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
