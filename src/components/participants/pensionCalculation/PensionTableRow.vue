<script setup lang="ts">
defineProps({
  label: {
    type: String,
    required: true,
  },
  isAlternate: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <tr :class="{ 'bg-grey-lighten-5': isAlternate }">
    <td class="font-weight-medium">
      {{ label }}
    </td>
    <td class="text-right">
      <slot name="left" />
    </td>
    <td class="text-right">
      <slot name="middle" />
    </td>
    <td class="text-right">
      <slot name="right" />
    </td>
  </tr>
</template>
