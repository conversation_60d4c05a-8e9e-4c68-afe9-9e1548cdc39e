<script setup lang="ts">
import avatar from '../../../assets/images/avatars/avatar-0.png'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:information'])

const { state: { participantDetails, loadingParticipant } } = useParticipants()

const appStore = useAppStore()

const editDialog = ref(false)
const selectedField = ref({ field: '', value: '', id: '', entityType: '', name: '', typename: '' })

const openEditDialog = (item: any) => {
  if (!props.editable)
    return

  // Show warning for disabled fields
  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  }

  selectedField.value = item
  editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
}

const updateField = (updatedValue: string) => {
  closeEditDialog()
}

const getFieldValue = (fieldName: string) => {
  const field = participantDetails.value.find((item: any) => item.field === fieldName)

  return field ? field.value : ''
}

const fullName = computed(() => {
  const firstName = getFieldValue('firstName')
  const lastName = getFieldValue('lastName')

  return `${firstName} ${lastName}`
})

const getIconForField = (fieldName: string) => {
  const iconMap: Record<string, string> = {
    phone: 'tabler-phone',
    birthDate: 'tabler-calendar-event',
    firstName: 'tabler-user-circle',
    lastName: 'tabler-user-circle',
    email: 'tabler-mail',
    maritalStatus: 'tabler-heart',
    Address: 'tabler-home',
    disabled: 'tabler-alert-triangle',
  }

  return iconMap[fieldName] || 'tabler-info-circle'
}

const formatFieldValue = (field: any) => {
  if (field.field.toLowerCase().includes('date') && field.value) {
    try {
      return new Date(field.value).toLocaleDateString()
    }
    catch (e) {
      return field.value
    }
  }

  return field.value
}
</script>

<template>
  <VSkeletonLoader
    v-if="loadingParticipant"
    class="mx-auto border py-6"
    type="list-item-avatar-three-line"
  />
  <VCard
    v-else
    class="basic-information-container"
  >
    <VCardText>
      <VRow>
        <VCol cols="9">
          <div class="d-flex align-bottom flex-sm-row flex-column justify-center gap-x-5">
            <!-- Avatar section -->
            <div class="d-flex">
              <VAvatar
                size="100"
                :image="avatar"
                class="mx-auto my-auto"
              />
            </div>

            <div class="w-100 mt-8 pt-4 mt-sm-0">
              <h6 class="text-h4 text-center text-sm-start font-weight-medium mb-3">
                {{ fullName }}
              </h6>

              <div class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4">
                <div class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4">
                  <span
                    v-for="(field, index) in participantDetails.filter(item => !['pendingChanges', 'birthDay', 'birthMonth', 'birthYear', 'id', 'participantId', 'partnerInfo', 'children', 'personalInfo', 'address', 'certificationRejectReason'].includes(item.field))"
                    :key="index"
                    class="d-flex align-center info-item"
                    :class="[{ 'cursor-pointer': props.editable && !field.disabled, 'cursor-default': !props.editable || field.disabled }]"
                    @click="openEditDialog(field)"
                  >
                    <VIcon
                      size="20"
                      :icon="getIconForField(field.field)"
                      class="me-1"
                    />

                    <span class="text-body-1 mr-1">
                      <template v-if="field.field === 'status'">
                        <VChip
                          class="ml-2 font-weight-bold success"
                          label
                          color="success"
                          text-color="white"
                        >
                          {{ formatFieldValue(field) }}
                        </VChip>
                      </template>
                      <template v-else>
                        {{ formatFieldValue(field) }}
                      </template>
                    </span>

                    <template v-if="props.editable">
                      <VIcon
                        v-if="field.disabled"
                        size="16"
                        icon="tabler-alert-triangle"
                        class="edit-icon ms-1 ml-4"
                        color="error"
                      />
                      <VIcon
                        v-else
                        size="16"
                        icon="tabler-edit"
                        class="edit-icon ms-1 ml-4"
                        color="primary"
                      />
                    </template>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </VCol>

        <VCol
          cols="3"
          class="d-flex justify-end"
        >
          <PensionCode :editable="props.editable" />
        </VCol>
      </VRow>
    </VCardText>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.name"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      :participant-name="fullName"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />
  </VCard>
</template>

<style scoped>
  .basic-information-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: white;
  }

  .info-item {
    position: relative;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .edit-icon {
    opacity: 1;
    transition: opacity 0.2s;
  }
</style>
