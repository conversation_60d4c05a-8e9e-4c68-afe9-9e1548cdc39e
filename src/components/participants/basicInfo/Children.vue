<script setup lang="ts">
import { computed, ref } from 'vue'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
})

const { state: { participantChildrenInfo, loadingParticipant } } = useParticipants()
const appStore = useAppStore()

const editDialog = ref(false)

const selectedField = ref({
  field: '',
  value: '',
  id: '',
  childIndex: 0,
  typename: '',
})

const filteredChildren = computed(() => {
  return participantChildrenInfo.value.map((child: any) =>
    child.filter((item: any) => !['id', 'certificationRejectReason'].includes(item.field)),
  )
})

const openEditDialog = (item: any, childIndex: number) => {
  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  }
  selectedField.value = {
    field: item.field,
    value: item.value,
    id: item.id,
    childIndex,
    typename: item.typename,
  }
  editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
}

const updateField = (newValue: any) => {
  closeEditDialog()
}
</script>

<template>
  <div class="children-container">
    <div class="header-container">
      <h2>Children</h2>
    </div>

    <VSkeletonLoader
      v-if="loadingParticipant"
      class="mx-auto border py-6"
      type="table"
    />
    <div v-else>
      <div v-if="filteredChildren.length > 0">
        <div
          v-for="(child, childIndex) in filteredChildren"
          :key="child[0]?.id"
          class="child-section"
        >
          <div class="child-header">
            <h3>Child {{ childIndex + 1 }}</h3>
          </div>

          <VDataTable
            :headers="[
              { title: 'Field', key: 'name', width: '200px' },
              { title: 'Value', key: 'value' },
              { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
            ]"
            :items="child"
            hide-default-footer
            class="elevation-0"
            density="compact"
          >
            <template #item.actions="{ item }">
              <VBtn
                v-if="props.editable"
                icon
                size="small"
                variant="text"
                color="primary"
                @click="openEditDialog(item, childIndex)"
              >
                <VIcon
                  v-if="item?.disabled"
                  size="16"
                  icon="tabler-alert-triangle"
                  class="edit-icon"
                  color="error"
                />
                <VIcon
                  v-else
                  size="16"
                  icon="tabler-edit"
                  class="edit-icon"
                  color="primary"
                />
              </VBtn>
            </template>
          </VDataTable>
        </div>
      </div>
      <div
        v-else
        class="no-data-message"
      >
        No children information available
      </div>
    </div>

    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />
  </div>
</template>

<style scoped>
  .children-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .child-section {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .child-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
  }

  .no-data-message {
    padding: 16px;
    text-align: center;
    color: #757575;
    font-style: italic;
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>
