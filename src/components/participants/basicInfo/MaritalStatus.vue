<script setup lang="ts">
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:maritalStatus'])

const { state: { participantPartnerInfo } } = useParticipants()

const filteredList = computed(() => {
  return participantPartnerInfo.value.filter(item => item.field !== 'id' && item.field !== 'isCurrent' && item.field !== 'startDate' && item.field !== 'certificationRejectReason')
})

const appStore = useAppStore()

const editDialog = ref(false)
const selectedField = ref({ field: '', value: '', id: '' })

const openEditDialog = (item: any) => {
  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  };
  selectedField.value = item
  editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
}
</script>

<template>
  <div class="marital-status-container">
    <div class="header-container">
      <h2>Marital Status</h2>
    </div>

    <div>
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="filteredList"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.actions="{ item }">
          <VBtn
            v-if="props.editable"
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <VIcon
              v-if="item?.disabled "
              size="16"
              icon="tabler-alert-triangle"
              class="edit-icon ms-1 ml-4"
              color="error"
            />
            <VIcon
              v-else
              size="16"
              icon="tabler-edit"
              class="edit-icon ms-1 ml-4"
              color="primary"
            />
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="participantPartnerInfo[0].typename"
      :year="2025"
      @close="closeEditDialog"
    />
  </div>
</template>

<style scoped>
  .marital-status-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
</style>
