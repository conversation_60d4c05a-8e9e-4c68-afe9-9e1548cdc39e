<script setup lang="ts">
import { nextTick } from 'vue'
import AppDateTimePicker from '@/@core/components/app-form-elements/AppDateTimePicker.vue'
import { getPensionCodeDescription, pensionCodes } from '@/utils/excelUtils'
import { useAppStore } from '@/stores/app/appStore'

interface Partner {
  firstName: string
  lastName: string
  dateOfBirth: string
  startDate: string
  endDate: string
  isCurrent: boolean
  isDeceased: boolean
}

interface Child {
  firstName: string
  lastName: string
  dateOfBirth: string
  isOrphan: boolean
  isStudying: boolean
}

interface Address {
  street: string
  houseNumber: string
  postalCode: string
  city: string
  state: string
  country: string
}

interface PersonalInfo {
  firstName: string
  lastName: string
  sex: string
  email: string
  phone: string
  maritalStatus: string
  birthDate: string
  partners: Partner[]
  children: Child[]
  address: Address
}

interface SalaryEntry {
  year: number
  amount: number | null
  partTimePercentage: number | null
}

interface EmploymentInfo {
  employeeId: string
  department: string
  position: string
  startDate: string
  status: string
  salaryEntries: SalaryEntry[]
}

interface PensionInfo {
  code: number | null
  codeDescription: string
  codeEffectiveDate: string
  pensionBase: number | null
}



interface FormData {
  personalInfo: PersonalInfo
  employmentInfo: EmploymentInfo
  pensionInfo: PensionInfo
}

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['submit', 'cancel'])

const form = ref()
const isValid = ref(false)
const appStore = useAppStore()

const maritalStatusOptions = [
  'Single',
  'Married',
  'Divorced',
  'Widowed',
  'Separated',
]



const getPensionCodeIcon = (code: number): string => {
  const iconMap: Record<number, string> = {
    10: 'tabler-bolt',
    11: 'tabler-disabled',
    30: 'tabler-pause',
    40: 'tabler-rocket',
    50: 'tabler-heart',
    55: 'tabler-baby-bottle',
    70: 'tabler-cancel',
  }

  return iconMap[code] || 'tabler-user'
}

const getPensionCodeColor = (code: number): string => {
  const colorMap: Record<number, string> = {
    10: 'success',
    11: 'warning',
    30: 'secondary',
    40: 'primary',
    50: 'info',
    55: 'purple',
    70: 'error',
  }

  return colorMap[code] || 'grey'
}

const handlePensionCodeChange = (code: number) => {
  const description = getPensionCodeDescription(code)
  formData.value.pensionInfo.codeDescription = description
  formData.value.employmentInfo.status = description
}

const formData = ref<FormData>({
  personalInfo: {
    firstName: '',
    lastName: '',
    sex: '',
    email: '',
    phone: '',
    maritalStatus: '',
    birthDate: '',
    partners: [],
    children: [],
    address: {
      street: '',
      houseNumber: '',
      postalCode: '',
      city: '',
      state: '',
      country: '',
    },
  },
  employmentInfo: {
    employeeId: '',
    department: '',
    position: '',
    startDate: '',
    status: 'Active',
    salaryEntries: [
      {
        year: new Date().getFullYear(),
        amount: null,
        partTimePercentage: 100,
      },
    ],
  },
  pensionInfo: {
    code: null,
    codeDescription: '',
    codeEffectiveDate: new Date().toISOString(),
    pensionBase: null,
  },
})

const isPensionCode50Or55 = computed(() => {
  const code = formData.value.pensionInfo.code

  return code === 50 || code === 55
})

// Watch for changes in partners' isCurrent status to ensure only one is selected
watch(() => formData.value.personalInfo.partners.map(p => p.isCurrent), (currentStates, oldStates) => {
  const newCurrentPartnerIndex = currentStates.findIndex((state, index) => state && (!oldStates || state !== oldStates[index]))

  if (newCurrentPartnerIndex !== -1) {
    // Unselect other partners
    formData.value.personalInfo.partners.forEach((partner, index) => {
      if (index !== newCurrentPartnerIndex)
        partner.isCurrent = false
    })

    // Unselect deceased if current is selected
    if (formData.value.personalInfo.partners[newCurrentPartnerIndex].isDeceased)
      formData.value.personalInfo.partners[newCurrentPartnerIndex].isDeceased = false
  }
})

// Watch for changes in partners' isDeceased status
watch(() => formData.value.personalInfo.partners.map(p => p.isDeceased), (deceasedStates, oldStates) => {
  deceasedStates.forEach((isDeceased, index) => {
    if (isDeceased && (!oldStates || isDeceased !== oldStates[index])) {
      // A partner was marked as deceased, unselect current
      const partner = formData.value.personalInfo.partners[index]
      if (partner && partner.isCurrent) {
        partner.isCurrent = false
      }
    }
  })
})

// Watch for marital status changes
watch(() => formData.value.personalInfo.maritalStatus, newStatus => {
  if (newStatus === 'Single')
    formData.value.personalInfo.partners = []
})

// Watch for startDate changes to update salary entry year
watch(() => formData.value.employmentInfo.startDate, newStartDate => {
  if (newStartDate && formData.value.employmentInfo.salaryEntries.length > 0) {
    const startYear = new Date(newStartDate).getFullYear()

    formData.value.employmentInfo.salaryEntries[0].year = startYear
  }
})

const rules = {
  required: (v: any) => !!v || 'This field is required',
  email: (v: string) => {
    const pattern = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/

    return !v || pattern.test(v) || 'Invalid email format'
  },
  phone: (v: string) => {
    const pattern = /^\+?[\d\s\-()]+$/

    return !v || pattern.test(v) || 'Invalid phone format'
  },
  positiveNumber: (v: number) => v > 0 || 'Must be a positive number',
  percentage: (v: number) => (v >= 0 && v <= 100) || 'Must be between 0 and 100',
}

// Partner date validation functions
const validatePartnerDateRange = (partner: Partner): string | true => {
  if (!partner.startDate)
    return true
  if (!partner.endDate)
    return true

  const startDate = new Date(partner.startDate)
  const endDate = new Date(partner.endDate)

  if (startDate > endDate)
    return 'Start date cannot be after end date'

  return true
}

const validatePartnerOverlaps = (currentPartner: Partner, currentIndex: number): string | true => {
  if (!currentPartner.startDate) return true
  
  const currentStart = new Date(currentPartner.startDate)
  const currentEnd = currentPartner.endDate ? new Date(currentPartner.endDate) : null
  
  for (let i = 0; i < formData.value.personalInfo.partners.length; i++) {
    if (i === currentIndex) continue // Skip self
    
    const otherPartner = formData.value.personalInfo.partners[i]
    if (!otherPartner.startDate) continue
    
    const otherStart = new Date(otherPartner.startDate)
    const otherEnd = otherPartner.endDate ? new Date(otherPartner.endDate) : null
    
    // Check for overlaps using the same logic as backend
    if (datesOverlap(currentStart, currentEnd, otherStart, otherEnd)) {
      const otherPartnerName = otherPartner.firstName && otherPartner.lastName 
        ? `${otherPartner.firstName} ${otherPartner.lastName}` 
        : `Partner ${i + 1}`
      const otherEndStr = otherEnd ? otherEnd.toDateString() : 'ongoing'
      return `Dates overlap with ${otherPartnerName} (${otherStart.toDateString()} - ${otherEndStr})`
    }
  }
  
  return true
}

const datesOverlap = (
  start1: Date,
  end1: Date | null,
  start2: Date,
  end2: Date | null,
): boolean => {
  // If either period has no end date, treat it as ongoing (infinite end)
  const effectiveEnd1 = end1 || new Date('9999-12-31')
  const effectiveEnd2 = end2 || new Date('9999-12-31')

  // Two periods overlap if:
  // start1 <= effectiveEnd2 AND start2 <= effectiveEnd1
  return start1 <= effectiveEnd2 && start2 <= effectiveEnd1
}

// Create validation rules for partner dates
const createPartnerDateRules = (partner: Partner, index: number) => ({
  startDate: [
    rules.required,
    () => validatePartnerDateRange(partner),
    () => validatePartnerOverlaps(partner, index),
  ],
  endDate: [
    () => validatePartnerDateRange(partner),
    () => validatePartnerOverlaps(partner, index),
  ],
})

const addPartner = () => {
  formData.value.personalInfo.partners.push({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    startDate: '',
    endDate: '',
    isCurrent: false,
    isDeceased: false,
  })
}

const removePartner = (index: number) => {
  formData.value.personalInfo.partners.splice(index, 1)
}

const addChild = () => {
  formData.value.personalInfo.children.push({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    isOrphan: false,
    isStudying: false,
  })
}

const removeChild = (index: number) => {
  formData.value.personalInfo.children.splice(index, 1)
}

// Salary entries management
const addSalaryEntry = () => {
  const currentYear = new Date().getFullYear()

  const startYear = formData.value.employmentInfo.startDate
    ? new Date(formData.value.employmentInfo.startDate).getFullYear()
    : currentYear

  formData.value.employmentInfo.salaryEntries.push({
    year: startYear + formData.value.employmentInfo.salaryEntries.length,
    amount: null,
    partTimePercentage: 100,
  })
}

const removeSalaryEntry = (index: number) => {
  if (formData.value.employmentInfo.salaryEntries.length > 1)
    formData.value.employmentInfo.salaryEntries.splice(index, 1)
}

const handleSubmit = async () => {
  // First validate partner dates manually to show specific error messages
  let hasPartnerErrors = false

  for (let i = 0; i < formData.value.personalInfo.partners.length; i++) {
    const partner = formData.value.personalInfo.partners[i]

    // Check date range validation
    const dateRangeResult = validatePartnerDateRange(partner)
    if (dateRangeResult !== true) {
      appStore.showSnack(`Partner ${i + 1}: ${dateRangeResult}`, 'error')
      hasPartnerErrors = true
    }

    // Check overlap validation
    const overlapResult = validatePartnerOverlaps(partner, i)
    if (overlapResult !== true) {
      appStore.showSnack(`Partner ${i + 1}: ${overlapResult}`)
      hasPartnerErrors = true
    }
  }

  if (hasPartnerErrors)
    return

  const { valid } = await form.value.validate()

  if (valid) {
    try {
      emit('submit', formData.value)
    }
    catch (error) {
      // Handle backend validation errors
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      if (errorMessage.includes('overlap'))
        appStore.showSnack(errorMessage)
      else
        appStore.showSnack('Failed to create participant')
    }
    finally {
      // Reset form or handle errors
      form.value.resetValidation()
    }
  }
  else {
    appStore.showSnack('Please fix the validation errors before submitting')
  }
}

// Method to populate form from imported data
const populateForm = async (importedData: any) => {
  // Populate personal info including multiple partners and children
  if (importedData.personalInfo) {
    formData.value.personalInfo = {
      ...formData.value.personalInfo,
      ...importedData.personalInfo,
    }
  }

  // Populate employment info including multiple salary entries
  if (importedData.employmentInfo) {
    formData.value.employmentInfo = {
      ...formData.value.employmentInfo,
      ...importedData.employmentInfo,
    }
  }

  // Populate pension info and ensure description is set
  if (importedData.pensionInfo) {
    formData.value.pensionInfo = {
      ...formData.value.pensionInfo,
      ...importedData.pensionInfo,
    }

    // Ensure pension code description is set
    if (formData.value.pensionInfo.code && !formData.value.pensionInfo.codeDescription)
      formData.value.pensionInfo.codeDescription = getPensionCodeDescription(formData.value.pensionInfo.code)
  }

  // Trigger form validation after populating data
  await nextTick() // Wait for DOM updates
  if (form.value)
    await form.value.validate()
}

// Expose methods for parent component
defineExpose({
  populateForm,
})
</script>

<template>
  <VForm
    ref="form"
    v-model="isValid"
    @submit.prevent="handleSubmit"
  >
    <VRow>
      <VCol cols="12">
        <VCard class="mb-6">
          <VCardTitle class="text-h6">
            Pension Information
          </VCardTitle>
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="formData.pensionInfo.code"
                  label="Pension Code *"
                  :items="pensionCodes"
                  item-title="code"
                  item-value="code"
                  variant="outlined"
                  density="compact"
                  :rules="[rules.required]"
                  @update:model-value="handlePensionCodeChange"
                >
                  <template #item="{ props, item }">
                    <VListItem
                      v-bind="props"
                      :title="`${item.raw.code} - ${item.raw.description}`"
                      :subtitle="item.raw.impact"
                    >
                      <template #prepend>
                        <VIcon
                          :icon="getPensionCodeIcon(item.raw.code)"
                          :color="getPensionCodeColor(item.raw.code)"
                        />
                      </template>
                    </VListItem>
                  </template>
                </VSelect>
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.pensionInfo.codeDescription"
                  label="Code Description"
                  variant="outlined"
                  density="compact"
                  readonly
                />
              </VCol>
              <!--              <VCol -->
              <!--                cols="12" -->
              <!--                md="6" -->
              <!--              > -->
              <!--                <AppDateTimePicker -->
              <!--                  v-model="formData.pensionInfo.codeEffectiveDate" -->
              <!--                  label="Code Effective Date *" -->
              <!--                  density="compact" -->
              <!--                  :rules="[rules.required]" -->
              <!--                  :config="{ maxDate: 'today' }" -->
              <!--                /> -->
              <!--              </VCol> -->
            </VRow>
          </VCardText>
        </VCard>
      </VCol>
      <!-- Basic Information Section -->
      <VCol cols="12">
        <VCard class="mb-6">
          <VCardTitle class="text-h6">
            Basic Information
          </VCardTitle>
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.firstName"
                  label="First Name *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.lastName"
                  label="Last Name *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="formData.personalInfo.sex"
                  label="Sex *"
                  :items="['Male', 'Female']"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.email"
                  label="Email *"
                  :rules="[rules.required, rules.email]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.phone"
                  label="Phone"
                  :rules="[rules.phone]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="formData.personalInfo.maritalStatus"
                  label="Marital Status *"
                  :items="maritalStatusOptions"
                  variant="outlined"
                  density="compact"
                  :rules="[rules.required]"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppDateTimePicker
                  v-model="formData.personalInfo.birthDate"
                  label="Date of birth *"
                  :rules="[rules.required]"
                  :config="{ maxDate: 'today' }"
                  density="compact"
                />
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Partners Section -->
      <VCol
        v-if="formData.personalInfo.maritalStatus !== 'Single' && !isPensionCode50Or55"
        cols="12"
      >
        <VCard class="mb-6">
          <VCardTitle class="d-flex justify-space-between align-center">
            <span class="text-h6">Partners</span>
            <VBtn
              color="primary"
              variant="tonal"
              size="small"
              prepend-icon="tabler-plus"
              @click="addPartner"
            >
              Add Partner
            </VBtn>
          </VCardTitle>
          <VCardText>
            <VRow
              v-for="(partner, index) in formData.personalInfo.partners"
              :key="index"
            >
              <VCol cols="12">
                <VCard
                  variant="outlined"
                  class="mb-4"
                >
                  <VCardText>
                    <div class="d-flex justify-space-between align-center mb-4">
                      <span class="text-subtitle-1">Partner {{ index + 1 }}</span>
                      <VBtn
                        color="error"
                        variant="text"
                        icon="tabler-trash"
                        @click="removePartner(index)"
                      />
                    </div>
                    <VRow>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VTextField
                          v-model="partner.firstName"
                          label="First Name *"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VTextField
                          v-model="partner.lastName"
                          label="Last Name *"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <AppDateTimePicker
                          v-model="partner.dateOfBirth"
                          label="Date of Birth *"
                          :rules="[rules.required]"
                          :config="{ maxDate: 'today' }"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <AppDateTimePicker
                          v-model="partner.startDate"
                          label="Start Date *"
                          :rules="createPartnerDateRules(partner, index).startDate"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <AppDateTimePicker
                          v-model="partner.endDate"
                          label="End Date"
                          :rules="createPartnerDateRules(partner, index).endDate"
                          density="compact"
                        />
                      </VCol>                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VCheckbox
                          v-model="partner.isCurrent"
                          label="Current Partner"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VCheckbox
                          v-model="partner.isDeceased"
                          label="Deceased"
                        />
                      </VCol>
                    </VRow>
                  </VCardText>
                </VCard>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Children Section -->
      <VCol
        v-if="!isPensionCode50Or55"
        cols="12"
      >
        <VCard class="mb-6">
          <VCardTitle class="d-flex justify-space-between align-center">
            <span class="text-h6">Children</span>
            <VBtn
              color="primary"
              size="small"
              variant="tonal"
              prepend-icon="tabler-plus"
              @click="addChild"
            >
              Add Child
            </VBtn>
          </VCardTitle>
          <VCardText>
            <VRow
              v-for="(child, index) in formData.personalInfo.children"
              :key="index"
            >
              <VCol cols="12">
                <VCard
                  variant="outlined"
                  class="mb-4"
                >
                  <VCardText>
                    <div class="d-flex justify-space-between align-center mb-4">
                      <span class="text-subtitle-1">Child {{ index + 1 }}</span>
                      <VBtn
                        color="error"
                        variant="text"
                        icon="tabler-trash"
                        @click="removeChild(index)"
                      />
                    </div>
                    <VRow>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VTextField
                          v-model="child.firstName"
                          label="First Name *"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VTextField
                          v-model="child.lastName"
                          label="Last Name *"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <AppDateTimePicker
                          v-model="child.dateOfBirth"
                          label="Date of Birth *"
                          :rules="[rules.required]"
                          :config="{ maxDate: 'today' }"
                          density="compact"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VCheckbox
                          v-model="child.isOrphan"
                          label="Orphan"
                        />
                      </VCol>
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VCheckbox
                          v-model="child.isStudying"
                          label="Currently Studying"
                        />
                      </VCol>
                    </VRow>
                  </VCardText>
                </VCard>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Address Section -->
      <VCol cols="12">
        <VCard class="mb-6">
          <VCardTitle class="text-h6">
            Address
          </VCardTitle>
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.address.street"
                  label="Street *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.address.houseNumber"
                  label="House Number *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.address.postalCode"
                  label="Postal Code *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.address.city"
                  label="City *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.address.state"
                  label="State"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.personalInfo.address.country"
                  label="Country *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Employment Information Section -->
      <VCol
        v-if="!isPensionCode50Or55"
        cols="12"
      >
        <VCard class="mb-6">
          <VCardTitle class="text-h6">
            Employment Information
          </VCardTitle>
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.employmentInfo.employeeId"
                  label="Employee ID *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.employmentInfo.department"
                  label="Department *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="formData.employmentInfo.position"
                  label="Position *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <AppDateTimePicker
                  v-model="formData.employmentInfo.startDate"
                  label="Start Date *"
                  :rules="[rules.required]"
                  density="compact"
                />
              </VCol>
              <!--              <VCol -->
              <!--                cols="12" -->
              <!--                md="6" -->
              <!--              > -->
              <!--                <VSelect -->
              <!--                  :disabled="true" -->
              <!--                  v-model="formData.employmentInfo.status" -->
              <!--                  label="Status *" -->
              <!--                  variant="outlined" -->
              <!--                  density="compact" -->
              <!--                  hint="Status is set from pension code." -->
              <!--                  persistent-hint -->
              <!--                /> -->
              <!--              </VCol> -->
            </VRow>

            <!-- Salary Entries Section -->
            <VRow class="mt-4">
              <VCol cols="12">
                <VCard variant="outlined">
                  <VCardTitle class="text-subtitle-1 d-flex align-center justify-space-between">
                    <span>Salary Entries</span>
                    <VBtn
                      size="small"
                      color="primary"
                      variant="outlined"
                      prepend-icon="tabler-plus"
                      @click="addSalaryEntry"
                    >
                      Add Entry
                    </VBtn>
                  </VCardTitle>
                  <VCardText>
                    <div
                      v-for="(entry, index) in formData.employmentInfo.salaryEntries"
                      :key="index"
                      class="mb-4"
                    >
                      <VRow>
                        <VCol
                          cols="12"
                          md="4"
                        >
                          <VTextField
                            v-model.number="entry.year"
                            label="Year *"
                            type="number"
                            :rules="[rules.required, rules.positiveNumber]"
                            variant="outlined"
                            density="compact"
                          />
                        </VCol>
                        <VCol
                          cols="12"
                          md="4"
                        >
                          <VTextField
                            v-model.number="entry.amount"
                            label="Salary Amount *"
                            type="number"
                            :persistent-hint="true"
                            hint="Fulltime Monthly Salary"
                            :rules="[rules.required, rules.positiveNumber]"
                            variant="outlined"
                            density="compact"
                            prefix="Afl."
                          />
                        </VCol>
                        <VCol
                          cols="12"
                          md="3"
                        >
                          <VTextField
                            v-model.number="entry.partTimePercentage"
                            label="Part-time % *"
                            type="number"
                            :rules="[rules.required, rules.percentage]"
                            variant="outlined"
                            density="compact"
                            suffix="%"
                          />
                        </VCol>
                        <VCol
                          cols="12"
                          md="1"
                          class="d-flex align-center"
                        >
                          <VBtn
                            v-if="formData.employmentInfo.salaryEntries.length > 1"
                            icon
                            size="small"
                            color="error"
                            variant="text"
                            @click="removeSalaryEntry(index)"
                          >
                            <VIcon icon="tabler-trash" />
                          </VBtn>
                        </VCol>
                      </VRow>
                      <VDivider
                        v-if="index < formData.employmentInfo.salaryEntries.length - 1"
                        class="mt-2"
                      />
                    </div>
                  </VCardText>
                </VCard>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Form Actions -->
      <VCol
        cols="12"
        class="d-flex justify-end gap-4"
      >
        <VBtn
          variant="outlined"
          color="primary"
          :disabled="props.loading"
          @click="$emit('cancel')"
        >
          Cancel
        </VBtn>
        <VBtn
          type="submit"
          color="primary"
          :loading="props.loading"
          :disabled="!isValid || props.loading"
        >
          Create Participant
        </VBtn>
      </VCol>
    </VRow>
  </VForm>
</template>

<style scoped>
.v-row {
  margin: 0;
}
</style>
