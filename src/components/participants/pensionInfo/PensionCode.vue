<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits<{
  'code-changed': [{ code: number; description: string }]
}>()

const { state: { participantPensionInfo } } = useParticipants()
const appStore = useAppStore()

const router = useRouter()

const participantId = computed(() => {
  return router.currentRoute.value.params.id as string
})

interface InfoData {
  code?: number | null
  currentDescription?: string | null
  pendingChanges?: string[]
  codeDescription?: string | null
  id?: string
  previousCode?: number | null
  previousCodeEffectiveDate?: string | null
}

const pensionInfo = computed(() => {
  if (!participantPensionInfo.value)
    return {}
  const info: InfoData = participantPensionInfo.value

  return {
    id: info.id,
    code: info.code,
    previousCode: info.previousCode,
    previousCodeEffectiveDate: info.previousCodeEffectiveDate,
    codeDescription: info.codeDescription,
    hasPendingChanges: info.pendingChanges?.includes('code'),
  }
})

const dialogOpen = ref(false)

const openDialog = () => {
  if (!props.editable) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  }
  if (pensionInfo.value?.hasPendingChanges) {
    appStore.showSnack(
      'Please save or discard pending changes before editing the pension code',
    )

    return
  }
  dialogOpen.value = true
}

const handleCodeChanged = (data: { code: number; description: string }) => {
  emit('code-changed', data)
  dialogOpen.value = false
}

const getCodeDisplay = (code?: number | null) => {
  return code ? `${code}` : 'No Code'
}

const getStatusColor = (code?: number | null) => {
  if (!code)
    return 'grey'

  const colorMap: Record<number, string> = {
    10: 'success', // Active - green
    11: 'warning', // Disabled - orange
    30: 'secondary', // Inactive - grey
    40: 'primary', // Retired - blue
    50: 'info', // Partner pension - light blue
    55: 'secondary', // Orphan pension - purple
    70: 'error', // No longer entitled - red
  }

  return colorMap[code] || 'grey'
}

const getStatusIcon = (code?: number | null) => {
  if (!code)
    return 'tabler-help-circle'

  const iconMap: Record<number, string> = {
    10: 'tabler-bolt', // Active - lightning bolt
    11: 'tabler-disabled', // Disabled - accessibility
    30: 'tabler-pause', // Inactive - pause
    40: 'tabler-rocket', // Retired - rocket (enjoying retirement)
    50: 'tabler-heart', // Partner pension - heart
    55: 'tabler-baby-bottle', // Orphan pension - baby bottle
    70: 'tabler-cancel', // No longer entitled - skull
  }

  return iconMap[code] || 'tabler-user'
}

const getStatusBadge = (code?: number | null) => {
  if (!code)
    return 'Status Unknown'

  const badgeMap: Record<number, string> = {
    10: 'Active',
    11: 'Disabled',
    30: 'Inactive',
    40: 'Retired',
    50: 'Partner Pension',
    55: 'Orphan Pension',
    70: 'No Entitlement',
  }

  return badgeMap[code] || 'Unknown Status'
}
</script>

<template>
  <VCard
    class="pension-code-card"
    variant="outlined"
    hover
    :ripple="{ center: true }"
    min-width="220"
    @click="openDialog"
  >
    <VCardText class="pa-4">
      <!-- Status Badge -->
      <div class="d-flex align-center mb-2">
        <VIcon
          :icon="getStatusIcon(pensionInfo?.code)"
          :color="getStatusColor(pensionInfo?.code)"
          size="24"
          class="mr-2"
        />
        <span class="text-caption font-weight-bold text-uppercase text-medium-emphasis">
          {{ getStatusBadge(pensionInfo?.code) }}
        </span>
        <VSpacer />

        <VTooltip
          text="Has pending changes"
          location="top"
        >
          <template #activator="{ props }">
            <VIcon
              v-if="pensionInfo?.hasPendingChanges"
              v-bind="props"
              icon="tabler-alert-circle"
              color="warning"
              size="20"
              class="ml-4"
            />
          </template>
        </VTooltip>
      </div>

      <!-- Code Display -->
      <div class="d-flex align-center">
        <VChip
          :color="getStatusColor(pensionInfo?.code)"
          variant="flat"
          size="large"
          class="px-3"
        >
          <span class="font-weight-bold">CODE: {{ getCodeDisplay(pensionInfo?.code) }}</span>
          <VIcon
            icon="tabler-edit"
            size="16"
            class="ml-2"
          />
        </VChip>
      </div>

      <!-- Description -->
      <div class="mt-3">
        <VExpandTransition>
          <div
            v-if="pensionInfo?.codeDescription"
            class="text-body-2 text-medium-emphasis"
          >
            {{ pensionInfo.codeDescription }}
          </div>
          <VAlert
            v-else
            type="info"
            variant="tonal"
            density="compact"
            class="mt-2"
          >
            No description available
          </VAlert>
        </VExpandTransition>
      </div>
    </VCardText>
  </VCard>

  <PensionCodeDialog
    v-model="dialogOpen"
    :current-code="pensionInfo?.code"
    :current-description="pensionInfo?.codeDescription"
    :participant-id="participantId"
    :pension-info-id="pensionInfo.id"
    :previous-code-effective-date="pensionInfo?.previousCodeEffectiveDate"
    :previous-code="pensionInfo?.previousCode"
    @code-changed="handleCodeChanged"
  />
</template>

<style scoped lang="scss">
  .pension-code-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 12px;
    border-width: 2px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: rgb(var(--v-theme-primary));
    }

    &:active {
      transform: translateY(0);
    }

    .v-card-text {
      padding: 16px;
    }
  }

  .v-chip {
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
</style>
