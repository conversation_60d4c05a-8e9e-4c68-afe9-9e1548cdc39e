<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import { useAppStore } from '@/stores/app/appStore'
import { ChangeType } from '@/gql/graphql'
import { usePensionStore } from '@/stores/pension/pensionStore'
import AppDateTimePicker from '@/@core/components/app-form-elements/AppDateTimePicker.vue'

interface PensionCode {
  code: number
  description: string
  allowedTransitions: number[]
  impact: string
}

interface Props {
  modelValue: boolean
  currentCode?: number | null
  currentDescription?: string | null
  participantId: string
  pensionInfoId: string
  previousCode?: number | null
  previousCodeEffectiveDate?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [boolean]
  'code-changed': [{ code: number; description: string; effectiveDate: string; reason: string }]
}>()

const dialog = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const selectedCode = ref<number | null>(null)
const effectiveDate = ref(new Date().toISOString().split('T')[0])
const changeReason = ref('')
const loading = ref(false)
const reverseConfirmDialog = ref(false)
const appStore = useAppStore()
const pensionStore = usePensionStore()

const participantName = computed(() => {
  return pensionStore.activeParticipant?.personalInfo?.name || ''
})

const { state: { loadingCreateChangeProposal }, actions: { createChangeProposal } } = useChangeProposal()

const pensionCodes: PensionCode[] = [
  {
    code: 10,
    description: 'Active',
    allowedTransitions: [30, 11, 40, 70],
    impact: 'Active pension accrual and indexation',
  },
  {
    code: 11,
    description: 'Disabled',
    allowedTransitions: [40, 70],
    impact: 'InActive service but continued pension accrual and indexation',
  },
  {
    code: 30,
    description: 'Inactive',
    allowedTransitions: [11, 40, 70],
    impact: 'No further pension accrual, only indexation',
  },
  {
    code: 40,
    description: 'Retired',
    allowedTransitions: [70],
    impact: 'No further pension accrual, only indexation',
  },
  {
    code: 50,
    description: 'Partner entitled to partner\'s pension',
    allowedTransitions: [70],
    impact: 'Partner will be added as new participant when pension starts',
  },
  {
    code: 55,
    description: 'Orphan entitled to orphan\'s pension',
    allowedTransitions: [70],
    impact: 'Orphan will be added as new participant when pension starts',
  },
  {
    code: 70,
    description: 'No longer entitled to pension',
    allowedTransitions: [],
    impact: 'No pension accrual or payments (e.g., due to death)',
  },
]

const currentCodeDescription = computed(() => {
  if (!props.currentCode)
    return ''
  const codeInfo = pensionCodes.find(c => c.code === props.currentCode)

  return codeInfo?.description || ''
})

const currentCodeImpact = computed(() => {
  if (!props.currentCode)
    return ''
  const codeInfo = pensionCodes.find(c => c.code === props.currentCode)

  return codeInfo?.impact || ''
})

const availableCodes = computed(() => {
  if (!props.currentCode)
    return pensionCodes

  const currentCodeInfo = pensionCodes.find(c => c.code === props.currentCode)
  if (!currentCodeInfo)
    return []

  // Get allowed transitions for current code
  const allowedCodes = currentCodeInfo.allowedTransitions
    .map(code => pensionCodes.find(c => c.code === code))
    .filter(Boolean) as PensionCode[]

  const reverseAllowed = true

  if (reverseAllowed && props.previousCode) {
    const previousCodeInfo = pensionCodes.find(c => c.code === props.previousCode)
    if (previousCodeInfo && !allowedCodes.some(c => c.code === props.previousCode))
      allowedCodes.push(previousCodeInfo)
  }

  return allowedCodes
})

const selectedCodeDetails = computed(() => {
  if (!selectedCode.value)
    return null

  return pensionCodes.find(c => c.code === selectedCode.value)
})

const isReverseChange = computed(() => {
  return selectedCode.value === props.previousCode && props.previousCode !== null
})

const isFormValid = computed(() => {
  return selectedCode.value !== null && effectiveDate.value && changeReason.value.trim()
})

const rules = {
  required: (v: any) => !!v || 'This field is required',
}

const getItemTitle = (item: PensionCode) => `${item.code} - ${item.description}`
const getItemValue = (item: PensionCode) => item.code

const getTransitionNote = (code: number) => {
  const codeInfo = pensionCodes.find(c => c.code === code)

  return codeInfo?.impact || ''
}

const getImpactDescription = (code: number | null) => {
  if (!code)
    return ''
  const codeInfo = pensionCodes.find(c => c.code === code)

  return codeInfo?.impact || ''
}

  type PensionCodeNumber = 10 | 11 | 30 | 40 | 50 | 55 | 70

const getChipIcon = (code: number | null | undefined): string => {
  if (code === null || code === undefined)
    return 'tabler-user'

  const iconMap: Record<PensionCodeNumber, string> = {
    10: 'tabler-user-check',
    11: 'tabler-user-exclamation',
    30: 'tabler-user-pause',
    40: 'tabler-user-star',
    50: 'tabler-users',
    55: 'tabler-baby-carriage',
    70: 'tabler-user-x',
  }

  return iconMap[code as PensionCodeNumber] || 'tabler-user'
}

const getChipColor = (code: number | null | undefined): string => {
  if (code === null || code === undefined)
    return 'grey'

  const colorMap: Record<PensionCodeNumber, string> = {
    10: 'success',
    11: 'warning',
    30: 'secondary',
    40: 'primary',
    50: 'info',
    55: 'purple',
    70: 'error',
  }

  return colorMap[code as PensionCodeNumber] || 'grey'
}

const confirmChange = () => {
  if (isReverseChange.value)
    reverseConfirmDialog.value = true
  else
    proceedWithChange()
}

const proceedWithChange = async () => {
  if (!selectedCode.value || !selectedCodeDetails.value)
    return

  loading.value = true
  reverseConfirmDialog.value = false

  try {
    await createChangeProposal({
      entityId: props.pensionInfoId,
      entityType: 'PensionInfo',
      path: 'code',
      newValue: selectedCode.value.toString(),
      oldValue: props.currentCode,
      effectiveDate: effectiveDate.value,
      type: ChangeType.Participant,
      participantName: participantName.value,
    })

    appStore.showSnack('Change proposal created successfully')

    // Also emit the event for UI updates
    emit('code-changed', {
      code: selectedCode.value,
      description: selectedCodeDetails.value.description,
      effectiveDate: effectiveDate.value,
      reason: changeReason.value,
    })

    closeDialog()
  }
  catch (error) {
    appStore.showSnack('Error creating change proposal')
    console.error('Error creating change proposal:', error)
  }
  finally {
    loading.value = false
  }
}

const closeDialog = () => {
  dialog.value = false
  resetForm()
}

const resetForm = () => {
  selectedCode.value = null
  effectiveDate.value = ''
  changeReason.value = ''
}

// Set today's date as default
watch(dialog, newVal => {
  if (newVal) {
    const today = new Date().toISOString().split('T')[0]

    effectiveDate.value = today
  }
})

const revertToPreviousCode = () => {
  if (!props.previousCode)
    return

  const previousCodeInfo = pensionCodes.find(c => c.code === props.previousCode)
  if (!previousCodeInfo)
    return

  selectedCode.value = props.previousCode
  changeReason.value = `Reverting to previous code ${props.previousCode} - ${previousCodeInfo.description}`
}

const hasPreviousCode = computed(() => {
  return props.previousCode !== null && props.previousCode !== undefined
})

const getPreviousCodeInfo = computed(() => {
  if (!props.previousCode)
    return null

  return pensionCodes.find(c => c.code === props.previousCode)
})

const getPreviousCodeIcon = computed((): string => {
  return getChipIcon(props.previousCode)
})

const getPreviousCodeColor = computed((): string => {
  return getChipColor(props.previousCode)
})

const isDateFieldDisabled = computed(() => {
  return loading.value || (selectedCode.value === props.previousCode && props.previousCode !== null)
})

// Format date for display
const formatDate = (dateString?: string | null) => {
  if (!dateString)
    return 'N/A'
  const date = new Date(dateString)

  return date.toLocaleDateString()
}

watch(selectedCode, newCode => {
  if (newCode === props.previousCode && props.previousCodeEffectiveDate) {
    // If reverting to previous code, use the previous effective date
    effectiveDate.value = props.previousCodeEffectiveDate
  }
  else {
    // Otherwise use today's date
    const today = new Date().toISOString().split('T')[0]

    effectiveDate.value = today
  }
})
</script>

<template>
  <VDialog
    v-model="dialog"
    max-width="600"
    persistent
  >
    <VCard>
      <VCardTitle class="d-flex align-center">
        <VIcon
          icon="tabler-user-edit"
          class="mr-2"
        />
        Change Pension Code
      </VCardTitle>

      <VCardText>
        <VAlert
          v-if="currentCode"
          type="success"
          variant="tonal"
          class="mb-8"
        >
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="font-weight-bold">
                Current Code: {{ currentCode }} - {{ currentCodeDescription }}
              </div>
              <div class="font-weight-medium text-caption">
                {{ currentCodeImpact }}
              </div>
            </div>
          </div>
        </VAlert>

        <VAlert
          v-if="hasPreviousCode"
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="font-weight-medium">
                Previous Code: {{ props.previousCode }} - {{ getPreviousCodeInfo?.description }}
                <span class="text-caption ml-8"><VIcon
                  size="small"
                  class="mr-1 mb-1"
                >tabler-calendar</VIcon>{{ formatDate(props.previousCodeEffectiveDate) }}</span>
              </div>
              <div class="text-caption">
                {{ getPreviousCodeInfo?.impact }}
              </div>
            </div>
            <VTooltip
              v-if="hasPreviousCode"
              text="Revert to previous code"
              location="top"
            >
              <template #activator="{ props }">
                <VBtn
                  v-bind="props"
                  icon="tabler-rotate-2"
                  variant="text"
                  color="primary"
                  @click="revertToPreviousCode"
                />
              </template>
            </VTooltip>
          </div>
        </VAlert>

        <VAutocomplete
          v-model="selectedCode"
          :items="availableCodes"
          :item-title="getItemTitle"
          :item-value="getItemValue"
          label="Select New Pension Code"
          prepend-inner-icon="tabler-switch-horizontal"
          variant="outlined"
          :rules="[rules.required]"
          :disabled="loading"
          class="my-4"
        >
          <template #item="{ props, item }">
            <VListItem
              v-bind="props"
              :title="`${item.raw.code} - ${item.raw.description}`"
              :subtitle="getTransitionNote(item.raw.code)"
            >
              <template #prepend>
                <VIcon
                  :icon="getChipIcon(item.raw.code)"
                  :color="getChipColor(item.raw.code)"
                />
              </template>
            </VListItem>
          </template>
        </VAutocomplete>

        <AppDateTimePicker
          v-model="effectiveDate"
          label="Effective Date"
          variant="outlined"
          prepend-inner-icon="tabler-calendar"
          :rules="[rules.required]"
          :readonly="isDateFieldDisabled"
          :bg-color="isDateFieldDisabled ? 'grey-lighten-4' : ''"
          :config="{
            dateFormat: 'Y-m-d',
            maxDate: 'today',
            altInput: true,
            altFormat: 'F j, Y',
          }"
        />

        <div
          v-if="isReverseChange && props.previousCodeEffectiveDate"
          class="text-caption text-primary ml-4 mb-2"
        >
          <VIcon
            size="small"
            icon="tabler-info-circle"
            class="mr-1"
          />
          Date automatically set to previous code's effective date ({{ formatDate(props.previousCodeEffectiveDate) }}).
        </div>

        <VTextarea
          v-model="changeReason"
          label="Reason for Change"
          variant="outlined"
          prepend-inner-icon="tabler-message-2"
          rows="3"
          :rules="[rules.required]"
          :disabled="loading"
          class="mt-4"
        />

        <!--        <v-alert -->
        <!--          v-if="isReverseChange" -->
        <!--          type="warning" -->
        <!--          variant="tonal" -->
        <!--          class="mt-4" -->
        <!--        > -->
        <!--          <div class="font-weight-medium">Warning: Reverse Change Detected</div> -->
        <!--          <div>You are reversing a previous code change. Please ensure this is intentional.</div> -->
        <!--        </v-alert> -->

        <VAlert
          v-if="selectedCodeDetails"
          type="info"
          variant="tonal"
          class="mt-4"
        >
          <div class="font-weight-medium">
            Impact of Change
          </div>
          <div>{{ getImpactDescription(selectedCode) }}</div>
        </VAlert>
      </VCardText>

      <VCardActions>
        <VSpacer />
        <VBtn
          variant="text"
          :disabled="loading"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          variant="flat"
          :loading="loading"
          :disabled="!isFormValid"
          @click="confirmChange"
        >
          Confirm Change
        </VBtn>
      </VCardActions>
    </VCard>

    <!-- Confirmation Dialog for Reverse Changes -->
    <VDialog
      v-model="reverseConfirmDialog"
      max-width="400"
    >
      <VCard>
        <VCardTitle class="text-h6">
          Confirm Reverse Change
        </VCardTitle>
        <VCardText>
          <VAlert
            type="warning"
            variant="tonal"
          >
            You are about to reverse a previous code change from {{ props.currentCode }} to {{ selectedCode }}.
            This may affect pension accrual calculations.
          </VAlert>
          <p class="mt-3">
            Are you sure you want to proceed with this reverse change?
          </p>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="text"
            @click="reverseConfirmDialog = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="warning"
            variant="flat"
            @click="proceedWithChange"
          >
            Proceed
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VDialog>
</template>
