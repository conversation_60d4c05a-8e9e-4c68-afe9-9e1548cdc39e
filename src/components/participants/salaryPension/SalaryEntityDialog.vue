<script setup lang="ts">
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import { useAppStore } from '@/stores/app/appStore'
import { ChangeType } from '@/gql/graphql'

interface SalaryEntryFormData {
  id?: string
  amount: number | null
  partTimePercentage: number | null
  employmentInfoId?: string
}

const props = defineProps({
  // Props from SalaryEntryDialog
  modelValue: {
    type: Boolean,
    default: false,
  },
  path: {
    type: String,
    required: true,
  },
  entityId: {
    type: String,
    required: true,
  },
  entityType: {
    type: String,
    required: true,
  },
  entry: {
    type: Object as () => {
      employmentInfo?: any
      id?: string
      amount?: number
      partTimePercentage?: number
    },
    default: null,
  },
  formType: {
    type: String,
    default: 'salary',
    validator: (value: string) => ['salary', 'partTime', 'new'].includes(value),
  },
  currentValue: {
    type: [Number, String],
    default: null,
  },
  year: {
    type: [Number, String],
    default: '2026',
  },
  employmentInfoId: {
    type: String,
    default: '',
  },
  participantName: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: ChangeType.Participant,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh', 'submit', 'cancel', 'closeDialog'])
const { state: { loadingCreateChangeProposal }, actions: { createChangeProposal } } = useChangeProposal()
const appStore = useAppStore()

const editMode = computed(() => !!props.entry)

const oldValue = computed(() => {
  return props.formType === 'salary' ? props.entry?.amount : props.entry?.partTimePercentage
})

const newGrossPartTimeMonthlySalary = ref<number | string>(0)
const newPartTimePercentage = ref<number | string >(0)

// Simplified form data using refs
const amount = ref<number | null>(null)
const partTimePercentage = ref<number | null>(null)

// Initialize form data based on props
watch(() => props.entry, newEntry => {
  if (newEntry) {
    amount.value = newEntry.amount ?? null
    partTimePercentage.value = newEntry.partTimePercentage ?? null
  }
}, { immediate: true })

watch(() => props.currentValue, newValue => {
  if (props.formType === 'salary' && newValue !== null)
    amount.value = typeof newValue === 'string' ? Number.parseFloat(newValue) : newValue
  else if (props.formType === 'partTime' && newValue !== null)
    partTimePercentage.value = typeof newValue === 'string' ? Number.parseFloat(newValue) : newValue
}, { immediate: true })

const newValue = computed(() => {
  return props.formType === 'salary' ? amount.value : partTimePercentage.value
})

const effectiveDate = computed(() => {
  return new Date(`${props.year}-01-01T00:00:00`)
})

const formatNumberWithCommas = (number: number) => {
  if (number === null || number === undefined)
    return ''

  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')
}

const closeDialog = () => {
  emit('update:modelValue', false)
  emit('closeDialog', false)
}

const saveChanges = async () => {
  if (newValue.value === null)
    return

  if (props.formType === 'new') {
    await saveNewData()
  }
  else {
    await createChangeProposal({
      participantName: props.participantName,
      entityId: props.entityId,
      entityType: props.entityType,
      path: props.path,
      newValue: newValue.value.toString(),
      oldValue: oldValue.value?.toString() || '',
      effectiveDate: new Date().toISOString(),
      type: props.type as ChangeType || ChangeType.Parameters,
    })
  }

  showSuccessAndClose()
}

const saveNewData = async () => {
  const commonProps = {
    participantName: props.participantName,
    entityId: props.employmentInfoId,
    entityType: props.entityType,
    oldValue: 'new',
    effectiveDate: new Date().toISOString(),
    type: ChangeType.Participant,
  }

  await Promise.all([
    createChangeProposal({
      ...commonProps,
      path: 'amount',
      newValue: newGrossPartTimeMonthlySalary.value?.toString() ?? '0',
    }),
    createChangeProposal({
      ...commonProps,
      path: 'partTimePercentage',
      newValue: newPartTimePercentage.value?.toString() ?? '0',
    }),
  ])
  showSuccessAndClose()
}

const showSuccessAndClose = () => {
  // clear all inputs
  newGrossPartTimeMonthlySalary.value = 0
  newPartTimePercentage.value = 0
  appStore.showSnack('Change proposal created successfully')
  emit('refresh')
  closeDialog()
}
</script>

<template>
  <VDialog
    :model-value="modelValue"
    max-width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <VCard class="pa-3">
      <VCardTitle class="pb-3 text-h5">
        <span>{{ editMode ? 'Edit Salary Entry' : 'Add New Salary Entry' }}</span>
        <VBtn
          icon
          variant="text"
          size="small"
          class="float-right"
          @click="closeDialog"
        >
          <VIcon>tabler-x</VIcon>
        </VBtn>
      </VCardTitle>
      <VDivider />
      <VCardText class="pt-6">
        <VForm
          ref="form"
          @submit.prevent="saveChanges"
        >
          <VContainer>
            <h2 class="text-h5 mb-6">
              {{ editMode ? 'Edit' : `Enter salary information for ${year}` }} {{ editMode && formType === 'salary' ? 'Gross Part-time Monthly Salary' : 'Part-time Percentage' }} for {{ year }}
            </h2>

            <!-- Current Value Section -->
            <div
              v-if="editMode"
              class="bg-grey-lighten-5 mb-6"
            >
              <div class="text-subtitle-1 mb-2">
                Current Value
              </div>
              <VTextField
                :model-value="oldValue"
                type="number"
                readonly
                variant="solo-filled"
                density="comfortable"
                hide-details
                class="mb-2"
              />
            </div>

            <!--            Gross Part-time Monthly Salary -->
            <div
              v-else
              class="bg-grey-lighten-5 mb-6"
            >
              <div class="text-subtitle-1 mb-2">
                Gross Part-time Monthly Salary
              </div>
              <div class="text-caption">
                Enter the gross part-time monthly salary amount as of January 1st, {{ year }}
                (or as of start date if starting in {{ year }})
              </div>
              <VTextField
                v-model="newGrossPartTimeMonthlySalary"
                type="number"
                variant="outlined"
                density="comfortable"
                hide-details
                class="mb-2"
              />
            </div>

            <!-- New Value Section -->
            <div
              v-if="editMode"
              class="mb-6"
            >
              <div class="text-h6 mb-2">
                New {{ formType === 'salary' ? 'Gross Part-time Monthly Salary' : 'Part-time Percentage' }}
              </div>
              <div class="text-subtitle-2 text-grey mb-4">
                Enter the {{ formType === 'salary' ? 'gross part-time monthly salary amount' : 'part-time percentage' }} as of January 1st, {{ year }}
                (or as of start date if starting in {{ year }})
              </div>

              <!-- Salary Input -->
              <VTextField
                v-if="formType === 'salary'"
                v-model="amount"
                type="number"
                variant="outlined"
                density="comfortable"
                hide-details
                class="mb-2"
              />

              <!-- Part-time Percentage Input -->
              <VTextField
                v-else
                v-model="partTimePercentage"
                type="number"
                variant="outlined"
                density="comfortable"
                bg-color="surface"
                hide-details
                class="mb-2"
                step="0.01"
                min="0"
                max="1"
              />

              <!-- Helper text for part-time percentage -->
              <div
                v-if="formType === 'partTime'"
                class="text-caption text-grey mb-4"
              >
                Enter as decimal: 1 for 100%, 0.8 for 80%, etc.
              </div>
            </div>

            <div
              v-else
              class="mb-6"
            >
              <div class="text-h6 mb-2">
                Part-time Percentage
              </div>
              <div class="text-subtitle-2 text-grey mb-4">
                Enter the part-time percentage as of January 1st, {{ year }}
                (or as of start date if starting in {{ year }})
              </div>

              <!-- Part-time Percentage Input -->
              <VTextField
                v-model="newPartTimePercentage"
                type="number"
                variant="outlined"
                density="comfortable"
                bg-color="surface"
                hide-details
                class="mb-2"
                step="0.01"
                min="0"
                max="1"
              />

              <!-- Helper text for part-time percentage -->
              <div
                v-if="formType === 'partTime' || !editMode"
                class="text-caption text-grey mb-4"
              >
                Enter as decimal: 1 for 100%, 0.8 for 80%, etc.
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex justify-end">
              <VBtn
                color="grey-lighten-3"
                variant="elevated"
                class="me-4"
                @click="closeDialog"
              >
                Cancel
              </VBtn>
              <VBtn
                v-if="props.formType !== 'new'"
                color="primary"
                type="submit"
                :loading="loadingCreateChangeProposal"
                :disabled="loadingCreateChangeProposal || (formType === 'salary' ? amount === null : partTimePercentage === null)"
              >
                Submit for Review
              </VBtn>

              <VBtn
                v-else
                color="primary"
                :loading="loadingCreateChangeProposal"
                @click="saveNewData"
              >
                Submit for Review
              </VBtn>
            </div>
          </VContainer>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped>
  .float-right {
    position: absolute;
    right: 16px;
    top: 16px;
  }
</style>
