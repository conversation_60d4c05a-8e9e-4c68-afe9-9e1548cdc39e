<script setup lang="ts">
import SalaryEntries from './SalaryEntries.vue'
import PartTimePercentage from './PartTimePercentage.vue'
import type { SalaryEntry } from '@/gql/graphql'
import PensionPrimaryCalc from '@/components/participants/salaryPension/PensionPrimaryCalc.vue'

interface SalaryItem {
  id: string
  year: string
  grossSalary: string
  status: string
}

interface PercentageItem {
  id: string
  year: string
  percentage: string
  status: string
}

interface PensionItem {
  id: string
  year: string
  grossMonthlySalary: string
  annualMultiplier: string
  grossAnnualSalary: string
  offset: string
}

const props = defineProps({
  salaryData: {
    type: Array as () => SalaryItem[],
    default: () => [
      {
        id: 'sal-2024',
        year: '2024',
        grossSalary: 'Afl. 4.680,00',
        status: 'Current',
      },
      {
        id: 'sal-2023',
        year: '2023',
        grossSalary: 'Afl. 4.555,00',
        status: 'Certified',
      },
      {
        id: 'sal-2022',
        year: '2022',
        grossSalary: 'Afl. 4.430,00',
        status: 'Certified',
      },
    ],
  },
  percentageData: {
    type: Array as () => PercentageItem[],
    default: () => [
      {
        id: 'pct-2024',
        year: '2024',
        percentage: '100.00%',
        status: 'Current',
      },
      {
        id: 'pct-2023',
        year: '2023',
        percentage: '100.00%',
        status: 'Certified',
      },
      {
        id: 'pct-2022',
        year: '2022',
        percentage: '100.00%',
        status: 'Certified',
      },
    ],
  },
  pensionData: {
    type: Array as () => PensionItem[],
    default: () => [
      {
        id: 'pen-2024',
        year: '2024',
        grossMonthlySalary: 'Afl. 4.680,00',
        annualMultiplier: '13',
        grossAnnualSalary: 'Afl. 60.840,00',
        offset: 'Afl. 17.616,00',
      },
      {
        id: 'pen-2023',
        year: '2023',
        grossMonthlySalary: 'Afl. 4.555,00',
        annualMultiplier: '13',
        grossAnnualSalary: 'Afl. 59.215,00',
        offset: 'Afl. 17.616,00',
      },
      {
        id: 'pen-2022',
        year: '2022',
        grossMonthlySalary: 'Afl. 4.430,00',
        annualMultiplier: '13',
        grossAnnualSalary: 'Afl. 57.590,00',
        offset: 'Afl. 17.616,00',
      },
    ],
  },
  participant: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:salaryData', 'update:percentageData', 'update:pensionData'])

const dialogVisible = ref(false)
const selectedEntry = ref<SalaryEntry | null>(null)

const editSalaryDialog = ref(false)
const editPercentageDialog = ref(false)
const editPensionDialog = ref(false)
const selectedSalary = ref({ year: '', grossSalary: '', status: '', id: '' })
const selectedPercentage = ref({ year: '', percentage: '', status: '', id: '' })

const selectedPension = ref({
  year: '',
  grossMonthlySalary: '',
  annualMultiplier: '',
  grossAnnualSalary: '',
  offset: '',
  id: '',
})

const openSalaryEditDialog = (item: any) => {
  selectedSalary.value = { ...item }
  editSalaryDialog.value = true
}

const openAddDialog = () => {
  selectedEntry.value = null
  dialogVisible.value = true
}

const openPercentageEditDialog = (item: any) => {
  selectedPercentage.value = { ...item }
  editPercentageDialog.value = true
}

const isCollapsed = ref(false)

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// Helper function to determine status color
const getStatusColor = (status: string) => {
  if (status === 'Current')
    return 'primary'
  if (status === 'Certified')
    return 'success'

  return 'default'
}

const salaryEntries = ref<SalaryEntry[]>([])
const loadingSalaryEntries = ref(false)

const fetchSalaryEntries = () => {
  if (!props.participant || !props.participant.employmentInfo)
    return

  loadingSalaryEntries.value = true

  if (props.participant.employmentInfo.salaryEntries)
    salaryEntries.value = [...props.participant.employmentInfo.salaryEntries].sort((a, b) => b.year - a.year)
  else
    salaryEntries.value = []

  loadingSalaryEntries.value = false
}

onMounted(() => {
  if (props.participant)
    fetchSalaryEntries()
})

watch(() => props.participant, newVal => {
  if (newVal)
    fetchSalaryEntries()
}, { deep: true })
</script>

<template>
  <VCard
    class="salary-pension-container"
    elevation="0"
  >
    <VCardTitle class="header-container d-flex justify-space-between align-center">
      <h2 class="text-h4 font-weight-medium">
        Salary & Pension Base
      </h2>
      <VBtn
        variant="outlined"
        color="primary"
        size="small"
        :prepend-icon="isCollapsed ? 'tabler-chevron-down' : 'tabler-chevron-up'"
        @click="toggleCollapse"
      >
        {{ isCollapsed ? 'Expand' : 'Collapse' }}
      </VBtn>
    </VCardTitle>

    <VCardText v-if="!isCollapsed">
      <!-- Salary Information Section -->
      <div class="section-container mb-6">
        <div class="section-header d-flex justify-space-between align-start mb-4">
          <div>
            <h3 class="text-h6 font-weight-medium">
              Salary Information
            </h3>
            <div class="text-caption text-medium-emphasis">
              Overview of annual salary and part-time percentage
            </div>
          </div>
          <div class="d-flex justify-end mb-3">
            <VBtn
              color="primary"
              size="small"
              prepend-icon="tabler-plus"
              @click="openAddDialog"
            >
              Add New Entry
            </VBtn>
          </div>
        </div>

        <VRow>
          <VCol
            cols="12"
            md="6"
            class="pr-md-3"
          >
            <SalaryEntries
              :participant-id="participant.id"
              :employment-info-id="participant.employmentInfo.id"
              :can-edit="true"
              @refresh="fetchSalaryEntries"
            />
            <!--            <VCard -->
            <!--              v-else -->
            <!--              variant="outlined" -->
            <!--              class="mb-4" -->
            <!--            > -->
            <!--              <VCardTitle class="py-3"> -->
            <!--                <h4 class="text-subtitle-1 font-weight-medium"> -->
            <!--                  Gross Part-time Monthly Salary -->
            <!--                </h4> -->
            <!--              </VCardTitle> -->
            <!--              <VCardSubtitle class="text-caption text-medium-emphasis"> -->
            <!--                Amount as of January 1st or start date if started during the year -->
            <!--              </VCardSubtitle> -->
            <!--              <VCardText> -->
            <!--                <VTable -->
            <!--                  class="salary-table" -->
            <!--                  density="comfortable" -->
            <!--                > -->
            <!--                  <thead> -->
            <!--                    <tr> -->
            <!--                      <th class="text-left"> -->
            <!--                        YEAR -->
            <!--                      </th> -->
            <!--                      <th class="text-left"> -->
            <!--                        GROSS PART-TIME MONTHLY SALARY -->
            <!--                      </th> -->
            <!--                      <th class="text-center"> -->
            <!--                        STATUS -->
            <!--                      </th> -->
            <!--                      <th class="text-center"> -->
            <!--                        ACTIONS -->
            <!--                      </th> -->
            <!--                    </tr> -->
            <!--                  </thead> -->
            <!--                  <tbody> -->
            <!--                    <tr -->
            <!--                      v-for="item in salaryData" -->
            <!--                      :key="item.id" -->
            <!--                      :class="item.status === 'Current' ? 'bg-blue-lighten-5' : ''" -->
            <!--                    > -->
            <!--                      <td class="font-weight-medium"> -->
            <!--                        {{ item.year }} -->
            <!--                      </td> -->
            <!--                      <td>{{ item.grossSalary }}</td> -->
            <!--                      <td class="text-center"> -->
            <!-- &lt;!&ndash;                        <VChip&ndash;&gt; -->
            <!-- &lt;!&ndash;                               :color="getStatusColor(item.status)"&ndash;&gt; -->
            <!-- &lt;!&ndash;                               size="small"&ndash;&gt; -->
            <!-- &lt;!&ndash;                               label&ndash;&gt; -->
            <!-- &lt;!&ndash;&gt;&ndash;&gt; -->
            <!-- &lt;!&ndash;                          {{ item.status }}&ndash;&gt; -->
            <!-- &lt;!&ndash;                        </VChip>&ndash;&gt; -->
            <!--                      </td> -->
            <!--                      <td class="text-center"> -->
            <!--                        <VBtn -->
            <!--                          v-if="item.status === 'Current'" -->
            <!--                          icon -->
            <!--                          size="small" -->
            <!--                          variant="text" -->
            <!--                          color="primary" -->
            <!--                          @click="openSalaryEditDialog(item)" -->
            <!--                        > -->
            <!--                          <VIcon>tabler-edit</VIcon> -->
            <!--                        </VBtn> -->
            <!--                      </td> -->
            <!--                    </tr> -->
            <!--                  </tbody> -->
            <!--                </VTable> -->
            <!--              </VCardText> -->
            <!--            </VCard> -->
          </VCol>

          <VCol
            cols="12"
            md="6"
            class="pl-md-3"
          >
            <PartTimePercentage
              v-if="participant && participant.employmentInfo && salaryEntries.length > 0"
              :entries="salaryEntries"
              :participant-name="`${participant?.personalInfo?.firstName} ${participant?.personalInfo?.lastName}`"
              :employment-info-id="participant.employmentInfo.id"
              :loading="loadingSalaryEntries"
              :can-edit="true"
              @refresh="fetchSalaryEntries"
            />
            <VCard
              v-else
              variant="outlined"
              class="mb-4"
            >
              <VCardTitle class="py-3">
                <h4 class="text-subtitle-1 font-weight-medium">
                  Part-time Percentage eee
                </h4>
              </VCardTitle>
              <VCardSubtitle class="text-caption text-medium-emphasis">
                Percentage as of January 1st or start date if started during the year
              </VCardSubtitle>
              <VCardText>
                <VTable
                  class="percentage-table"
                  density="comfortable"
                >
                  <thead>
                    <tr>
                      <th class="text-left">
                        YEAR
                      </th>
                      <th class="text-left">
                        PART-TIME %
                      </th>
                      <th class="text-center">
                        STATUS
                      </th>
                      <th class="text-center">
                        ACTIONS
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="item in percentageData"
                      :key="item.id"
                      :class="item.status === 'Current' ? 'bg-blue-lighten-5' : ''"
                    >
                      <td class="font-weight-medium">
                        {{ item.year }}
                      </td>
                      <td>{{ item.percentage }}</td>
                      <td class="text-center">
                        <!--                        <VChip -->
                        <!--                               :color="getStatusColor(item.status)" -->
                        <!--                               size="small" -->
                        <!--                               label -->
                        <!-- &gt; -->
                        <!--                          {{ item.status }} -->
                        <!--                        </VChip> -->
                      </td>
                      <td class="text-center">
                        <VBtn
                          v-if="item.status === 'Current'"
                          icon
                          size="small"
                          variant="text"
                          color="primary"
                          @click="openPercentageEditDialog(item)"
                        >
                          <VIcon>tabler-edit</VIcon>
                        </VBtn>
                      </td>
                    </tr>
                  </tbody>
                </VTable>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
      </div>

      <VDivider class="my-6" />

      <!-- Pension Base Calculation Section -->
      <PensionPrimaryCalc />
    </VCardText>

    <SalaryEntityDialog
      v-model="dialogVisible"
      :edit-mode="false"
      path="new"
      entity-id=""
      :employment-info-id="participant.employmentInfo.id"
      entity-type="salaryEntry"
      :entry="selectedEntry as any"
      form-type="new"
      :year="2026"
      @refresh="$emit('refresh')"
    />
  </VCard>
</template>

<style scoped>
  .salary-pension-container {
    border-radius: 8px;
  }

  .header-container {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 20px
  }

  .section-header {
    margin-bottom: 16px;
  }

  .salary-table,
  .percentage-table,
  .pension-table {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
  }

  .salary-table th,
  .percentage-table th,
  .pension-table th {
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }

  @media (max-width: 960px) {
    .pl-md-3, .pr-md-3 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
</style>
