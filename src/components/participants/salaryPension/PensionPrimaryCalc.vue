<script setup lang="ts">
import type { SalaryEntry } from '@/gql/graphql'
import { useParticipants } from '@/composables/participants/useParticipants'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
import { formatCurrency } from '@/utils/transformers'
import type { PensionItem } from '@/types/participant.types'

const props = defineProps({
  participant: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:salaryData', 'update:percentageData', 'update:pensionData'])
const { state: { allPensionParameters } } = usePensionParameters()
const { state: { participantSalaryEntries } } = useParticipants()

// Generate pension data based on salary entries and pension parameters
const pensionData = computed<PensionItem[]>(() => {
  if (!participantSalaryEntries.value || !Array.isArray(participantSalaryEntries.value) || !allPensionParameters.value)
    return []

  return participantSalaryEntries.value
    .slice()
    .sort((a, b) => b.year - a.year) // Sort by year in descending order
    .map(entry => {
      const params = allPensionParameters.value.find((param: any) => param.year === entry.year.toString())

      if (!params) {
        console.warn(`No pension parameters found for year ${entry.year}`)

        return {
          id: `pen-${entry.year}`,
          year: entry.year.toString(),
          grossMonthlySalary: '-',
          annualMultiplier: 'Not set',
          grossAnnualSalary: '-',
          offset: 'Not set',
          pensionBase: 'Missing parameters',
        }
      }

      // Calculate values based on the formulas:
      // 1. Gross fulltime monthly salary = gross parttime monthly salary / parttime percentage
      const grossFulltimeMonthly = entry.amount / entry.partTimePercentage

      // 2. Gross fulltime annual salary = annual multiplier x gross fulltime monthly salary
      const grossFulltimeAnnual = params.annualMultiplier * grossFulltimeMonthly

      // 3. Pension base = gross fulltime annual salary - offset
      const pensionBase = grossFulltimeAnnual - params.offsetAmount

      return {
        id: `pen-${entry.year}`,
        year: entry.year.toString(),
        grossMonthlySalary: formatCurrency(grossFulltimeMonthly),
        annualMultiplier: params.annualMultiplier.toString(),
        grossAnnualSalary: formatCurrency(grossFulltimeAnnual),
        offset: formatCurrency(params.offsetAmount),
        pensionBase: formatCurrency(pensionBase),
      }
    })
})

const salaryEntries = ref<SalaryEntry[]>([])
const loadingSalaryEntries = ref(false)

const fetchSalaryEntries = () => {
  if (!props.participant || !props.participant.employmentInfo)
    return

  loadingSalaryEntries.value = true

  if (props.participant.employmentInfo.salaryEntries)
    salaryEntries.value = props.participant.employmentInfo.salaryEntries
  else
    salaryEntries.value = []

  loadingSalaryEntries.value = false
}

onMounted(() => {
  if (props.participant)
    fetchSalaryEntries()
})

watch(() => props.participant, newVal => {
  if (newVal)
    fetchSalaryEntries()
}, { deep: true })
</script>

<template>
  <div>
    <div class="section-container">
      <div class="section-header d-flex justify-space-between align-start mb-4">
        <div>
          <h3 class="text-h6 font-weight-medium">
            Pension Base Calculation
          </h3>
          <div class="text-caption text-medium-emphasis">
            Overview of pension base calculations per year
          </div>
        </div>
      </div>

      <VCard variant="outlined">
        <VCardText>
          <VTable
            class="pension-table"
            density="comfortable"
          >
            <thead>
              <tr>
                <th class="text-left">
                  YEAR
                </th>
                <th class="text-left">
                  GROSS FULLTIME MONTHLY SALARY
                </th>
                <th class="text-left">
                  ANNUAL MULTIPLIER
                </th>
                <th class="text-left">
                  GROSS FULLTIME ANNUAL SALARY
                </th>
                <th class="text-left">
                  OFFSET
                </th>
                <th class="text-left">
                  PENSION BASE
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in pensionData"
                :key="item.id"
              >
                <td class="font-weight-medium">
                  {{ item.year }}
                </td>
                <td>{{ item.grossMonthlySalary }}</td>
                <td>{{ item.annualMultiplier }}</td>
                <td>{{ item.grossAnnualSalary }}</td>
                <td>{{ item.offset }}</td>
                <td>{{ item.pensionBase }}</td>
              </tr>
            </tbody>
          </VTable>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<style scoped>
  .section-header {
    margin-bottom: 16px;
  }

  .pension-table {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
  }

  .salary-table th,
  .percentage-table th,
  .pension-table th {
    font-weight: 500;
    background-color: #f5f5f5;
  }
</style>
