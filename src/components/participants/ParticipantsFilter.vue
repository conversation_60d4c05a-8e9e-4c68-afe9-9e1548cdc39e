<script setup lang="ts">
import { computed, ref } from 'vue'

const props = defineProps({
  participantsList: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['update:filters'])

const isFilterPanelOpen = ref(false)
const loadingFilteredParticipants = ref(false)

// Generate filter options from participants list
const filterOptions = computed(() => {
  const departments = new Set()
  const positions = new Set()
  const locations = new Set()
  const statuses = new Set()

  props.participantsList.forEach(item => {
    if (item.department)
      departments.add(item.department)
    if (item.position)
      positions.add(item.position)
    if (item.location)
      locations.add(item.location)
    if (item.status)
      statuses.add(item.status)
  })

  return {
    departments: Array.from(departments).map(item => ({ id: item, text: item })),
    positions: Array.from(positions).map(item => ({ id: item, text: item })),
    locations: Array.from(locations).map(item => ({ id: item, text: item })),
    statuses: Array.from(statuses).map((item: any) => ({
      id: item,
      text: item.charAt(0).toUpperCase() + item.slice(1),
    })),
  }
})

// Filter state
const filters = ref({
  department: null,
  position: null,
  status: null,
  location: null,
  birthDateRange: {
    startDate: null,
    endDate: null,
  },
  enrollmentDateRange: {
    startDate: null,
    endDate: null,
  },
})

// Date range menu states
const birthDateRangeMenu = ref(false)
const enrollmentDateRangeMenu = ref(false)

// Computed properties for date range displays
const birthDateRangeDisplay = computed(() => {
  const { startDate, endDate } = filters.value.birthDateRange
  if (startDate && endDate)
    return `${formatDateToString(startDate)} - ${formatDateToString(endDate)}`

  return 'Select Birth Date Range'
})

const enrollmentDateRangeDisplay = computed(() => {
  const { startDate, endDate } = filters.value.enrollmentDateRange
  if (startDate && endDate)
    return `${formatDateToString(startDate)} - ${formatDateToString(endDate)}`

  return 'Select Enrollment Date Range'
})

// Method to clear all filters
const resetFilters = () => {
  filters.value = {
    department: null,
    position: null,
    status: null,
    location: null,
    birthDateRange: {
      startDate: null,
      endDate: null,
    },
    enrollmentDateRange: {
      startDate: null,
      endDate: null,
    },
  }
  birthDateRangeMenu.value = false
  enrollmentDateRangeMenu.value = false
  emit('update:filters', {})
}

// Clear date range filters
const clearBirthDateRangeFilter = () => {
  filters.value.birthDateRange.startDate = null
  filters.value.birthDateRange.endDate = null
  birthDateRangeMenu.value = false
}

const clearEnrollmentDateRangeFilter = () => {
  filters.value.enrollmentDateRange.startDate = null
  filters.value.enrollmentDateRange.endDate = null
  enrollmentDateRangeMenu.value = false
}

// Toggle filter panel
const toggleFilterPanel = () => {
  isFilterPanelOpen.value = !isFilterPanelOpen.value
}

// Apply filters
const applyFilters = () => {
  loadingFilteredParticipants.value = true

  // Prepare filters for emitting
  const preparedFilters = {
    status: filters.value.status?.id || null,
    birthDateRange: {
      startDate: filters.value.birthDateRange.startDate,
      endDate: filters.value.birthDateRange.endDate,
    },
    enrollmentDateRange: {
      startDate: filters.value.enrollmentDateRange.startDate,
      endDate: filters.value.enrollmentDateRange.endDate,
    },
  }

  emit('update:filters', preparedFilters)

  // Simulate loading
  setTimeout(() => {
    loadingFilteredParticipants.value = false
  }, 500)
}

// Count applied filters
const appliedFiltersCount = computed(() => {
  let count = 0
  if (filters.value.status)
    count++
  if (filters.value.birthDateRange.startDate && filters.value.birthDateRange.endDate)
    count++

  return count
})
</script>

<template>
  <div class="filter-component mb-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <VBtn
        prepend-icon="tabler-filter"
        color="primary"
        size="small"
        variant="outlined"
        class="filter-btn"
        @click="toggleFilterPanel"
      >
        Advanced Filter
        <template #append>
          <VBadge
            v-if="appliedFiltersCount > 0"
            color="error"
            :content="appliedFiltersCount"
            inline
          />
        </template>
      </VBtn>
    </div>

    <VExpandTransition>
      <VCard
        v-if="isFilterPanelOpen"
        variant="outlined"
        class="mb-4"
      >
        <VCardText>
          <VRow>
            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VSelect
                v-model="filters.status"
                :disabled="loadingFilteredParticipants"
                :items="filterOptions.statuses"
                item-title="text"
                item-value="id"
                label="Status"
                prepend-inner-icon="tabler-circle-check"
                clearable
                density="compact"
                variant="outlined"
                return-object
              />
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VSelect
                v-model="filters.location"
                :disabled="loadingFilteredParticipants"
                :items="filterOptions.locations"
                item-title="text"
                item-value="id"
                label="Location"
                prepend-inner-icon="tabler-map-pin"
                clearable
                density="compact"
                variant="outlined"
                return-object
              />
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VMenu
                v-model="birthDateRangeMenu"
                :disabled="loadingFilteredParticipants"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template #activator="{ props }">
                  <VTextField
                    v-bind="props"
                    v-model="birthDateRangeDisplay"
                    label="Birth Date Range"
                    prepend-inner-icon="tabler-calendar"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearBirthDateRangeFilter"
                  />
                </template>

                <VCard min-width="300px">
                  <VCardText>
                    <VRow>
                      <VCol cols="12">
                        <VTextField
                          v-model="filters.birthDateRange.startDate"
                          :disabled="loadingFilteredParticipants"
                          label="Start Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </VCol>

                      <VCol cols="12">
                        <VTextField
                          v-model="filters.birthDateRange.endDate"
                          :disabled="loadingFilteredParticipants"
                          label="End Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </VCol>
                    </VRow>
                  </VCardText>
                  <VCardActions>
                    <VSpacer />
                    <VBtn
                      color="primary"
                      @click="clearBirthDateRangeFilter"
                    >
                      Clear
                    </VBtn>
                    <VBtn
                      color="success"
                      @click="birthDateRangeMenu = false"
                    >
                      Apply
                    </VBtn>
                  </VCardActions>
                </VCard>
              </VMenu>
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VMenu
                v-model="enrollmentDateRangeMenu"
                :disabled="loadingFilteredParticipants"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template #activator="{ props }">
                  <VTextField
                    v-bind="props"
                    v-model="enrollmentDateRangeDisplay"
                    label="Enrollment Date Range"
                    prepend-inner-icon="tabler-calendar"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearEnrollmentDateRangeFilter"
                  />
                </template>

                <VCard min-width="300px">
                  <VCardText>
                    <VRow>
                      <VCol cols="12">
                        <VTextField
                          v-model="filters.enrollmentDateRange.startDate"
                          :disabled="loadingFilteredParticipants"
                          label="Start Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </VCol>

                      <VCol cols="12">
                        <VTextField
                          v-model="filters.enrollmentDateRange.endDate"
                          :disabled="loadingFilteredParticipants"
                          label="End Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </VCol>
                    </VRow>
                  </VCardText>
                  <VCardActions>
                    <VSpacer />
                    <VBtn
                      color="primary"
                      @click="clearEnrollmentDateRangeFilter"
                    >
                      Clear
                    </VBtn>
                    <VBtn
                      color="success"
                      @click="enrollmentDateRangeMenu = false"
                    >
                      Apply
                    </VBtn>
                  </VCardActions>
                </VCard>
              </VMenu>
            </VCol>
          </VRow>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn
            variant="text"
            size="small"
            prepend-icon="tabler-x"
            class="clear-btn mx-4"
            @click="resetFilters"
          >
            Clear All
          </VBtn>
          <VBtn
            :loading="loadingFilteredParticipants"
            :disabled="appliedFiltersCount === 0 || loadingFilteredParticipants"
            variant="outlined"
            color="primary"
            @click="applyFilters"
          >
            Apply Filters
          </VBtn>
        </VCardActions>
      </VCard>
    </VExpandTransition>
  </div>
</template>

<style scoped>
  .filter-component {
    width: 100%;
  }
</style>
