# Breadcrumb Components

This package provides two breadcrumb components for the Vue.js pension admin frontend, built on top of Vuetify's `VBreadcrumbs` component.

## Components

### 1. AppBreadcrumbs (Basic)

A simple breadcrumb component that automatically generates breadcrumbs based on the current route.

```vue
<template>
  <AppBreadcrumbs />
</template>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `customItems` | `BreadcrumbItem[]` | `undefined` | Custom breadcrumb items to override auto-generation |
| `showHome` | `boolean` | `true` | Whether to show the home/dashboard link |
| `divider` | `string` | `'tabler-chevron-right'` | Icon to use as divider between items |
| `hideOnSingleItem` | `boolean` | `false` | Hide breadcrumbs when there's only one item |
| `maxItems` | `number` | `6` | Maximum number of breadcrumb items to show |

### 2. AppBreadcrumbsAdvanced (Recommended)

An advanced breadcrumb component that uses the `useBreadcrumbs` composable and supports dynamic title providers for better integration with stores and data.

```vue
<template>
  <AppBreadcrumbsAdvanced 
    :use-participant-names="true"
    :participant-name-provider="getParticipantName"
  />
</template>
```

#### Props

Extends all props from `AppBreadcrumbs` plus:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `useParticipantNames` | `boolean` | `false` | Enable participant name resolution for dynamic routes |
| `useUserNames` | `boolean` | `false` | Enable user name resolution for dynamic routes |
| `participantNameProvider` | `(id: string) => string \| null` | `undefined` | Function to resolve participant names by ID |
| `userNameProvider` | `(id: string) => string \| null` | `undefined` | Function to resolve user names by ID |

## Usage Examples

### Basic Usage

```vue
<script setup>
import AppBreadcrumbs from '@/components/AppBreadcrumbs.vue'
</script>

<template>
  <div>
    <AppBreadcrumbs />
    <!-- Your page content -->
  </div>
</template>
```

### Advanced Usage with Participant Names

```vue
<script setup>
import AppBreadcrumbsAdvanced from '@/components/AppBreadcrumbsAdvanced.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'

const pensionStore = usePensionStore()

// Function to get participant name for breadcrumbs
const getParticipantName = (id: string) => {
  const participant = pensionStore.getParticipantById(id)
  if (participant?.personalInfo) {
    return `${participant.personalInfo.firstName} ${participant.personalInfo.lastName}`
  }
  return `Participant ${id}`
}
</script>

<template>
  <div>
    <AppBreadcrumbsAdvanced 
      :use-participant-names="true"
      :participant-name-provider="getParticipantName"
    />
    <!-- Your page content -->
  </div>
</template>
```

### Custom Breadcrumbs

```vue
<script setup>
import AppBreadcrumbs from '@/components/AppBreadcrumbs.vue'

const customBreadcrumbs = [
  { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
  { title: 'Custom Section', to: '/custom', icon: 'tabler-settings' },
  { title: 'Current Page', disabled: true, icon: 'tabler-file' }
]
</script>

<template>
  <div>
    <AppBreadcrumbs :custom-items="customBreadcrumbs" />
    <!-- Your page content -->
  </div>
</template>
```

### Using the Composable Directly

For more advanced use cases, you can use the `useBreadcrumbs` composable directly:

```vue
<script setup>
import { useBreadcrumbs } from '@/composables/breadcrumbs/useBreadcrumbs'

const { breadcrumbs, addDynamicTitleProvider, addRouteTitle } = useBreadcrumbs({
  showHome: true,
  maxItems: 5
})

// Add custom route titles
addRouteTitle('/my-custom-route', 'My Custom Page', 'tabler-star')

// Add dynamic title provider
addDynamicTitleProvider('organization', (route) => {
  const orgId = route.params.orgId
  // Fetch organization name from store/API
  return `Organization ${orgId}`
})
</script>

<template>
  <div>
    <!-- Use breadcrumbs.value -->
    <VBreadcrumbs :items="breadcrumbs" />
  </div>
</template>
```

## Route Configuration

The breadcrumb components automatically generate titles based on route paths. You can configure custom titles by updating the route mappings:

### Default Route Titles

```typescript
const routeTitles: Record<string, string> = {
  '/': 'Dashboard',
  '/participants': 'Participants',
  '/participant-export': 'Export',
  '/change-proposals': 'Change Proposals',
  '/certification-management': 'Certification',
  '/pension-parameters': 'Pension Parameters',
  '/notifications': 'Notifications',
  '/users': 'User Management'
}
```

### Route Icons

```typescript
const routeIcons: Record<string, string> = {
  '/': 'tabler-dashboard',
  '/participants': 'tabler-users',
  '/participant-export': 'tabler-download',
  '/change-proposals': 'tabler-file-text',
  '/certification-management': 'tabler-certificate',
  '/pension-parameters': 'tabler-settings',
  '/notifications': 'tabler-bell',
  '/users': 'tabler-user-cog'
}
```

## Styling

Both components come with responsive styling that adapts to different screen sizes. The breadcrumbs use Vuetify's theme colors and follow the app's design system.

### Customization

You can customize the appearance by overriding the CSS classes:

```scss
.app-breadcrumbs {
  .breadcrumb-item {
    // Custom styles for breadcrumb items
  }
  
  .breadcrumb-title {
    // Custom styles for breadcrumb text
    max-width: 150px; // Custom max width
  }
}
```

## Automatic Path Depth Detection

The breadcrumb components automatically show/hide based on navigation depth:

- **Hidden** on single-level pages: `/`, `/participants`, `/users`
- **Shown** on multi-level pages: `/participants/add`, `/participants/123`, `/participants/123/export`

This provides a clean UX where breadcrumbs only appear when they're actually useful for navigation.

## Best Practices

1. **Use AppBreadcrumbsAdvanced** for pages with dynamic routes (like participant details)
2. **Provide name providers** to show meaningful names instead of IDs
3. **Keep breadcrumb titles short** - they truncate automatically but shorter is better
4. **Test on mobile** - breadcrumbs are responsive but verify on small screens
5. **Use consistent icons** - stick to the Tabler icon set for consistency
6. **Breadcrumbs appear automatically** - no need to manually show/hide based on page depth

## Integration with Stores

For best results, integrate breadcrumbs with your Pinia stores:

```typescript
// In your page component
const getParticipantName = (id: string) => {
  // Try to get from store first
  const participant = participantStore.getById(id)
  if (participant) {
    return participant.fullName
  }
  
  // If not in store, you could dispatch an action to load it
  participantStore.loadParticipant(id)
  
  // Return fallback while loading
  return `Participant ${id}`
}
```

This ensures users see meaningful breadcrumbs even when navigating directly to deep pages via URL. 