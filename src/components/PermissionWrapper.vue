<script setup lang="ts">
import { computed } from 'vue'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

interface Props {
  requiredAction?: string
  requiredRoute?: string
  showFallback?: boolean
  fallbackMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  requiredAction: '',
  requiredRoute: '',
  showFallback: false,
  fallbackMessage: 'Access restricted',
})

const { canPerformAction, canAccessRoute } = useRoleAccess()

const hasPermission = computed(() => {
  if (props.requiredAction)
    return canPerformAction(props.requiredAction)

  if (props.requiredRoute)
    return canAccessRoute(props.requiredRoute)

  return true
})
</script>

<template>
  <div
    v-if="hasPermission"
    class="permission-wrapper"
  >
    <slot />
  </div>
  <div
    v-else-if="showFallback"
    class="permission-fallback"
  >
    <slot name="fallback">
      <div class="permission-fallback-content">
        <VIcon
          icon="tabler-lock"
          size="16"
          class="mr-2"
        />
        <span class="text-caption text-disabled">{{ fallbackMessage }}</span>
      </div>
    </slot>
  </div>
</template>

<style scoped>
.permission-wrapper {
  display: contents;
}

.permission-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.permission-fallback-content {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}
</style>
