<script setup lang="ts">
import { computed, ref } from 'vue'
import { useMutation } from '@vue/apollo-composable'
import { useAuthStore } from '@/stores/auth/authStore'
import { useAppStore } from '@/stores/app/appStore'
import { CREATE_PENSION_PARAMETERS } from '@/api/graphql/mutations/pensionParameterMutations'
import type { CreatePensionParametersInput } from '@/gql/graphql'

const props = defineProps({
  modelValue: { type: Boolean, required: true },
})

const emit = defineEmits(['update:modelValue', 'created'])

const authStore = useAuthStore()
const appStore = useAppStore()

// Form data
const formData = ref({
  accrualPercentage: null as number | null,
  annualMultiplier: null as number | null,
  offsetAmount: null as number | null,
  partnersPensionPercentage: null as number | null,
  retirementAge: null as number | null,
  voluntaryContributionInterestRate: null as number | null,
  minimumPensionBase: null as number | null,
  year: '',
  effectiveDate: null as Date | null,
})

// Date picker state
const dateMenu = ref(false)

// Mutation
const { mutate: createPensionParameters, loading: creatingPensionParameters } = useMutation(CREATE_PENSION_PARAMETERS)

// Validation rules
const rules = {
  required: (v: any) => !!v || 'This field is required',
  percentage: (v: number) => (v >= 0 && v <= 100) || 'Percentage must be between 0 and 100',
  positiveNumber: (v: number) => v > 0 || 'Value must be greater than 0',
  year: (v: string) => /^\d{4}$/.test(v) || 'Year must be a valid 4-digit year',
  retirementAge: (v: number) => (v >= 18 && v <= 100) || 'Retirement age must be between 18 and 100',
}

// Form validation
const isFormValid = computed(() => {
  return formData.value.accrualPercentage !== null
         && formData.value.annualMultiplier !== null
         && formData.value.offsetAmount !== null
         && formData.value.partnersPensionPercentage !== null
         && formData.value.retirementAge !== null
         && formData.value.voluntaryContributionInterestRate !== null
         && formData.value.minimumPensionBase !== null
         && formData.value.year
         && formData.value.effectiveDate
         && authStore.userId
})

// Convert percentage inputs to decimal values (divide by 100)
const getPercentageAsDecimal = (value: number | null): number => {
  return value ? value / 100 : 0
}

// Reset form
const resetForm = () => {
  formData.value = {
    accrualPercentage: null,
    annualMultiplier: null,
    offsetAmount: null,
    partnersPensionPercentage: null,
    retirementAge: null,
    voluntaryContributionInterestRate: null,
    minimumPensionBase: null,
    year: '',
    effectiveDate: null,
  }
}

// Close dialog
const closeDialog = () => {
  emit('update:modelValue', false)
  resetForm()
}

// Create pension parameters
const handleSubmit = async () => {
  if (!isFormValid.value) {
    appStore.showSnack('Please fill all required fields correctly')

    return
  }

  try {
    const input: CreatePensionParametersInput = {
      accrualPercentage: getPercentageAsDecimal(formData.value.accrualPercentage),
      annualMultiplier: formData.value.annualMultiplier!,
      offsetAmount: formData.value.offsetAmount!,
      partnersPensionPercentage: getPercentageAsDecimal(formData.value.partnersPensionPercentage),
      retirementAge: formData.value.retirementAge!,
      voluntaryContributionInterestRate: getPercentageAsDecimal(formData.value.voluntaryContributionInterestRate),
      minimumPensionBase: formData.value.minimumPensionBase!,
      year: formData.value.year,
      effectiveDate: formData.value.effectiveDate!.toISOString(),
      userId: authStore.claims.pensionUserId,
    }

    await createPensionParameters({ createPensionParametersInput: input })

    appStore.showSnack('Pension parameters created successfully')
    emit('created')
    closeDialog()
  }
  catch (error) {
    console.error('Error creating pension parameters:', error)
    appStore.showSnack(error.message)
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 700"
    :model-value="modelValue"
    @update:model-value="(value) => emit('update:modelValue', value)"
  >
    <VCard class="pa-4">
      <VCardTitle class="text-h5 pb-2">
        Create Pension Parameters
      </VCardTitle>
      <VCardSubtitle class="mb-4">
        Add new pension parameters for a specific year
      </VCardSubtitle>

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <!-- Year -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model="formData.year"
                label="Year *"
                placeholder="e.g., 2024"
                variant="outlined"
                density="comfortable"
                :rules="[rules.required, rules.year]"
              />
            </VCol>

            <!-- Effective Date -->
            <VCol
              cols="12"
              md="6"
            >
              <VMenu
                v-model="dateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template #activator="{ props }">
                  <VTextField
                    :model-value="formData.effectiveDate ? formData.effectiveDate.toLocaleDateString() : ''"
                    label="Effective Date *"
                    variant="outlined"
                    density="comfortable"
                    readonly
                    v-bind="props"
                    prepend-inner-icon="tabler-calendar"
                    :rules="[rules.required]"
                  />
                </template>
                <VDatePicker
                  v-model="formData.effectiveDate"
                  color="primary"
                  @update:model-value="dateMenu = false"
                />
              </VMenu>
            </VCol>

            <!-- Accrual Percentage -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.accrualPercentage"
                label="Accrual Percentage * (%)"
                placeholder="e.g., 1.75"
                variant="outlined"
                density="comfortable"
                type="number"
                step="0.01"
                suffix="%"
                :rules="[rules.required, rules.percentage]"
              />
              <div class="text-caption text-grey mt-1">
                Percentage of pensionable salary accrued as pension each year
              </div>
            </VCol>

            <!-- Partners Pension Percentage -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.partnersPensionPercentage"
                label="Partners Pension Percentage * (%)"
                placeholder="e.g., 70"
                variant="outlined"
                density="comfortable"
                type="number"
                step="0.01"
                suffix="%"
                :rules="[rules.required, rules.percentage]"
              />
              <div class="text-caption text-grey mt-1">
                Percentage of pension that goes to partner in case of death
              </div>
            </VCol>

            <!-- Voluntary Contribution Interest Rate -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.voluntaryContributionInterestRate"
                label="Voluntary Contribution Interest Rate * (%)"
                placeholder="e.g., 4"
                variant="outlined"
                density="comfortable"
                type="number"
                step="0.01"
                suffix="%"
                :rules="[rules.required, rules.percentage]"
              />
              <div class="text-caption text-grey mt-1">
                Interest rate applied to voluntary contributions
              </div>
            </VCol>

            <!-- Annual Multiplier -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.annualMultiplier"
                label="Annual Multiplier *"
                placeholder="e.g., 12"
                variant="outlined"
                density="comfortable"
                type="number"
                step="0.01"
                :rules="[rules.required, rules.positiveNumber]"
              />
              <div class="text-caption text-grey mt-1">
                Factor to calculate annual pensionable salary from monthly salary
              </div>
            </VCol>

            <!-- Offset Amount -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.offsetAmount"
                label="Offset Amount *"
                placeholder="e.g., 15000"
                variant="outlined"
                density="comfortable"
                type="number"
                step="0.01"
                prefix="Afl."
                :rules="[rules.required, rules.positiveNumber]"
              />
              <div class="text-caption text-grey mt-1">
                Amount deducted from pensionable salary before calculating pension accrual
              </div>
            </VCol>

            <!-- Minimum Pension Base -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.minimumPensionBase"
                label="Minimum Pension Base *"
                placeholder="e.g., 5000"
                variant="outlined"
                density="comfortable"
                type="number"
                step="0.01"
                prefix="Afl."
                :rules="[rules.required, rules.positiveNumber]"
              />
              <div class="text-caption text-grey mt-1">
                If gross fulltime annual salary minus offset is less than this amount, pension base is set to this minimum
              </div>
            </VCol>

            <!-- Retirement Age -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model.number="formData.retirementAge"
                label="Retirement Age *"
                placeholder="e.g., 65"
                variant="outlined"
                density="comfortable"
                type="number"
                :rules="[rules.required, rules.retirementAge]"
              />
              <div class="text-caption text-grey mt-1">
                Age at which participant is eligible for full pension benefits
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VCardActions class="pb-4 px-4">
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          min-width="100"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          variant="elevated"
          color="primary"
          :loading="creatingPensionParameters"
          min-width="100"
          :disabled="!isFormValid || creatingPensionParameters"
          class="ml-3"
          @click="handleSubmit"
        >
          Create
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
