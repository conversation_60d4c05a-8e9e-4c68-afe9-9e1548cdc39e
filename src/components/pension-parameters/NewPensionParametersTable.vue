<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'

const appStore = useAppStore()
const { canReviewPensionParameters } = useRoleAccess()

const { 
  state: { pendingPensionParameters, loadingPendingPensionParameters }, 
  actions: { refetchPendingPensionParameters, approvePensionParameters, rejectPensionParameters } 
} = usePensionParameters()

const { subscribeToNotifications } = usePusherNotifications()

const rejectDialog = ref(false)
const selectedParameter = ref<any>(null)
const rejectReason = ref('')

const parameterDefinitions: Record<string, {
  title: string
  formatter: (value: number | null | undefined) => string | number
}> = {
  accrualPercentage: {
    title: 'Accrual Percentage',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `${(value * 100).toFixed(2)}%`
    },
  },
  annualMultiplier: {
    title: 'Annual Multiplier',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return value
    },
  },
  offsetAmount: {
    title: 'Offset Amount',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `Afl. ${value.toLocaleString()}`
    },
  },
  partnersPensionPercentage: {
    title: 'Partners Pension Percentage',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `${(value * 100).toFixed(2)}%`
    },
  },
  retirementAge: {
    title: 'Retirement Age',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return value
    },
  },
  voluntaryContributionInterestRate: {
    title: 'Voluntary Contribution Interest Rate',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `${(value * 100).toFixed(2)}%`
    },
  },
  minimumPensionBase: {
    title: 'Minimum Pension Base',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `Afl. ${value.toLocaleString()}`
    },
  },
}

const tableHeaders = [
  { title: 'YEAR', key: 'year', width: '120px' },
  { title: 'ACCRUAL %', key: 'accrualPercentage', width: '120px' },
  { title: 'ANNUAL MULTIPLIER', key: 'annualMultiplier', width: '150px' },
  { title: 'OFFSET AMOUNT', key: 'offsetAmount', width: '150px' },
  { title: 'PARTNERS %', key: 'partnersPensionPercentage', width: '120px' },
  { title: 'RETIREMENT AGE', key: 'retirementAge', width: '140px' },
  { title: 'VC INTEREST %', key: 'voluntaryContributionInterestRate', width: '140px' },
  { title: 'MIN PENSION BASE', key: 'minimumPensionBase', width: '160px' },
  { title: 'CREATED BY', key: 'createdBy', width: '150px' },
  { title: 'CREATED AT', key: 'createdAt', width: '150px' },
  { title: 'STATUS', key: 'status', width: '100px' },
  { title: 'ACTIONS', key: 'actions', sortable: false, width: '200px' },
]

const tableItems = computed(() => {
  if (!pendingPensionParameters.value) return []
  
  return pendingPensionParameters.value.map((param: any) => ({
    id: param.id,
    year: param.year || 'N/A',
    accrualPercentage: parameterDefinitions.accrualPercentage.formatter(param.accrualPercentage),
    annualMultiplier: parameterDefinitions.annualMultiplier.formatter(param.annualMultiplier),
    offsetAmount: parameterDefinitions.offsetAmount.formatter(param.offsetAmount),
    partnersPensionPercentage: parameterDefinitions.partnersPensionPercentage.formatter(param.partnersPensionPercentage),
    retirementAge: parameterDefinitions.retirementAge.formatter(param.retirementAge),
    voluntaryContributionInterestRate: parameterDefinitions.voluntaryContributionInterestRate.formatter(param.voluntaryContributionInterestRate),
    minimumPensionBase: parameterDefinitions.minimumPensionBase.formatter(param.minimumPensionBase),
    createdBy: param.updatedBy?.displayName || 'N/A',
    createdAt: param.createdAt ? new Date(param.createdAt).toLocaleDateString() : 'N/A',
    status: param.status || 'pending',
    rejectReason: param.rejectReason,
    raw: param,
  }))
})

const handleApprove = async (item: any) => {
  try {
    await approvePensionParameters(item.id)
    appStore.showSnack('Pension parameters approved successfully', 'success')
    await refetchPendingPensionParameters()
  } catch (error) {
    console.error('Error approving pension parameters:', error)
    appStore.showSnack('Failed to approve pension parameters', 'error')
  }
}

const openRejectDialog = (item: any) => {
  selectedParameter.value = item
  rejectReason.value = ''
  rejectDialog.value = true
}

const handleReject = async () => {
  if (!selectedParameter.value || !rejectReason.value.trim()) {
    appStore.showSnack('Please provide a reject reason', 'error')
    return
  }

  try {
    await rejectPensionParameters(selectedParameter.value.id, rejectReason.value.trim())
    appStore.showSnack('Pension parameters rejected successfully', 'success')
    await refetchPendingPensionParameters()
    closeRejectDialog()
  } catch (error) {
    console.error('Error rejecting pension parameters:', error)
    appStore.showSnack('Failed to reject pension parameters', 'error')
  }
}

const closeRejectDialog = () => {
  rejectDialog.value = false
  selectedParameter.value = null
  rejectReason.value = ''
}

onMounted(async () => {
  await refetchPendingPensionParameters()

  subscribeToNotifications(notification => {
    if (notification.entityType === 'PensionParameters') {
      refetchPendingPensionParameters()
    }
  })
})
</script>

<template>
  <div>
    <VCard>
      <VCardTitle class="d-flex justify-space-between align-center pa-4">
        <span>New Pension Parameters</span>
        <VChip
          :color="tableItems.length > 0 ? 'warning' : 'success'"
          variant="tonal"
        >
          {{ tableItems.length }} Pending
        </VChip>
      </VCardTitle>

      <VCardText>
        <VDataTable
          :headers="tableHeaders"
          :items="tableItems"
          :loading="loadingPendingPensionParameters"
          class="elevation-0"
          density="compact"
          :items-per-page="10"
        >
          <template #item.status="{ item }">
            <VChip
              :color="item.status === 'pending' ? 'warning' : item.status === 'rejected' ? 'error' : 'success'"
              variant="tonal"
              size="small"
            >
              {{ item.status }}
            </VChip>
          </template>

          <template #item.actions="{ item }">
            <div class="d-flex gap-2">
              <VBtn
                v-if="canReviewPensionParameters && item.status === 'pending'"
                color="success"
                variant="tonal"
                size="small"
                prepend-icon="tabler-check"
                @click="handleApprove(item)"
              >
                Approve
              </VBtn>
              
              <VBtn
                v-if="canReviewPensionParameters && item.status === 'pending'"
                color="error"
                variant="tonal"
                size="small"
                prepend-icon="tabler-x"
                @click="openRejectDialog(item)"
              >
                Reject
              </VBtn>

              <VTooltip
                v-if="item.status === 'rejected' && item.rejectReason"
                location="top"
              >
                <template #activator="{ props }">
                  <VBtn
                    v-bind="props"
                    color="info"
                    variant="tonal"
                    size="small"
                    icon="tabler-info-circle"
                  />
                </template>
                <span>{{ item.rejectReason }}</span>
              </VTooltip>
            </div>
          </template>

          <template #no-data>
            <div class="text-center pa-4">
              <VIcon
                size="48"
                color="grey-lighten-1"
                class="mb-2"
              >
                tabler-database-off
              </VIcon>
              <p class="text-grey-lighten-1">
                No pending pension parameters found
              </p>
            </div>
          </template>
        </VDataTable>
      </VCardText>
    </VCard>

    <!-- Reject Dialog -->
    <VDialog
      v-model="rejectDialog"
      max-width="500px"
      persistent
    >
      <VCard>
        <VCardTitle class="d-flex align-center gap-2">
          <VIcon color="error">tabler-x</VIcon>
          Reject Pension Parameters
        </VCardTitle>

        <VCardText>
          <p class="mb-4">
            Are you sure you want to reject the pension parameters for year 
            <strong>{{ selectedParameter?.year }}</strong>?
          </p>

          <VTextarea
            v-model="rejectReason"
            label="Reject Reason"
            placeholder="Please provide a reason for rejection..."
            rows="3"
            variant="outlined"
            :rules="[v => !!v || 'Reject reason is required']"
          />
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn
            color="grey"
            variant="text"
            @click="closeRejectDialog"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            variant="elevated"
            :disabled="!rejectReason.trim()"
            @click="handleReject"
          >
            Reject
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>