<script setup lang="ts">
import { ref } from 'vue'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'

import { useAppStore } from '@/stores/app/appStore'

const { state: { pensionParamsTransformed, allPensionParameters } } = usePensionParameters()

const appStore = useAppStore()

// Define our calculation years
const years = ['2023', '2024', '2025']
const currentYear = new Date().getFullYear().toString()

// Status indicators
const CERTIFIED = 'CERTIFIED'
const NOT_STARTED = 'CERTIFICATION NOT STARTED'

// Reference dates for indexation
const indexationReferenceDates = {
  2023: 'Dec 31, 2022',
  2024: 'Jan 1, 2024',
  2025: 'Jan 1, 2025',
}

// Reference dates for accrual
const accrualReferenceDates = {
  2023: 'Dec 30, 2023',
  2024: '12/26/2024',
  2025: '03/12/2025',
}

// Indexation data
const indexationData = {
  'OP-TE': { 2023: 'Afl. 10,00', 2024: '30.00', 2025: '30.00' },
  'WP-TE': { 2023: 'Afl. 20,00', 2024: '210.00', 2025: '210.00' },
  'ONP-TE': { 2023: 'Afl. 30,00', 2024: '3.00', 2025: '3.00' },
  'AOP-TE': { 2023: 'Afl. 60,00', 2024: '30.00', 2025: '30.00' },
}

// Accrual data
const accrualData = {
  'Start date of participation': { 2023: 'Nov 30, 2004', 2024: 'Dec 31, 2019', 2025: 'Dec 31, 2019' },
  'End date of employment': { 2023: '-', 2024: 'May 11, 2025', 2025: 'May 11, 2025' },
  'Birth date': { 2023: 'Mar 31, 1950', 2024: 'Jul 31, 1982', 2025: 'Jul 31, 1982' },
  'AOV age': { 2023: '65', 2024: '65', 2025: '65' },
  'Retirement date': { 2023: 'Mar 31, 2015', 2024: 'Jul 31, 2047', 2025: 'Jul 31, 2047' },
  'Period during calculation year': { 2023: '1.00', 2024: '0.99', 2025: '0.20' },
  'Period after reference date': { 2023: '-8.75', 2024: '22.60', 2025: '22.39' },
}

// Year statuses
const yearStatus = {
  2023: CERTIFIED,
  2024: NOT_STARTED,
  2025: '',
}

// UI interaction states
const indexationExpanded = ref(true)
const accrualExpanded = ref(true)

// Calculation years inputs
const indexationCalcYears = ref({
  2023: '2023',
  2024: '2024',
  2025: '2025',
})

const accrualCalcYears = ref({
  2023: '2023',
  2024: '2024',
  2025: '2025',
})

const getStatusClass = (year: string) => {
  if (yearStatus[year] === CERTIFIED)
    return 'certified'
  if (yearStatus[year] === NOT_STARTED)
    return 'not-started'

  return ''
}
</script>

<template>
  <div>
    <VCard>
      <VCardText>
        <div class="pension-calculation-container">
          <!-- Indexation Section -->
          <div class="calculation-section">
            <div
              class="section-header"
              @click="indexationExpanded = !indexationExpanded"
            >
              <h2>Indexation beginning of calendar year</h2>
              <VIcon>{{ indexationExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</VIcon>
            </div>

            <div
              v-if="indexationExpanded"
              class="section-content"
            >
              <!-- Input Fields Row -->
              <div class="input-row">
                <div class="input-label">
                  Calculation year
                </div>
                <div class="input-fields">
                  <input
                    v-for="year in years"
                    :key="`indexation-year-${year}`"
                    v-model="indexationCalcYears[year]"
                    type="text"
                    class="year-input"
                  >
                </div>
              </div>

              <div class="input-row">
                <div class="input-label">
                  Reference date
                </div>
                <div class="input-fields">
                  <div
                    v-for="year in years"
                    :key="`indexation-ref-${year}`"
                    class="date-field-container"
                  >
                    <select
                      v-if="year === '2023'"
                      v-model="indexationReferenceDates[year]"
                      class="date-select"
                    >
                      <option>Dec 31, 2022</option>
                    </select>
                    <input
                      v-else
                      type="text"
                      :value="indexationReferenceDates[year]"
                      class="date-input"
                    >
                  </div>
                </div>
              </div>

              <!-- Data Table -->
              <div class="data-table">
                <div class="table-header">
                  <div class="header-cell description-cell">
                    DESCRIPTION
                  </div>
                  <div
                    v-for="year in years"
                    :key="`indexation-header-${year}`"
                    class="header-cell"
                  >
                    <div
                      class="status-badge"
                      :class="[getStatusClass(year)]"
                    >
                      {{ yearStatus[year] }}
                    </div>
                    <div>{{ year }}</div>
                  </div>
                </div>

                <div
                  v-for="(values, description) in indexationData"
                  :key="`indexation-row-${description}`"
                  class="table-row"
                >
                  <div class="row-cell description-cell">
                    {{ description }}
                  </div>
                  <div
                    v-for="year in years"
                    :key="`indexation-cell-${description}-${year}`"
                    class="row-cell"
                  >
                    {{ values[year] }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Accrual Period Section -->
          <div class="calculation-section">
            <div
              class="section-header"
              @click="accrualExpanded = !accrualExpanded"
            >
              <h2>Accrual period calculation jjj</h2>
              <VIcon>{{ accrualExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</VIcon>
            </div>

            <div
              v-if="accrualExpanded"
              class="section-content"
            >
              <!-- Input Fields Row -->
              <div class="input-row">
                <div class="input-label">
                  Calculation year
                </div>
                <div class="input-fields">
                  <input
                    v-for="year in years"
                    :key="`accrual-year-${year}`"
                    v-model="accrualCalcYears[year]"
                    type="text"
                    class="year-input"
                  >
                </div>
              </div>

              <div class="input-row">
                <div class="input-label">
                  Reference date
                </div>
                <div class="input-fields">
                  <div
                    v-for="year in years"
                    :key="`accrual-ref-${year}`"
                    class="date-field-container"
                  >
                    <select
                      v-if="year === '2023'"
                      v-model="accrualReferenceDates[year]"
                      class="date-select"
                    >
                      <option>Dec 30, 2023</option>
                    </select>
                    <input
                      v-else
                      type="text"
                      :value="accrualReferenceDates[year]"
                      class="date-input"
                    >
                  </div>
                </div>
              </div>

              <!-- Data Table -->
              <div class="data-table">
                <div class="table-header">
                  <div class="header-cell description-cell">
                    DESCRIPTION
                  </div>
                  <div
                    v-for="year in years"
                    :key="`accrual-header-${year}`"
                    class="header-cell"
                  >
                    <div
                      class="status-badge"
                      :class="[getStatusClass(year)]"
                    >
                      {{ yearStatus[year] }}
                    </div>
                    <div>{{ year }}</div>
                  </div>
                </div>

                <div
                  v-for="(values, description) in accrualData"
                  :key="`accrual-row-${description}`"
                  class="table-row"
                >
                  <div class="row-cell description-cell">
                    {{ description }}
                  </div>
                  <div
                    v-for="year in years"
                    :key="`accrual-cell-${description}-${year}`"
                    class="row-cell"
                  >
                    {{ values[year] }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
  .pension-calculation-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
  }

  .calculation-section {
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    margin-bottom: 16px;
    overflow: hidden;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #f9f9f9;
    border-bottom: 2px solid #4169e1;
    cursor: pointer;
  }

  .section-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .section-content {
    padding: 20px;
  }

  .input-row {
    display: flex;
    margin-bottom: 16px;
  }

  .input-label {
    width: 160px;
    font-weight: 500;
    padding-top: 8px;
  }

  .input-fields {
    display: flex;
    flex: 1;
    gap: 16px;
  }

  .year-input, .date-input, .date-select {
    flex: 1;
    min-width: 0;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
  }

  .date-field-container {
    flex: 1;
    position: relative;
  }

  .date-select {
    width: 100%;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24'%3E%3Cpath fill='%23333' d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
  }

  .data-table {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 16px;
  }

  .table-header {
    display: flex;
    background-color: #f9f9f9;
    font-weight: 600;
  }

  .table-row {
    display: flex;
    border-top: 1px solid #e0e0e0;
  }

  .header-cell, .row-cell {
    flex: 1;
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .description-cell {
    width: 200px;
    flex: none;
  }

  .status-badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    margin-bottom: 4px;
    text-align: center;
    font-weight: 500;
    max-width: fit-content;
  }

  .certified {
    background-color: rgba(46, 204, 113, 0.2);
    color: #27ae60;
  }

  .not-started {
    background-color: rgba(241, 196, 15, 0.2);
    color: #f39c12;
  }
</style>
