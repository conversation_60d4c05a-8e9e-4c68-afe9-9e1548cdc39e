<script setup lang="ts">
import { computed, ref } from 'vue'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import PensionCodeDialog from '@/components/participants/pensionInfo/PensionCodeDialog.vue'
import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'

const props = defineProps({
  showRejectOptions: {
    type: Boolean,
    default: true,
  },
  editorView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['field-rejected'])
const { state: { participantPensionInfo, participantDetails, loadingParticipant }, actions: { refetchSingleParticipant } } = useParticipants()
const appStore = useAppStore()

const rejectDialog = ref(false)
const fieldToReject = ref(null)

// Get rejected fields with reasons for editor view
const rejectedFieldsWithReasons = computed(() => {
  if (!props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string; reason: string }> = []

  // Check pension info certification reject reasons
  if (participantPensionInfo.value && (participantPensionInfo.value as any).certificationRejectReason) {
    (participantPensionInfo.value as any).certificationRejectReason.forEach((item: any) => {
      if (item.reason && item.status === 'VALID' && isPensionField(item.field)) {
        rejectedFieldsArray.push({
          field: fieldKeyToName(item.field),
          fieldKey: item.field,
          reason: item.reason,
        })
      }
    })
  }

  // Also check personal info for pension-related rejections (fallback)
  const certificationRejectReasonField = participantDetails.value.find((item: any) => item.field === 'certificationRejectReason')
  if (certificationRejectReasonField?.value && Array.isArray(certificationRejectReasonField.value)) {
    certificationRejectReasonField.value.forEach((item: any) => {
      if (item.reason && item.status === 'VALID' && isPensionField(item.field)) {
        rejectedFieldsArray.push({
          field: fieldKeyToName(item.field),
          fieldKey: item.field,
          reason: item.reason,
        })
      }
    })
  }

  return rejectedFieldsArray
})

// Check if a field is pension-related
function isPensionField(fieldKey: string): boolean {
  const pensionFields = ['code', 'codeDescription', 'codeEffectiveDate', 'codeImpact', 'previousCode', 'previousCodeEffectiveDate']

  return pensionFields.includes(fieldKey)
}

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

const pensionInfo = computed(() => {
  if (!participantPensionInfo.value)
    return []
  const info = participantPensionInfo.value as any
  const pendingChanges = info.pendingChanges || []

  return [
    {
      field: 'code',
      name: 'Code',
      value: info.code?.toString() || 'N/A',
      disabled: pendingChanges.includes('code'),
      hasPendingChanges: pendingChanges.includes('code'),
      id: info.id,
      typename: 'PensionInfo',
    },
    {
      field: 'codeDescription',
      name: 'Code Description',
      value: info.codeDescription || 'N/A',
      disabled: pendingChanges.includes('codeDescription'),
      hasPendingChanges: pendingChanges.includes('codeDescription'),
      id: info.id,
      typename: 'PensionInfo',
    },
  ]
})

const refreshing = ref(false)

const openRejectDialog = (item: any) => {
  if (!props.showRejectOptions || refreshing.value || props.editorView)
    return

  fieldToReject.value = item
  rejectDialog.value = true
}

const closeRejectDialog = () => {
  rejectDialog.value = false
  fieldToReject.value = null
}

const handleFieldRejected = async (rejectionData: any) => {
  closeRejectDialog()
  emit('field-rejected', rejectionData)

  // Set refreshing state and show loading feedback
  refreshing.value = true
  appStore.showSnack('Updating field status...')

  // Refresh participant data to get updated values with rejection information
  try {
    await refetchSingleParticipant()
    appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`)
  }
  catch (error) {
    console.error('Error refreshing participant data:', error)
    appStore.showSnack('Error updating field status')
  }
  finally {
    refreshing.value = false
  }
}

const getFieldValue = (fieldName: string) => {
  const field = participantDetails.value.find((item: any) => item.field === fieldName)

  return field ? field.value : ''
}

const fullName = computed(() => {
  const firstName = getFieldValue('firstName')
  const lastName = getFieldValue('lastName')

  return `${firstName} ${lastName}`
})

const editDialog = ref(false)
const pensionCodeDialog = ref(false)

const selectedField = ref({
  field: '',
  value: '',
  id: '',
  typename: '',
} as any)

const openEditDialog = (item: any) => {
  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  }

  selectedField.value = {
    field: item.field,
    value: item.value,
    id: item.id,
    typename: item.typename,
  }

  if (item.field === 'code')
    pensionCodeDialog.value = true
  else
    editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
  pensionCodeDialog.value = false
}

const updateField = (newValue: any) => {
  closeEditDialog()
}

const handleCodeChanged = (codeData: any) => {
  closeEditDialog()

  // Additional handling for code changes if needed
}
</script>

<template>
  <VCard class="pension-info-container">
    <div class="header-container">
      <h2 class="text-h4 font-weight-medium">
        Pension Information
      </h2>
    </div>

    <!-- Alerts for rejected fields with reasons (Editor View) -->
    <div
      v-if="rejectedFieldsWithReasons.length > 0 && editorView"
      class="mb-4"
    >
      <VAlert
        v-for="(item, index) in rejectedFieldsWithReasons"
        :key="`rejected-reason-${item.fieldKey}-${index}`"
        type="error"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-x"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.field }}:</strong>
          <span>{{ item.reason }}</span>
          <VChip
            size="small"
            color="error"
            class="ml-auto"
          >
            Needs Fix
          </VChip>
        </div>
      </VAlert>
    </div>

    <div class="pension-info-section">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
        ]"
        :items="pensionInfo"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <!-- Custom row styling for disabled fields -->
        <template #item.name="{ item }">
          <span :class="{ 'dimmed-text': item.disabled }">{{ item.name }}</span>
        </template>

        <template #item.value="{ item }">
          <template v-if="item.disabled">
            <VChip
              size="small"
              color="error"
              variant="tonal"
              class="font-weight-medium"
            >
              {{ item.value }}
              <VTooltip
                activator="parent"
                location="top"
              >
                This field was rejected and is pending review
              </VTooltip>
            </VChip>
          </template>
          <template v-else>
            <span :class="{ 'dimmed-text': item.disabled }">{{ item.value }}</span>
          </template>
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <!-- Show warning icon for rejected/disabled fields -->
            <template v-if="item.disabled">
              <VIcon
                size="16"
                icon="tabler-alert-triangle"
                color="error"
              >
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  This field was rejected and is pending review
                </VTooltip>
              </VIcon>
            </template>

            <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
            <template v-else-if="showRejectOptions && !editorView && !item.disabled && !refreshing && item.field !== 'codeDescription'">
              <VBtn
                icon
                size="small"
                variant="text"
                color="error"
                @click="openRejectDialog(item)"
              >
                <VIcon
                  size="16"
                  icon="tabler-x"
                  class="reject-icon"
                />
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  Reject this field
                </VTooltip>
              </VBtn>
            </template>

            <!-- Show loading spinner while refreshing (reviewer view only) -->
            <template v-else-if="showRejectOptions && !editorView && !item.disabled && refreshing">
              <VBtn
                icon
                size="small"
                variant="text"
                disabled
              >
                <VIcon
                  size="16"
                  icon="tabler-loader-2"
                  class="spinning"
                  color="primary"
                />
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  Updating field status...
                </VTooltip>
              </VBtn>
            </template>
          </div>
        </template>
      </VDataTable>
    </div>

    <div
      v-if="!pensionInfo.length"
      class="text-caption text-disabled pa-2"
    >
      No pension information available.
    </div>

    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />

    <PensionCodeDialog
      v-if="pensionCodeDialog"
      v-model="pensionCodeDialog"
      :previous-code="(participantPensionInfo.value as any)?.previousCode"
      :current-code="(selectedField as any).value ? parseInt((selectedField as any).value) : null"
      :participant-id="selectedField.id"
      :pension-info-id="selectedField.id"
      @close="closeEditDialog"
      @code-changed="handleCodeChanged"
    />

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="fullName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />
  </VCard>
</template>

<style scoped>
  .pension-info-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .pension-info-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>
