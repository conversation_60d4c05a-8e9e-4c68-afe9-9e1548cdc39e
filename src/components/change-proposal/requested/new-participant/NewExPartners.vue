<script setup lang="ts">
import { computed, ref } from 'vue'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'
import EditFieldDialog from '@/components/EditFieldDialog.vue'
import RejectedFieldsAlert from '@/components/change-proposal/RejectedFieldsAlert.vue'
import { ChangeType } from '@/gql/graphql'

const props = defineProps({
  showRejectOptions: {
    type: Boolean,
    default: true,
  },
  editorView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['field-rejected'])
const { state: { participantExPartnerInfo, participantDetails, loadingParticipant }, actions: { refetchSingleParticipant } } = useParticipants()
const appStore = useAppStore()

const editDialog = ref(false)

const selectedField = ref({
  field: '',
  value: '',
  id: '',
  partnerIndex: 0,
  typename: '',
})

const rejectDialog = ref(false)
const fieldToReject = ref(null)

// Edit dialog for rejected fields in editor view
const editRejectedDialog = ref(false)
const fieldToEdit = ref<any>(null)

// Track recently submitted fields for feedback
const recentlySubmittedFields = ref<Set<string>>(new Set())

// Track permanently submitted fields (until data refresh shows submittedForReview: true)
const permanentlySubmittedFields = ref<Set<string>>(new Set())

// Get rejected fields with reasons for editor view
const rejectedFieldsWithReasons = computed(() => {
  if (!props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string; reason: string; partnerIndex: number; partnerName: string; submittedForReview?: boolean; submittedAt?: string }> = []

  // Check each ex-partner for rejection reasons
  participantExPartnerInfo.value.forEach((partner: any[], partnerIndex: number) => {
    const partnerName = getPartnerName(partner)

    // Find certification reject reasons for this partner
    partner.forEach((item: any) => {
      if (item.field === 'certificationRejectReason' && Array.isArray(item.value)) {
        item.value.forEach((rejectItem: any) => {
          if (rejectItem.reason && rejectItem.status === 'VALID' && isPartnerField(rejectItem.field)) {
            const fieldKey = `${rejectItem.field}_${partnerIndex}` // Include partner index for uniqueness

            rejectedFieldsArray.push({
              field: fieldKeyToName(rejectItem.field),
              fieldKey: rejectItem.field,
              reason: rejectItem.reason,
              partnerIndex,
              partnerName,
              submittedForReview: rejectItem.submittedForReview || permanentlySubmittedFields.value.has(fieldKey),
              submittedAt: rejectItem.submittedAt,
            })
          }
        })
      }
    })
  })

  return rejectedFieldsArray
})

// Watch for changes in rejected fields to clean up permanently submitted fields
// when the backend properly reflects submittedForReview: true
watch(rejectedFieldsWithReasons, newRejectedFields => {
  // Clean up permanently submitted fields that now have submittedForReview: true from backend
  newRejectedFields.forEach(field => {
    const fieldKey = `${field.fieldKey}_${field.partnerIndex}`
    if (field.submittedForReview === true && permanentlySubmittedFields.value.has(fieldKey))
      permanentlySubmittedFields.value.delete(fieldKey)
  })
}, { deep: true })

// Get rejected fields based on pendingChanges array (for reviewer view)
const rejectedFields = computed(() => {
  if (!props.showRejectOptions || props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string; partnerIndex: number; partnerName: string }> = []

  // Check each ex-partner for pending changes
  participantExPartnerInfo.value.forEach((partner: any[], partnerIndex: number) => {
    const partnerName = getPartnerName(partner)

    partner.forEach((item: any) => {
      if (item.field === 'pendingChanges' && Array.isArray(item.value)) {
        item.value.forEach((fieldKey: string) => {
          if (isPartnerField(fieldKey)) {
            rejectedFieldsArray.push({
              field: fieldKeyToName(fieldKey),
              fieldKey,
              partnerIndex,
              partnerName,
            })
          }
        })
      }
    })
  })

  return rejectedFieldsArray
})

// Check if a field is partner-related
function isPartnerField(fieldKey: string): boolean {
  const partnerFields = ['firstName', 'lastName', 'dateOfBirth', 'startDate', 'isDeceased']

  return partnerFields.includes(fieldKey)
}

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

function getPartnerName(partner: any[]): string {
  const firstNameField = partner.find(item => item.field === 'firstName')
  const lastNameField = partner.find(item => item.field === 'lastName')
  const firstName = firstNameField?.value || ''
  const lastName = lastNameField?.value || ''

  return `${firstName} ${lastName}`.trim() || 'Ex-Partner'
}

const filteredPartners = computed(() => {
  return participantExPartnerInfo.value.map(partner =>
    partner.filter(item => item.field !== 'id' && item.field !== 'isCurrent' && item.field !== 'pendingChanges' && item.field !== 'certificationRejectReason'),
  )
})

const refreshing = ref(false)

const openEditDialog = (item: any, partnerIndex: number) => {
  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  }

  selectedField.value = {
    field: item.field,
    value: item.value,
    id: item.id,
    partnerIndex,
    typename: item.typename,
  }
  editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
}

const updateField = (newValue: any) => {
  closeEditDialog()
}

const openRejectDialog = (item: any, partnerIndex: number) => {
  if (!props.showRejectOptions || refreshing.value || props.editorView)
    return

  // Add partnerIndex to the field item for context
  fieldToReject.value = { ...item, partnerIndex }
  rejectDialog.value = true
}

const closeRejectDialog = () => {
  rejectDialog.value = false
  fieldToReject.value = null
}

// Functions for editing rejected fields in editor view
const openEditDialogForRejectedField = (rejectedFieldItem: any) => {
  if (!props.editorView)
    return

  // Find the actual field data from filteredPartners
  const partnerData = filteredPartners.value[rejectedFieldItem.partnerIndex]
  const fieldData = partnerData?.find((item: any) => item.field === rejectedFieldItem.fieldKey)

  if (fieldData) {
    fieldToEdit.value = {
      ...fieldData,
      rejectedFieldInfo: rejectedFieldItem,
      partnerIndex: rejectedFieldItem.partnerIndex,
    }
    editRejectedDialog.value = true
  }
}

const closeEditRejectedDialog = () => {
  editRejectedDialog.value = false
  fieldToEdit.value = null
}

const handleRejectedFieldUpdate = async () => {
  if (!fieldToEdit.value)
    return

  const fieldKey = fieldToEdit.value.rejectedFieldInfo.fieldKey
  const fieldName = fieldToEdit.value.rejectedFieldInfo.field
  const partnerIndex = fieldToEdit.value.rejectedFieldInfo.partnerIndex
  const uniqueFieldKey = `${fieldKey}_${partnerIndex}` // Include partner index for uniqueness

  closeEditRejectedDialog()

  // Show loading feedback
  refreshing.value = true
  appStore.showSnack('Submitting change proposal...')

  try {
    // The EditFieldDialog will handle creating a change proposal
    // When approved, this will update the field and clear rejection reasons
    await refetchSingleParticipant()

    // Mark field as recently submitted for feedback (temporary)
    recentlySubmittedFields.value.add(uniqueFieldKey)

    // Mark field as permanently submitted until backend reflects the change
    permanentlySubmittedFields.value.add(uniqueFieldKey)

    // Show success feedback
    appStore.showSnack(`✅ Change proposal submitted for "${fieldName}"! It will be reviewed and applied once approved.`)

    // Remove from recently submitted after 5 seconds (but keep in permanently submitted)
    setTimeout(() => {
      recentlySubmittedFields.value.delete(uniqueFieldKey)
    }, 5000)
  }
  catch (error) {
    console.error('Error submitting change proposal:', error)
    appStore.showSnack('❌ Error submitting change proposal')
  }
  finally {
    refreshing.value = false
  }
}

const handleFieldRejected = async (rejectionData: any) => {
  closeRejectDialog()
  emit('field-rejected', rejectionData)

  // Set refreshing state and show loading feedback
  refreshing.value = true
  appStore.showSnack('Updating field status...')

  // Refresh participant data to get updated values with rejection information
  try {
    await refetchSingleParticipant()
    appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`)
  }
  catch (error) {
    console.error('Error refreshing participant data:', error)
    appStore.showSnack('Error updating field status')
  }
  finally {
    refreshing.value = false
  }
}

const getFieldValue = (fieldName: string) => {
  const field = participantDetails.value.find((item: any) => item.field === fieldName)

  return field ? field.value : ''
}

const fullName = computed(() => {
  const firstName = getFieldValue('firstName')
  const lastName = getFieldValue('lastName')

  return `${firstName} ${lastName}`
})
</script>

<template>
  <div class="ex-partners-container">
    <div class="header-container">
      <h2>Ex-Partners</h2>
    </div>

    <!-- Rejected Fields Alert -->
    <RejectedFieldsAlert
      :rejected-fields="rejectedFieldsWithReasons"
      :editor-view="editorView"
      :recently-submitted-fields="recentlySubmittedFields"
      @edit-field="openEditDialogForRejectedField"
    />

    <!-- Simple alerts for rejected fields (Reviewer View) -->
    <div
      v-if="rejectedFields.length > 0 && !editorView"
      class="mb-4"
    >
      <VAlert
        v-for="(item, index) in rejectedFields"
        :key="`rejected-${item.fieldKey}-${item.partnerIndex}-${index}`"
        type="info"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-info-circle"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.partnerName }} - {{ item.field }}:</strong>
          <span>Field has been rejected and is pending review</span>
          <VChip
            size="small"
            color="primary"
            class="ml-auto"
          >
            Follow-up submitted
          </VChip>
        </div>
      </VAlert>
    </div>

    <div v-if="filteredPartners.length > 0">
      <div
        v-for="(partner, partnerIndex) in filteredPartners"
        :key="partner[0]?.id || `partner-${partnerIndex}`"
        class="partner-section"
      >
        <div class="partner-header">
          <h3>{{ getPartnerName(participantExPartnerInfo[partnerIndex]) }}</h3>
        </div>

        <VDataTable
          :headers="[
            { title: 'Field', key: 'name', width: '200px' },
            { title: 'Value', key: 'value' },
            { title: 'Actions', key: 'actions', sortable: false },
          ]"
          :items="partner"
          hide-default-footer
          class="elevation-0"
          density="compact"
        >
          <!-- Custom row styling for disabled fields -->
          <template #item.name="{ item }">
            <span :class="{ 'dimmed-text': item.disabled }">{{ item.name }}</span>
          </template>

          <template #item.value="{ item }">
            <template v-if="item.disabled">
              <VChip
                size="small"
                color="error"
                variant="tonal"
                class="font-weight-medium"
              >
                {{ item.value }}
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  This field was rejected and is pending review
                </VTooltip>
              </VChip>
            </template>
            <template v-else>
              <span :class="{ 'dimmed-text': item.disabled }">{{ item.value }}</span>
            </template>
          </template>

          <template #item.actions="{ item }">
            <div class="d-flex gap-1">
              <!-- Show warning icon for rejected/disabled fields -->
              <template v-if="item.disabled">
                <VIcon
                  size="16"
                  icon="tabler-alert-triangle"
                  color="error"
                >
                  <VTooltip
                    activator="parent"
                    location="top"
                  >
                    This field was rejected and is pending review
                  </VTooltip>
                </VIcon>
              </template>

              <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
              <template v-else-if="showRejectOptions && !editorView && !item.disabled && !refreshing">
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  color="error"
                  @click="openRejectDialog(item, partnerIndex)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-x"
                    class="reject-icon"
                  />
                  <VTooltip
                    activator="parent"
                    location="top"
                  >
                    Reject this field
                  </VTooltip>
                </VBtn>
              </template>

              <!-- Show loading spinner while refreshing (reviewer view only) -->
              <template v-else-if="showRejectOptions && !editorView && !item.disabled && refreshing">
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  disabled
                >
                  <VIcon
                    size="16"
                    icon="tabler-loader-2"
                    class="spinning"
                    color="primary"
                  />
                  <VTooltip
                    activator="parent"
                    location="top"
                  >
                    Updating field status...
                  </VTooltip>
                </VBtn>
              </template>
            </div>
          </template>
        </VDataTable>
      </div>
    </div>

    <div
      v-else
      class="no-partners-message"
    >
      No ex-partners found
    </div>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="fullName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />

    <!-- Edit Dialog for rejected fields (only for editor view) -->
    <EditFieldDialog
      v-if="editRejectedDialog && fieldToEdit && editorView"
      v-model="editRejectedDialog"
      :field="fieldToEdit.field"
      :field-name="fieldToEdit.rejectedFieldInfo.field"
      :current-value="fieldToEdit.value"
      :entity-id="fieldToEdit.id"
      :entity-type="fieldToEdit.typename || 'PartnerInfo'"
      :participant-name="`${fullName} - ${fieldToEdit.rejectedFieldInfo.partnerName}`"
      :type="ChangeType.Participant"
      :year="2025"
      @close="closeEditRejectedDialog"
      @update="handleRejectedFieldUpdate"
    />
  </div>
</template>

<style scoped>
  .ex-partners-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .partner-section {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .partner-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
  }

  .no-partners-message {
    padding: 16px;
    text-align: center;
    color: #666;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
