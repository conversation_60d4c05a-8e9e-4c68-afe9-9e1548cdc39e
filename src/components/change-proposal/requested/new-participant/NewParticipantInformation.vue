<script setup lang="ts">
const props = defineProps({
  showRejectOptions: {
    type: Boolean,
    default: true,
  },
  editorView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['field-rejected'])

const propertyListingSteps = [
  {
    title: 'Marital Status & Partner',
    subtitle: 'Marital and partner details',
    icon: 'tabler-heart',
  },
  {
    title: 'Ex Partners',
    subtitle: 'Ex Partner details',
    icon: 'tabler-users-plus',
  },
  {
    title: 'Children',
    subtitle: 'Children details',
    icon: 'tabler-accessible',
  },
  {
    title: 'Address',
    subtitle: 'Address details',
    icon: 'tabler-address-book',
  },
]

const currentStep = ref(0)

const handleFieldRejected = (rejectionData: any) => {
  emit('field-rejected', rejectionData)
}
</script>

<template>
  <VCard>
    <VRow no-gutters>
      <VCardText>
        <AppStepper
          v-model:current-step="currentStep"
          :items="propertyListingSteps"
          icon-size="22"
          class="stepper-icon-step-bg"
        />
      </VCardText>

      <VCol cols="12">
        <VCardText>
          <VWindow
            v-model="currentStep"
            class="disable-tab-transition"
          >
            <VWindowItem>
              <NewMaritalStatus
                :show-reject-options="showRejectOptions"
                :editor-view="editorView"
                @field-rejected="handleFieldRejected"
              />
            </VWindowItem>
            <VWindowItem>
              <NewExPartners
                :show-reject-options="showRejectOptions"
                :editor-view="editorView"
                @field-rejected="handleFieldRejected"
              />
            </VWindowItem>
            <VWindowItem>
              <NewChildren
                :show-reject-options="showRejectOptions"
                :editor-view="editorView"
                @field-rejected="handleFieldRejected"
              />
            </VWindowItem>
            <VWindowItem>
              <NewAddress
                :show-reject-options="showRejectOptions"
                :editor-view="editorView"
                @field-rejected="handleFieldRejected"
              />
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>

<style scoped>
  .stepper-icon-step-bg {
    margin-bottom: 16px;
  }
</style>
