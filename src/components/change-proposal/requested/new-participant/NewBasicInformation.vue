<script setup lang="ts">
import avatar from '@images/avatars/avatar-0.png'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'
import EditFieldDialog from '@/components/EditFieldDialog.vue'
import RejectedFieldsAlert from '@/components/change-proposal/RejectedFieldsAlert.vue'
import { ChangeType } from '@/gql/graphql'

const props = defineProps({
  showRejectOptions: {
    type: Boolean,
    default: true,
  },
  editorView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['field-rejected'])

const { state: { participantDetails, loadingParticipant }, actions: { refetchSingleParticipant } } = useParticipants()

const appStore = useAppStore()

const rejectDialog = ref(false)
const fieldToReject = ref(null)

// Edit dialog for rejected fields in editor view
const editDialog = ref(false)
const fieldToEdit = ref<any>(null)

// Track recently submitted fields for feedback
const recentlySubmittedFields = ref<Set<string>>(new Set())

// Get rejected fields with reasons for editor view
const rejectedFieldsWithReasons = computed(() => {
  if (!props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string; reason: string; submittedForReview?: boolean; submittedAt?: string }> = []

  // Find the certificationRejectReason field in participantDetails
  const certificationRejectReasonField = participantDetails.value.find((item: any) => item.field === 'certificationRejectReason')

  if (certificationRejectReasonField?.value && Array.isArray(certificationRejectReasonField.value)) {
    certificationRejectReasonField.value.forEach((item: any) => {
      if (item.reason && item.status === 'VALID') {
        rejectedFieldsArray.push({
          field: fieldKeyToName(item.field),
          fieldKey: item.field,
          reason: item.reason,
          submittedForReview: item.submittedForReview,
          submittedAt: item.submittedAt,
        })
      }
    })
  }

  return rejectedFieldsArray
})

// Get rejected fields based on pendingChanges array (for reviewer view)
const rejectedFields = computed(() => {
  if (!props.showRejectOptions || props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string }> = []

  // Find personalInfo field that contains the pendingChanges
  const personalInfoField = participantDetails.value.find((item: any) => item.field === 'personalInfo')
  const personalInfoValue = personalInfoField?.value as any
  const pendingChanges = personalInfoValue?.pendingChanges || []

  // Convert pending changes to rejected fields display
  pendingChanges.forEach((fieldKey: string) => {
    rejectedFieldsArray.push({
      field: fieldKeyToName(fieldKey),
      fieldKey,
    })
  })

  return rejectedFieldsArray
})

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

const openRejectDialog = (item: any) => {
  if (!props.showRejectOptions || refreshing.value || props.editorView)
    return

  fieldToReject.value = item
  rejectDialog.value = true
}

const closeRejectDialog = () => {
  rejectDialog.value = false
  fieldToReject.value = null
}

// Functions for editing rejected fields in editor view
const openEditDialogForRejectedField = (rejectedFieldItem: any) => {
  if (!props.editorView)
    return

  // Find the actual field data from participantDetails
  const fieldData = participantDetails.value.find((item: any) => item.field === rejectedFieldItem.fieldKey)

  if (fieldData) {
    fieldToEdit.value = {
      ...fieldData,
      rejectedFieldInfo: rejectedFieldItem,
    }
    editDialog.value = true
  }
}

const closeEditDialog = () => {
  editDialog.value = false
  fieldToEdit.value = null
}

const handleFieldUpdate = async () => {
  if (!fieldToEdit.value)
    return

  const fieldKey = fieldToEdit.value.rejectedFieldInfo.fieldKey
  const fieldName = fieldToEdit.value.rejectedFieldInfo.field

  closeEditDialog()

  // Show loading feedback
  refreshing.value = true
  appStore.showSnack('Submitting change proposal...')

  try {
    // The EditFieldDialog will handle creating a change proposal
    // When approved, this will update the field and clear rejection reasons
    await refetchSingleParticipant()

    // Mark field as recently submitted for feedback
    recentlySubmittedFields.value.add(fieldKey)

    // Show success feedback
    appStore.showSnack(`✅ Change proposal submitted for "${fieldName}"! It will be reviewed and applied once approved.`)

    // Remove from recently submitted after 5 seconds
    setTimeout(() => {
      recentlySubmittedFields.value.delete(fieldKey)
    }, 5000)
  }
  catch (error) {
    console.error('Error submitting change proposal:', error)
    appStore.showSnack('❌ Error submitting change proposal')
  }
  finally {
    refreshing.value = false
  }
}

const refreshing = ref(false)

const handleFieldRejected = async (rejectionData: any) => {
  closeRejectDialog()
  emit('field-rejected', rejectionData)

  // Set refreshing state and show loading feedback
  refreshing.value = true
  appStore.showSnack('Updating field status...')

  // Refresh participant data to get updated values with rejection information
  try {
    await refetchSingleParticipant()
    appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`)
  }
  catch (error) {
    console.error('Error refreshing participant data:', error)
    appStore.showSnack('Error updating field status')
  }
  finally {
    refreshing.value = false
  }
}

const getFieldValue = (fieldName: string) => {
  const field = participantDetails.value.find((item: any) => item.field === fieldName)

  return field ? field.value : ''
}

const fullName = computed(() => {
  const firstName = getFieldValue('firstName')
  const lastName = getFieldValue('lastName')

  return `${firstName} ${lastName}`
})

const getIconForField = (fieldName: string) => {
  const iconMap: Record<string, string> = {
    phone: 'tabler-phone',
    birthDate: 'tabler-calendar-event',
    firstName: 'tabler-user-circle',
    lastName: 'tabler-user-circle',
    email: 'tabler-mail',
    maritalStatus: 'tabler-heart',
    Address: 'tabler-home',
  }

  return iconMap[fieldName] || 'tabler-info-circle'
}

const formatFieldValue = (field: any) => {
  // Handle case where field.value might be an object
  let value = field.value

  // If value is an object, try to extract a meaningful string representation
  if (typeof value === 'object' && value !== null) {
    // If it's an array, join it
    if (Array.isArray(value)) {
      value = value.join(', ')
    }
    else {
      // For other objects, try to get a string representation or fallback
      value = value.toString !== Object.prototype.toString ? value.toString() : JSON.stringify(value)
    }
  }

  // Handle date formatting
  if (field.field.toLowerCase().includes('date') && value) {
    try {
      return new Date(value).toLocaleDateString()
    }
    catch (e) {
      return value
    }
  }

  return value || ''
}

const fieldsToExclude = [
  'pendingChanges',
  'birthDay',
  'birthMonth',
  'birthYear',
  'id',
  'participantId',
  'partnerInfo',
  'children',
  'personalInfo',
  'address',
  'certificationRejectReason',
]

const displayableFields = computed(() => {
  return participantDetails.value.filter(item => !fieldsToExclude.includes(item.field))
})
</script>

<template>
  <VSkeletonLoader
    v-if="loadingParticipant"
    class="mx-auto border py-6"
    type="list-item-avatar-three-line"
  />

  <VCard
    v-else
    class="basic-information-container"
  >
    <VCardText>
      <!-- Rejected Fields Alert -->
      <RejectedFieldsAlert
        :rejected-fields="rejectedFieldsWithReasons"
        :editor-view="editorView"
        :recently-submitted-fields="recentlySubmittedFields"
        @edit-field="openEditDialogForRejectedField"
      />

      <!-- Simple alerts for rejected fields (Reviewer View) -->
      <div
        v-if="rejectedFields.length > 0 && !editorView"
        class="mb-4"
      >
        <VAlert
          v-for="(item, index) in rejectedFields"
          :key="`rejected-${item.fieldKey}-${index}`"
          type="info"
          variant="tonal"
          class="mb-2"
          closable
        >
          <template #prepend>
            <VIcon
              icon="tabler-info-circle"
              size="10"
            />
          </template>
          <div class="d-flex align-center">
            <strong class="mr-2">{{ item.field }}:</strong>
            <span>Field has been rejected and is pending review</span>
            <VChip
              size="small"
              color="primary"
              class="ml-auto"
            >
              Follow-up submitted
            </VChip>
          </div>
        </VAlert>
      </div>

      <VRow>
        <VCol cols="9">
          <div class="d-flex align-bottom flex-sm-row flex-column justify-center gap-x-5">
            <!-- Avatar section -->
            <div class="d-flex">
              <VAvatar
                size="100"
                :image="avatar"
                class="mx-auto my-auto"
              />
            </div>

            <div class="w-100 mt-8 pt-4 mt-sm-0">
              <h6 class="text-h4 text-center text-sm-start font-weight-medium mb-3">
                {{ fullName }}
              </h6>

              <div class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4">
                <div class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4">
                  <span
                    v-for="(field, index) in displayableFields"
                    :key="index"
                    class="d-flex align-center info-item"
                  >
                    <VIcon
                      size="20"
                      :icon="getIconForField(field.field)"
                      class="me-1"
                    />

                    <span class="text-body-1 mr-1">
                      <template v-if="field.field === 'status'">
                        <VChip
                          class="ml-2 font-weight-bold"
                          label
                          color="success"
                          density="compact"
                        >
                          {{ formatFieldValue(field) }}
                        </VChip>
                      </template>
                      <template v-else-if="field.disabled">
                        <VChip
                          class="ml-2 font-weight-bold"
                          label
                          color="error"
                          density="compact"
                        >
                          {{ formatFieldValue(field) }}
                          <VTooltip
                            activator="parent"
                            location="top"
                          >
                            This field was rejected and is pending review
                          </VTooltip>
                        </VChip>
                      </template>
                      <template v-else>
                        {{ formatFieldValue(field) }}
                      </template>
                    </span>

                    <!-- Reject Icon (only show for reviewer view and non-disabled fields) -->
                    <template v-if="showRejectOptions && !editorView && !field.disabled && !refreshing">
                      <VIcon
                        size="16"
                        icon="tabler-x"
                        class="reject-icon ms-1 ml-2"
                        color="error"
                        style="cursor: pointer;"
                        @click.stop="openRejectDialog(field)"
                      >
                        <VTooltip
                          activator="parent"
                          location="top"
                        >
                          Reject this field
                        </VTooltip>
                      </VIcon>
                    </template>

                    <!-- Show loading spinner while refreshing (reviewer view only) -->
                    <template v-else-if="showRejectOptions && !editorView && !field.disabled && refreshing">
                      <VIcon
                        size="16"
                        icon="tabler-loader-2"
                        class="ms-1 ml-2 spinning"
                        color="primary"
                        style="opacity: 0.7;"
                      >
                        <VTooltip
                          activator="parent"
                          location="top"
                        >
                          Updating field status...
                        </VTooltip>
                      </VIcon>
                    </template>

                    <!-- Show warning icon for rejected/disabled fields -->
                    <template v-else-if="field.disabled">
                      <VIcon
                        size="16"
                        icon="tabler-alert-triangle"
                        class="ms-1 ml-2"
                        color="error"
                      >
                        <VTooltip
                          activator="parent"
                          location="top"
                        >
                          This field was rejected and is pending review
                        </VTooltip>
                      </VIcon>
                    </template>
                  </span>

                  <!-- Empty state messages -->
                  <div
                    v-if="displayableFields.length === 0 && participantDetails.length > 0"
                    class="text-caption text-disabled pa-2"
                  >
                    No details to display based on current filters.
                  </div>
                  <div
                    v-if="!loadingParticipant && participantDetails.length === 0"
                    class="text-caption text-disabled pa-2"
                  >
                    No participant details available.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </VCol>

        <VCol
          cols="3"
          class="d-flex justify-end"
        >
          <PensionCode />
        </VCol>
      </VRow>
    </VCardText>

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="fullName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />

    <!-- Edit Dialog for rejected fields (only for editor view) -->
    <EditFieldDialog
      v-if="editDialog && fieldToEdit && editorView"
      v-model="editDialog"
      :field="fieldToEdit.field"
      :field-name="fieldToEdit.rejectedFieldInfo.field"
      :current-value="fieldToEdit.value"
      :entity-id="fieldToEdit.id"
      :entity-type="fieldToEdit.typename || 'PersonalInfo'"
      :participant-name="fullName"
      :type="ChangeType.Participant"
      :year="2025"
      @close="closeEditDialog"
      @update="handleFieldUpdate"
    />
  </VCard>
</template>

<style scoped>
  .basic-information-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: white;
  }

  .info-item {
    position: relative;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
