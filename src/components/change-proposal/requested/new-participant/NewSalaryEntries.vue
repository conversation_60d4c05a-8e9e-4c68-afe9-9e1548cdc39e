<script setup lang="ts">
import { useQuery } from '@vue/apollo-composable'
import { GET_PARTICIPANT_BY_ID } from '@/api/graphql/queries/participantQueries'
import type { SalaryEntry } from '@/gql/graphql'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionStore } from '@/stores/pension/pensionStore'
import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'
import EditFieldDialog from '@/components/EditFieldDialog.vue'
import RejectedFieldsAlert from '@/components/change-proposal/RejectedFieldsAlert.vue'

const props = defineProps({
  participantId: {
    type: String,
    required: true,
  },
  employmentInfoId: {
    type: String,
    required: true,
  },
  showRejectOptions: {
    type: Boolean,
    default: true,
  },
  editorView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['refresh', 'field-rejected'])

const entries = ref<SalaryEntry[]>([])
const loading = ref(true)
const participantName = ref('')
const currentYear = new Date().getFullYear()
const appStore = useAppStore()
const pensionStore = usePensionStore()

const rejectDialog = ref(false)
const fieldToReject = ref(null as any)

// Edit dialog for rejected fields in editor view
const editRejectedDialog = ref(false)
const fieldToEdit = ref<any>(null)

// Track recently submitted fields for feedback
const recentlySubmittedFields = ref<Set<string>>(new Set())

const certifiedYears = computed(() => {
  return pensionStore.certifiedDataYears
})

const { result, loading: queryLoading, refetch } = useQuery(
  GET_PARTICIPANT_BY_ID,
  { id: props.participantId },
  { fetchPolicy: 'network-only' },
)

// Get rejected fields with reasons for editor view
const rejectedFieldsWithReasons = computed(() => {
  if (!props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string; reason: string; entryYear: number }> = []

  // Check each salary entry's certification reject reasons
  entries.value.forEach((entry: any) => {
    if (entry.certificationRejectReason && Array.isArray(entry.certificationRejectReason)) {
      entry.certificationRejectReason.forEach((item: any) => {
        if (item.reason && item.status === 'VALID' && isSalaryField(item.field)) {
          rejectedFieldsArray.push({
            field: fieldKeyToName(item.field),
            fieldKey: item.field,
            reason: item.reason,
            entryYear: entry.year,
          })
        }
      })
    }
  })

  return rejectedFieldsArray
})

// Check if a field is salary-related
function isSalaryField(fieldKey: string): boolean {
  const salaryFields = ['amount']

  return salaryFields.includes(fieldKey)
}

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''

  const fieldNames: Record<string, string> = {
    amount: 'Salary Amount',
  }

  return fieldNames[fieldKey] || fieldKey.replace(/([A-Z])/g, ' $1').charAt(0).toUpperCase() + fieldKey.slice(1)
}

// Format currency values
const formatCurrency = (value: number): string => {
  return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// Check if entry is from current year
const isCurrentYear = (year: number): boolean => {
  return year === currentYear
}

// Helper function to determine status color
const getStatusColor = (year: number): string => {
  if (isCurrentYear(year))
    return 'primary'

  return 'success'
}

const isDisabled = (pendingChanges: string[] | undefined | null) => {
  return pendingChanges?.includes('amount') || false
}

const isCertifiedYear = (year: number) => {
  return certifiedYears.value.includes(year)
}

const refreshing = ref(false)

const openRejectDialog = (entry: any) => {
  if (!props.showRejectOptions || refreshing.value || props.editorView)
    return

  fieldToReject.value = {
    field: 'amount',
    name: 'Salary Amount',
    value: formatCurrency(entry.amount),
    id: entry.id,
    typename: 'SalaryEntry',
    year: entry.year,
  }
  rejectDialog.value = true
}

const closeRejectDialog = () => {
  rejectDialog.value = false
  fieldToReject.value = null
}

const handleFieldRejected = async (rejectionData: any) => {
  closeRejectDialog()
  emit('field-rejected', rejectionData)

  // Set refreshing state and show loading feedback
  refreshing.value = true
  appStore.showSnack('Updating field status...')

  // Refresh participant data to get updated values with rejection information
  try {
    await fetchEntries()
    appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`)
  }
  catch (error) {
    console.error('Error refreshing participant data:', error)
    appStore.showSnack('Error updating field status')
  }
  finally {
    refreshing.value = false
  }
}

// Functions for editing rejected fields in editor view
const openEditDialogForRejectedField = (rejectedFieldItem: any) => {
  if (!props.editorView)
    return

  // Find the corresponding salary entry
  const entry = entries.value.find((entry: any) => entry.year === rejectedFieldItem.entryYear)

  if (entry) {
    fieldToEdit.value = {
      field: rejectedFieldItem.fieldKey,
      value: entry.amount,
      id: entry.id,
      typename: 'SalaryEntry',
      year: entry.year,
      rejectedFieldInfo: rejectedFieldItem,
    }
    editRejectedDialog.value = true
  }
}

const closeEditRejectedDialog = () => {
  editRejectedDialog.value = false
  fieldToEdit.value = null
}

const handleRejectedFieldUpdate = async () => {
  if (!fieldToEdit.value)
    return

  const fieldKey = `${fieldToEdit.value.rejectedFieldInfo.fieldKey}_${fieldToEdit.value.year}`
  const fieldName = `${fieldToEdit.value.rejectedFieldInfo.field} (${fieldToEdit.value.year})`

  closeEditRejectedDialog()

  // Show loading feedback
  refreshing.value = true
  appStore.showSnack('Submitting change proposal...')

  try {
    // The EditFieldDialog will handle creating a change proposal
    // When approved, this will update the field and clear rejection reasons
    await fetchEntries()

    // Mark field as recently submitted for feedback
    recentlySubmittedFields.value.add(fieldKey)

    // Show success feedback
    appStore.showSnack(`✅ Change proposal submitted for "${fieldName}"! It will be reviewed and applied once approved.`)

    // Remove from recently submitted after 5 seconds
    setTimeout(() => {
      recentlySubmittedFields.value.delete(fieldKey)
    }, 5000)
  }
  catch (error) {
    console.error('Error submitting change proposal:', error)
    appStore.showSnack('❌ Error submitting change proposal')
  }
  finally {
    refreshing.value = false
  }
}

const fetchEntries = async () => {
  loading.value = true
  try {
    await refetch()
    if (result.value?.getParticipantById?.employmentInfo?.salaryEntries) {
      const sortedEntries = [...result.value.getParticipantById.employmentInfo.salaryEntries].sort((a: any, b: any) => b.year - a.year)

      entries.value = sortedEntries
      participantName.value = `${result.value.getParticipantById.personalInfo?.firstName} ${result.value.getParticipantById.personalInfo?.lastName}`
    }
    else {
      entries.value = []
    }
    emit('refresh')
  }
  catch (error) {
    console.error('Error fetching salary entries:', error)
    entries.value = []
  }
  finally {
    loading.value = false
  }
}

onMounted(fetchEntries)
</script>

<template>
  <VCard
    variant="outlined"
    class="mb-4"
  >
    <VCardTitle class="py-3">
      <h4 class="text-subtitle-1 font-weight-medium">
        Gross Part-time Monthly Salary
      </h4>
    </VCardTitle>
    <VCardSubtitle class="text-caption text-medium-emphasis">
      Amount as of January 1st or start date if started during the year
    </VCardSubtitle>

    <!-- Rejected Fields Alert -->
    <div class="mx-4 mt-4">
      <RejectedFieldsAlert
        :rejected-fields="rejectedFieldsWithReasons.map(item => ({ ...item, field: `${item.field} (${item.entryYear})`, fieldKey: `${item.fieldKey}_${item.entryYear}` }))"
        :editor-view="editorView"
        :recently-submitted-fields="recentlySubmittedFields"
        @edit-field="openEditDialogForRejectedField"
      />
    </div>

    <VCardText>
      <VTable
        class="salary-table"
        density="comfortable"
      >
        <thead>
          <tr>
            <th class="text-left">
              YEAR
            </th>
            <th class="text-left">
              GROSS PART-TIME MONTHLY SALARY
            </th>
            <th class="text-center">
              STATUS
            </th>
            <th
              v-if="showRejectOptions"
              class="text-center"
            >
              ACTIONS
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td
              :colspan="showRejectOptions ? 4 : 3"
              class="text-center"
            >
              <VProgressCircular
                indeterminate
                color="primary"
              />
            </td>
          </tr>
          <tr v-else-if="entries.length === 0">
            <td
              :colspan="showRejectOptions ? 4 : 3"
              class="text-center"
            >
              No salary entries found
            </td>
          </tr>
          <template v-else>
            <tr
              v-for="entry in entries"
              :key="entry.id"
              :class="[
                isCurrentYear(entry.year) ? 'bg-blue-lighten-5' : '',
                isDisabled(entry.pendingChanges) ? 'dimmed-row' : '',
              ]"
            >
              <td class="font-weight-medium">
                {{ entry.year }}
              </td>
              <td>
                <template v-if="isDisabled(entry.pendingChanges)">
                  <VChip
                    size="small"
                    color="error"
                    variant="tonal"
                    class="font-weight-medium"
                  >
                    Afl. {{ formatCurrency(entry.amount) }}
                    <VTooltip
                      activator="parent"
                      location="top"
                    >
                      This field was rejected and is pending review
                    </VTooltip>
                  </VChip>
                </template>
                <template v-else>
                  <span :class="{ 'dimmed-text': isDisabled(entry.pendingChanges) }">
                    Afl. {{ formatCurrency(entry.amount) }}
                  </span>
                </template>
              </td>
              <td class="text-center">
                <VChip
                  :color="getStatusColor(entry.year)"
                  size="small"
                  label
                >
                  {{ isCurrentYear(entry.year) ? 'Current' : 'Certified' }}
                </VChip>
              </td>
              <td
                v-if="showRejectOptions"
                class="text-center"
              >
                <div class="d-flex gap-1 justify-center">
                  <!-- Show warning icon for rejected/disabled fields -->
                  <template v-if="isDisabled(entry.pendingChanges)">
                    <VIcon
                      size="16"
                      icon="tabler-alert-triangle"
                      color="error"
                    >
                      <VTooltip
                        activator="parent"
                        location="top"
                      >
                        This field was rejected and is pending review
                      </VTooltip>
                    </VIcon>
                  </template>

                  <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
                  <template v-else-if="showRejectOptions && !editorView && !isDisabled(entry.pendingChanges) && !refreshing">
                    <VBtn
                      icon
                      size="small"
                      variant="text"
                      color="error"
                      @click="openRejectDialog(entry)"
                    >
                      <VIcon
                        size="16"
                        icon="tabler-x"
                        class="reject-icon"
                      />
                      <VTooltip
                        activator="parent"
                        location="top"
                      >
                        Reject this field
                      </VTooltip>
                    </VBtn>
                  </template>

                  <!-- Show loading spinner while refreshing (reviewer view only) -->
                  <template v-else-if="showRejectOptions && !editorView && !isDisabled(entry.pendingChanges) && refreshing">
                    <VBtn
                      icon
                      size="small"
                      variant="text"
                      disabled
                    >
                      <VIcon
                        size="16"
                        icon="tabler-loader-2"
                        class="spinning"
                        color="primary"
                      />
                      <VTooltip
                        activator="parent"
                        location="top"
                      >
                        Updating field status...
                      </VTooltip>
                    </VBtn>
                  </template>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </VTable>
    </VCardText>

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="participantName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />

    <!-- Edit Dialog for rejected fields (only for editor view) -->
    <EditFieldDialog
      v-if="editRejectedDialog && fieldToEdit && editorView"
      v-model="editRejectedDialog"
      :field="fieldToEdit.field"
      :field-name="fieldToEdit.rejectedFieldInfo.field"
      :current-value="fieldToEdit.value"
      :entity-id="fieldToEdit.id"
      :entity-type="fieldToEdit.typename"
      :participant-name="participantName"
      type="PARTICIPANT"
      :year="fieldToEdit.year"
      @close="closeEditRejectedDialog"
      @update="handleRejectedFieldUpdate"
    />
  </VCard>
</template>

<style scoped>
  .salary-table th {
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }

  .dimmed-row {
    opacity: 0.8;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
