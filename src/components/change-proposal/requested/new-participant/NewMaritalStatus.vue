<script setup lang="ts">
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'
import EditFieldDialog from '@/components/EditFieldDialog.vue'
import RejectedFieldsAlert from '@/components/change-proposal/RejectedFieldsAlert.vue'
import { ChangeType } from '@/gql/graphql'

const props = defineProps({
  showRejectOptions: {
    type: Boolean,
    default: true,
  },
  editorView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['field-rejected'])

const { state: { participantPartnerInfo, participantDetails, loadingParticipant }, actions: { refetchSingleParticipant } } = useParticipants()

const appStore = useAppStore()

const rejectDialog = ref(false)
const fieldToReject = ref(null)

// Edit dialog for rejected fields in editor view
const editRejectedDialog = ref(false)
const fieldToEdit = ref<any>(null)

// Track recently submitted fields for feedback
const recentlySubmittedFields = ref<Set<string>>(new Set())

// Track permanently submitted fields (until data refresh shows submittedForReview: true)
const permanentlySubmittedFields = ref<Set<string>>(new Set())

// Get rejected fields with reasons for editor view
const rejectedFieldsWithReasons = computed(() => {
  if (!props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string; reason: string; submittedForReview?: boolean; submittedAt?: string }> = []

  // Check current partner's certification reject reasons
  if (participantPartnerInfo.value.length > 0) {
    const partnerData = participantPartnerInfo.value.find((item: any) => item.field === 'certificationRejectReason')

    if (partnerData?.value && Array.isArray(partnerData.value)) {
      partnerData.value.forEach((item: any) => {
        if (item.reason && item.status === 'VALID' && isPartnerField(item.field)) {
          rejectedFieldsArray.push({
            field: fieldKeyToName(item.field),
            fieldKey: item.field,
            reason: item.reason,
            submittedForReview: item.submittedForReview || permanentlySubmittedFields.value.has(item.field),
            submittedAt: item.submittedAt,
          })
        }
      })
    }
  }

  // Also check personal info for partner-related rejections (fallback)
  const certificationRejectReasonField = participantDetails.value.find((item: any) => item.field === 'certificationRejectReason' && item.current === true)
  if (certificationRejectReasonField?.value && Array.isArray(certificationRejectReasonField.value)) {
    certificationRejectReasonField.value.forEach((item: any) => {
      // Only include partner-related fields
      if (item.reason && item.status === 'VALID' && isPartnerField(item.field)) {
        rejectedFieldsArray.push({
          field: fieldKeyToName(item.field),
          fieldKey: item.field,
          reason: item.reason,
          submittedForReview: item.submittedForReview || permanentlySubmittedFields.value.has(item.field),
          submittedAt: item.submittedAt,
        })
      }
    })
  }

  return rejectedFieldsArray
})

// Watch for changes in rejected fields to clean up permanently submitted fields
// when the backend properly reflects submittedForReview: true
watch(rejectedFieldsWithReasons, newRejectedFields => {
  // Clean up permanently submitted fields that now have submittedForReview: true from backend
  newRejectedFields.forEach(field => {
    if (field.submittedForReview === true && permanentlySubmittedFields.value.has(field.fieldKey))
      permanentlySubmittedFields.value.delete(field.fieldKey)
  })
}, { deep: true })

// Get rejected fields based on pendingChanges array (for reviewer view)
const rejectedFields = computed(() => {
  if (!props.showRejectOptions || props.editorView)
    return []

  const rejectedFieldsArray: Array<{ field: string; fieldKey: string }> = []

  // Check current partner's pending changes
  if (participantPartnerInfo.value.length > 0) {
    const pendingChangesField = participantPartnerInfo.value.find((item: any) => item.field === 'pendingChanges')
    const pendingChanges = pendingChangesField?.value

    // Convert pending changes to rejected fields display
    if (Array.isArray(pendingChanges)) {
      pendingChanges.forEach((fieldKey: string) => {
        if (isPartnerField(fieldKey)) {
          rejectedFieldsArray.push({
            field: fieldKeyToName(fieldKey),
            fieldKey,
          })
        }
      })
    }
  }

  // Also check personal info for partner-related pending changes (fallback)
  const personalInfoField = participantDetails.value.find((item: any) => item.field === 'personalInfo')
  const personalInfoValue = personalInfoField?.value as any
  const personalPendingChanges = personalInfoValue?.pendingChanges

  // Convert pending changes to rejected fields display for partner-related fields
  if (Array.isArray(personalPendingChanges)) {
    personalPendingChanges.forEach((fieldKey: string) => {
      if (isPartnerField(fieldKey)) {
        rejectedFieldsArray.push({
          field: fieldKeyToName(fieldKey),
          fieldKey,
        })
      }
    })
  }

  return rejectedFieldsArray
})

// Check if a field is partner-related
function isPartnerField(fieldKey: string): boolean {
  const partnerFields = ['firstName', 'lastName', 'dateOfBirth', 'startDate', 'isDeceased', 'maritalStatus']

  return partnerFields.includes(fieldKey)
}

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

const filteredList = computed(() => {
  return participantPartnerInfo.value.filter(item => item.field !== 'id' && item.field !== 'isCurrent' && item.field !== 'startDate' && item.field !== 'certificationRejectReason')
})

const refreshing = ref(false)

const openRejectDialog = (item: any) => {
  if (!props.showRejectOptions || refreshing.value || props.editorView)
    return

  fieldToReject.value = item
  rejectDialog.value = true
}

const closeRejectDialog = () => {
  rejectDialog.value = false
  fieldToReject.value = null
}

// Functions for editing rejected fields in editor view
const openEditDialogForRejectedField = (rejectedFieldItem: any) => {
  if (!props.editorView)
    return

  // Find the actual field data from filteredList
  const fieldData = filteredList.value.find((item: any) => item.field === rejectedFieldItem.fieldKey)

  if (fieldData) {
    fieldToEdit.value = {
      ...fieldData,
      rejectedFieldInfo: rejectedFieldItem,
    }
    editRejectedDialog.value = true
  }
}

const closeEditRejectedDialog = () => {
  editRejectedDialog.value = false
  fieldToEdit.value = null
}

const handleRejectedFieldUpdate = async () => {
  if (!fieldToEdit.value)
    return

  const fieldKey = fieldToEdit.value.rejectedFieldInfo.fieldKey
  const fieldName = fieldToEdit.value.rejectedFieldInfo.field

  closeEditRejectedDialog()

  // Show loading feedback
  refreshing.value = true
  appStore.showSnack('Submitting change proposal...')

  try {
    // The EditFieldDialog will handle creating a change proposal
    // When approved, this will update the field and clear rejection reasons
    await refetchSingleParticipant()

    // Mark field as recently submitted for feedback (temporary)
    recentlySubmittedFields.value.add(fieldKey)

    // Mark field as permanently submitted until backend reflects the change
    permanentlySubmittedFields.value.add(fieldKey)

    // Show success feedback
    appStore.showSnack(`✅ Change proposal submitted for "${fieldName}"! It will be reviewed and applied once approved.`)

    // Remove from recently submitted after 5 seconds (but keep in permanently submitted)
    setTimeout(() => {
      recentlySubmittedFields.value.delete(fieldKey)
    }, 5000)
  }
  catch (error) {
    console.error('Error submitting change proposal:', error)
    appStore.showSnack('❌ Error submitting change proposal')
  }
  finally {
    refreshing.value = false
  }
}

const handleFieldRejected = async (rejectionData: any) => {
  closeRejectDialog()
  emit('field-rejected', rejectionData)

  // Set refreshing state and show loading feedback
  refreshing.value = true
  appStore.showSnack('Updating field status...')

  // Refresh participant data to get updated values with rejection information
  try {
    await refetchSingleParticipant()
    appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`)
  }
  catch (error) {
    console.error('Error refreshing participant data:', error)
    appStore.showSnack('Error updating field status')
  }
  finally {
    refreshing.value = false
  }
}

const getFieldValue = (fieldName: string) => {
  const field = participantDetails.value.find((item: any) => item.field === fieldName)

  return field ? field.value : ''
}

const fullName = computed(() => {
  const firstName = getFieldValue('firstName')
  const lastName = getFieldValue('lastName')

  return `${firstName} ${lastName}`
})
</script>

<template>
  <div class="marital-status-container">
    <div class="header-container">
      <h2>Marital Status</h2>
    </div>

    <!-- Rejected Fields Alert -->
    <RejectedFieldsAlert
      :rejected-fields="rejectedFieldsWithReasons"
      :editor-view="editorView"
      :recently-submitted-fields="recentlySubmittedFields"
      @edit-field="openEditDialogForRejectedField"
    />

    <div v-if="filteredList.length > 0">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="filteredList"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <!-- Custom row styling for disabled fields -->
        <template #item.name="{ item }">
          <span :class="{ 'dimmed-text': item.disabled }">{{ item.name }}</span>
        </template>

        <template #item.value="{ item }">
          <template v-if="item.disabled">
            <VChip
              size="small"
              color="error"
              variant="tonal"
              class="font-weight-medium"
            >
              {{ item.value }}
              <VTooltip
                activator="parent"
                location="top"
              >
                This field was rejected and is pending review
              </VTooltip>
            </VChip>
          </template>
          <template v-else>
            <span :class="{ 'dimmed-text': item.disabled }">{{ item.value }}</span>
          </template>
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <!-- Show warning icon for rejected/disabled fields -->
            <template v-if="item.disabled">
              <VIcon
                size="16"
                icon="tabler-alert-triangle"
                color="error"
              >
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  This field was rejected and is pending review
                </VTooltip>
              </VIcon>
            </template>

            <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
            <template v-else-if="showRejectOptions && !editorView && !item.disabled && !refreshing">
              <VBtn
                icon
                size="small"
                variant="text"
                color="error"
                @click="openRejectDialog(item)"
              >
                <VIcon
                  size="16"
                  icon="tabler-x"
                  class="reject-icon"
                />
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  Reject this field
                </VTooltip>
              </VBtn>
            </template>

            <!-- Show loading spinner while refreshing (reviewer view only) -->
            <template v-else-if="showRejectOptions && !editorView && !item.disabled && refreshing">
              <VBtn
                icon
                size="small"
                variant="text"
                disabled
              >
                <VIcon
                  size="16"
                  icon="tabler-loader-2"
                  class="spinning"
                  color="primary"
                />
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  Updating field status...
                </VTooltip>
              </VBtn>
            </template>
          </div>
        </template>
      </VDataTable>
    </div>

    <div
      v-else
      class="text-caption text-disabled pa-4 text-center"
    >
      No partner information available.
    </div>

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="fullName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />

    <!-- Edit Dialog for rejected fields (only for editor view) -->
    <EditFieldDialog
      v-if="editRejectedDialog && fieldToEdit && editorView"
      v-model="editRejectedDialog"
      :field="fieldToEdit.field"
      :field-name="fieldToEdit.rejectedFieldInfo.field"
      :current-value="fieldToEdit.value"
      :entity-id="fieldToEdit.id"
      :entity-type="fieldToEdit.typename || 'PartnerInfo'"
      :participant-name="fullName"
      :type="ChangeType.Participant"
      :year="2025"
      @close="closeEditRejectedDialog"
      @update="handleRejectedFieldUpdate"
    />
  </div>
</template>

<style scoped>
  .marital-status-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .marital-status-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
