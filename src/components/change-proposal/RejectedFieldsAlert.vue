<script setup lang="ts">
interface RejectedField {
  field: string
  fieldKey: string
  reason: string
  submittedForReview?: boolean
  submittedAt?: string
  [key: string]: any
}

const props = defineProps<{
  rejectedFields: RejectedField[]
  editorView: boolean
  recentlySubmittedFields: Set<string>
  entityName?: string
}>()

const emit = defineEmits<{
  editField: [rejectedField: RejectedField]
}>()

const handleEditClick = (rejectedField: RejectedField) => {
  emit('editField', rejectedField)
}

const isFieldSubmitted = (rejectedField: RejectedField): boolean => {
  return rejectedField.submittedForReview === true || props.recentlySubmittedFields.has(rejectedField.fieldKey)
}

const getDisplayName = (rejectedField: RejectedField): string => {
  if (props.entityName)
    return `${props.entityName} - ${rejectedField.field}`

  if (rejectedField.partnerName)
    return `${rejectedField.partnerName} - ${rejectedField.field}`

  if (rejectedField.childName)
    return `${rejectedField.childName} - ${rejectedField.field}`

  return rejectedField.field
}
</script>

<template>
  <div
    v-if="rejectedFields.length > 0 && editorView"
    class="rejected-fields-container"
  >
    <VAlert
      v-for="(item, index) in rejectedFields"
      :key="`rejected-reason-${item.fieldKey}-${index}`"
      :type="isFieldSubmitted(item) ? 'info' : 'error'"
      variant="tonal"
      class="mb-3 rejected-field-alert"
      :border-color="isFieldSubmitted(item) ? 'info' : 'error'"
      density="compact"
    >
      <template #prepend>
        <VIcon
          :icon="isFieldSubmitted(item) ? 'tabler-info-circle' : 'tabler-alert-circle'"
          size="20"
          class="mt-1"
        />
      </template>

      <div class="d-flex flex-wrap align-center justify-space-between gap-2">
        <div class="alert-content">
          <div class="text-body-1 font-weight-medium mb-1">
            {{ getDisplayName(item) }}
          </div>
          <div class="text-body-2">
            {{ item.reason }}
          </div>
        </div>

        <div class="d-flex align-center gap-2 action-buttons">
          <VBtn
            v-if="!isFieldSubmitted(item)"
            size="small"
            color="primary"
            variant="flat"
            :disabled="isFieldSubmitted(item)"
            class="edit-btn"
            @click.stop="handleEditClick(item)"
          >
            <VIcon
              icon="tabler-edit"
              size="16"
              start
            />
            Edit
          </VBtn>

          <VChip
            v-if="isFieldSubmitted(item)"
            size="small"
            :color="isFieldSubmitted(item) ? 'success' : 'error'"
            :variant="isFieldSubmitted(item) ? 'flat' : 'outlined'"
            :class="{ 'animate-pulse': props.recentlySubmittedFields.has(item.fieldKey) }"
            class="status-chip"
          >
            <VIcon
              :icon="isFieldSubmitted(item) ? 'tabler-check' : 'tabler-alert-triangle'"
              size="14"
              start
            />
            {{ isFieldSubmitted(item) ? 'Submitted for review' : 'Needs fix' }}
          </VChip>
        </div>
      </div>
    </VAlert>
  </div>
</template>

<style scoped>
.rejected-field-alert {
    transition: all 0.3s ease;
  }

  .rejected-field-alert:hover {
    transform: translateX(2px);
  }

  .alert-content {
    min-width: 200px;
    flex: 1 1 60%;
  }

  .action-buttons {
    flex: 0 0 auto;
  }

  .status-chip {
    transition: all 0.3s ease;
  }

  .edit-btn {
    transition: transform 0.2s ease;
  }

  .edit-btn:hover {
    transform: translateY(-1px);
  }

  .submitted-time {
    align-self: flex-end;
  }

  .animate-pulse {
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }
</style>
