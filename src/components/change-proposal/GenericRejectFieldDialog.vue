<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useGenericRejection } from '@/composables/generic-rejection/useGenericRejection'
import { useParticipants } from '@/composables/participants/useParticipants'

interface FieldItem {
  id: string
  name: string
  field: string
  value: any
  disabled: boolean
  typename?: string
  __typename?: string
  rejectReason?: string
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  field: {
    type: Object as () => FieldItem | null,
    required: true,
  },
  participantName: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'close', 'rejected'])

const { rejectField: rejectGenericField, rejecting } = useGenericRejection()
const { state: { currentUserId } } = useParticipants()

const dialog = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

const reasons = ref('')
const validationError = ref(false)

// Determine entity type from field typename or __typename
const getEntityType = (field: FieldItem): string => {
  const typename = field.typename || field.__typename || ''

  // Map GraphQL types to entity types for the generic service
  const typeMap: Record<string, string> = {
    PersonalInfo: 'personalInfo',
    PartnerInfo: 'partnerInfo',
    Address: 'address',
    Child: 'child',
    EmploymentInfo: 'employmentInfo',
    PensionInfo: 'pensionInfo',
    SalaryEntry: 'salaryEntry',

    // Certified entities
    CertifiedPersonalInfo: 'certifiedPersonalInfo',
    CertifiedPartnerInfo: 'certifiedPartnerInfo',
    CertifiedAddress: 'certifiedAddress',
    CertifiedChild: 'certifiedChild',
    CertifiedEmploymentInfo: 'certifiedEmploymentInfo',
    CertifiedPensionInfo: 'certifiedPensionInfo',
    CertifiedSalaryEntry: 'certifiedSalaryEntry',
  }

  return typeMap[typename] || 'personalInfo' // Default fallback
}

const closeDialog = () => {
  dialog.value = false
  emit('close')
}

const rejectField = async () => {
  if (!reasons.value.trim()) {
    validationError.value = true

    return
  }

  try {
    if (props.field) {
      const entityType = getEntityType(props.field)

      const result = await rejectGenericField({
        entityId: props.field.id,
        entityType,
        fieldName: props.field.field,
        rejectReason: reasons.value.trim(),
        userId: currentUserId.value,
      })

      emit('rejected', {
        field: props.field.field,
        reason: reasons.value.trim(),
        entityId: props.field.id,
        entityType,
        result,
      })
    }
  }
  catch (error) {
    console.error('Error rejecting field:', error)
  }
  finally {
    closeDialog()
  }
}

// Reset validation when dialog model value changes
watch(() => props.modelValue, () => {
  validationError.value = false
  reasons.value = ''
})
</script>

<template>
  <VDialog
    v-model="dialog"
    max-width="500"
  >
    <VCard v-if="field">
      <VCardTitle class="text-h5">
        Reject Field
      </VCardTitle>
      <VCardText>
        <div class="mb-4">
          You are about to reject the <strong>{{ field.name }}</strong> field for this participant.
        </div>
        <div class="d-flex flex-column gap-2">
          <div>
            <span class="text-subtitle-2 font-weight-medium">Field:</span>
            <span class="ml-2">{{ field.name }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Current Value:</span>
            <span class="ml-2">{{ field.value }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Participant:</span>
            <span class="ml-2">{{ participantName }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Entity Type:</span>
            <span class="ml-2">{{ getEntityType(field) }}</span>
          </div>

          <div class="mb-1 mt-4">
            Reason for rejection <span class="text-error">*</span>
          </div>
          <VTextarea
            v-model="reasons"
            placeholder="Please provide a detailed explanation for rejecting this field..."
            :error="validationError"
            :error-messages="validationError ? 'Please provide a reason for rejection' : ''"
            variant="outlined"
            rows="5"
            auto-grow
            class="mt-1"
          />
        </div>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          :disabled="rejecting"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="error"
          :loading="rejecting"
          @click="rejectField"
        >
          Reject Field
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
