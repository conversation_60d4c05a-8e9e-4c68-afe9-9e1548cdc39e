<script setup lang="ts">
import { computed, ref } from 'vue'
import { formatDateToString } from '@core/utils/formatters'

const props = defineProps({
  changeProposalsList: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['update:filters'])

const isFilterPanelOpen = ref(false)
const loadingFilteredChanges = ref(false)

const filterOptions = computed(() => {
  const participants = new Set()
  const fields = new Set()
  const proposedBy = new Set()
  const statuses = new Set()

  props.changeProposalsList.forEach(item => {
    participants.add(item.participant)
    fields.add(item.field)
    proposedBy.add(item.proposedBy)
    statuses.add(item.status)
  })

  return {
    participants: Array.from(participants).map(item => ({ id: item, text: item })),
    fields: Array.from(fields).map(item => ({ id: item, text: item })),
    proposedBy: Array.from(proposedBy).map(item => ({ id: item, text: item })),
    statuses: Array.from(statuses).map((item: any) => ({
      id: item,
      text: item.charAt(0).toUpperCase() + item.slice(1),
    })),
  }
})

// Filter state
const filters = ref({
  participant: null,
  field: null,
  status: null,
  proposedBy: null,
  effectiveDateRange: {
    startDate: null,
    endDate: null,
  },
})

// Date range menu state
const dateRangeMenu = ref(false)

const dateRangeDisplay = computed(() => {
  const { startDate, endDate } = filters.value.effectiveDateRange
  if (startDate && endDate)
    return `${formatDateToString(startDate)} - ${formatDateToString(endDate)}`

  return 'Select Date Range'
})

// Method to clear all filters
const resetFilters = () => {
  filters.value = {
    participant: null,
    field: null,
    status: null,
    proposedBy: null,
    effectiveDateRange: {
      startDate: null,
      endDate: null,
    },
  }
  dateRangeMenu.value = false
  emit('update:filters', {})
}

// Clear date range filter
const clearDateRangeFilter = () => {
  filters.value.effectiveDateRange.startDate = null
  filters.value.effectiveDateRange.endDate = null
  dateRangeMenu.value = false
}

// Toggle filter panel
const toggleFilterPanel = () => {
  isFilterPanelOpen.value = !isFilterPanelOpen.value
}

// Apply filters
const applyFilters = () => {
  loadingFilteredChanges.value = true

  // Prepare filters for emitting
  const preparedFilters = {
    participant: filters.value.participant?.id || null,
    field: filters.value.field?.id || null,
    status: filters.value.status?.id || null,
    proposedBy: filters.value.proposedBy?.id || null,
    effectiveDateRange: {
      startDate: filters.value.effectiveDateRange.startDate,
      endDate: filters.value.effectiveDateRange.endDate,
    },
  }

  emit('update:filters', preparedFilters)

  // Simulate loading
  setTimeout(() => {
    loadingFilteredChanges.value = false
  }, 500)
}

// Count applied filters
const appliedFiltersCount = computed(() => {
  let count = 0
  if (filters.value.participant)
    count++
  if (filters.value.field)
    count++
  if (filters.value.status)
    count++
  if (filters.value.proposedBy)
    count++
  if (filters.value.effectiveDateRange.startDate && filters.value.effectiveDateRange.endDate)
    count++

  return count
})
</script>

<template>
  <div class="filter-component mb-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <VBtn
        prepend-icon="tabler-filter"
        color="primary"
        size="small"
        variant="outlined"
        class="filter-btn"
        @click="toggleFilterPanel"
      >
        Filter
        <template #append>
          <VBadge
            v-if="appliedFiltersCount > 0"
            color="error"
            :content="appliedFiltersCount"
            inline
          />
        </template>
      </VBtn>
    </div>

    <VExpandTransition>
      <VCard
        v-if="isFilterPanelOpen"
        variant="outlined"
        class="mb-4"
      >
        <VCardText>
          <VRow>
            <VCol
              v-if="filters.participant"
              cols="12"
              sm="6"
              md="4"
            >
              <VSelect
                v-model="filters.participant"
                :disabled="loadingFilteredChanges"
                :items="filterOptions.participants"
                item-title="text"
                item-value="id"
                label="Participant"
                prepend-inner-icon="tabler-user"
                clearable
                density="compact"
                variant="outlined"
                return-object
              />
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VSelect
                v-model="filters.field"
                :disabled="loadingFilteredChanges"
                :items="filterOptions.fields"
                item-title="text"
                item-value="id"
                label="Field"
                prepend-inner-icon="tabler-edit"
                clearable
                density="compact"
                variant="outlined"
                return-object
              />
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VSelect
                v-model="filters.status"
                :disabled="loadingFilteredChanges"
                :items="filterOptions.statuses"
                item-title="text"
                item-value="id"
                label="Status"
                prepend-inner-icon="tabler-circle-check"
                clearable
                density="compact"
                variant="outlined"
                return-object
              />
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VSelect
                v-model="filters.proposedBy"
                :disabled="loadingFilteredChanges"
                :items="filterOptions.proposedBy"
                item-title="text"
                item-value="id"
                label="Proposed By"
                prepend-inner-icon="tabler-send"
                clearable
                density="compact"
                variant="outlined"
                return-object
              />
            </VCol>

            <VCol
              cols="12"
              sm="6"
              md="4"
            >
              <VMenu
                v-model="dateRangeMenu"
                :disabled="loadingFilteredChanges"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template #activator="{ props }">
                  <VTextField
                    v-bind="props"
                    v-model="dateRangeDisplay"
                    label="Effective Date Range"
                    prepend-inner-icon="tabler-calendar"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearDateRangeFilter"
                  />
                </template>

                <VCard min-width="300px">
                  <VCardText>
                    <VRow>
                      <VCol cols="12">
                        <VTextField
                          v-model="filters.effectiveDateRange.startDate"
                          :disabled="loadingFilteredChanges"
                          label="Start Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </VCol>

                      <VCol cols="12">
                        <VTextField
                          v-model="filters.effectiveDateRange.endDate"
                          :disabled="loadingFilteredChanges"
                          label="End Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </VCol>
                    </VRow>
                  </VCardText>
                  <VCardActions>
                    <VSpacer />
                    <VBtn
                      color="primary"
                      @click="clearDateRangeFilter"
                    >
                      Clear
                    </VBtn>
                    <VBtn
                      color="success"
                      @click="dateRangeMenu = false"
                    >
                      Apply
                    </VBtn>
                  </VCardActions>
                </VCard>
              </VMenu>
            </VCol>
          </VRow>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn
            variant="text"
            size="small"
            prepend-icon="tabler-x"
            class="clear-btn mx-4"
            @click="resetFilters"
          >
            Clear All
          </VBtn>
          <VBtn
            :loading="loadingFilteredChanges"
            :disabled="appliedFiltersCount === 0 || loadingFilteredChanges"
            variant="outlined"
            color="primary"
            @click="applyFilters"
          >
            Apply Filters
          </VBtn>
        </VCardActions>
      </VCard>
    </VExpandTransition>
  </div>
</template>

<style scoped>
  .filter-component {
    width: 100%;
  }
</style>
