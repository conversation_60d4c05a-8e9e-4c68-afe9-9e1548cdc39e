<script setup lang="ts">
import { computed, ref } from 'vue'
import type { ParticipantChange } from '@/types/participant.types'
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import {
  pensionParamChangeHistoryHeaders,
} from '@/components/change-proposal/ChangeProposalTableHeaders'

const {
  state: { pensionParamChangeProposalsHistoryList, loadingPensionParamChangeProposalsHistory: loadingTable, isLoading: loadingAction },
  actions: { handleApproveChangeProposal, handleRejectChangeProposal },
} = useChangeProposal()

const searchInput = ref('')
const isLoading = ref(false)
const appliedFilters = ref({})
const itemsPerPage = ref(10)
const page = ref(1)

// Filtered data based on search and advanced filters
const filteredChangeProposals = computed(() => {
  let filtered = [...pensionParamChangeProposalsHistoryList.value]

  if (searchInput.value) {
    const searchLower = searchInput.value.toLowerCase()

    filtered = filtered.filter(item =>
      item.participant.toLowerCase().includes(searchLower)
        || item.field.toLowerCase().includes(searchLower)
        || item.proposedBy.toLowerCase().includes(searchLower),
    )
  }

  // Apply advanced filters
  if (appliedFilters.value) {
    const filters = appliedFilters.value as any

    if (filters.participant)
      filtered = filtered.filter(item => item.participant === filters.participant)

    if (filters.field)
      filtered = filtered.filter(item => item.field === filters.field)

    if (filters.status)
      filtered = filtered.filter(item => item.status.toLowerCase() === filters.status.toLowerCase())

    if (filters.proposedBy)
      filtered = filtered.filter(item => item.proposedBy === filters.proposedBy)

    if (filters.effectiveDateRange && filters.effectiveDateRange.startDate && filters.effectiveDateRange.endDate) {
      const startDate = new Date(filters.effectiveDateRange.startDate)
      const endDate = new Date(filters.effectiveDateRange.endDate)

      filtered = filtered.filter(item => {
        const effectiveDate = new Date(item.effectiveDate)

        return effectiveDate >= startDate && effectiveDate <= endDate
      })
    }
  }

  return filtered
})

// Pagination
const paginatedData = computed(() => {
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return filteredChangeProposals.value.slice(start, end)
})

const totalItems = computed(() => filteredChangeProposals.value.length)

const paginationMeta = computed(() => {
  const start = (page.value - 1) * itemsPerPage.value + 1
  const end = Math.min(start + itemsPerPage.value - 1, totalItems.value)

  return `Showing ${start} to ${end} of ${totalItems.value} entries`
})

const updateFilters = (newFilters: any) => {
  isLoading.value = true
  appliedFilters.value = newFilters
  page.value = 1

  setTimeout(() => {
    isLoading.value = false
  }, 200)
}

const handleApprove = (change: ParticipantChange) => {
  handleApproveChangeProposal(change.id)
}

const handleReject = (change: ParticipantChange) => {
  handleRejectChangeProposal(change.id)
}

const resetAllFilters = () => {
  searchInput.value = ''
  appliedFilters.value = {}
}

// Expanded rows for rejected items
const expandedRows = computed(() => {
  return paginatedData.value
    .filter(item => item.status === 'rejected')
    .map(item => item.id)
})
</script>

<template>
  <VSkeletonLoader
    v-if="loadingTable"
    class="mx-auto border py-6"
    type="table"
  />
  <VCard
    v-else
    variant="outlined"
    title="Pension Parameters Change History"
    class="mb-4 pa-2"
  >
    <VExpandTransition>
      <div>
        <VCardText class="pt-0 pb-0">
          <ChangeFilterTest
            :change-proposals-list="pensionParamChangeProposalsHistoryList"
            @update:filters="updateFilters"
          />

          <!-- Search input -->
          <div class="d-flex justify-end align-center mb-4">
            <VTextField
              v-model="searchInput"
              placeholder="Search by participant, field, or proposer..."
              density="compact"
              hide-details
              variant="outlined"
              bg-color="white"
              class="max-width-400"
              prepend-inner-icon="mdi-magnify"
              clearable
              @click:clear="searchInput = ''"
            />
          </div>
        </VCardText>

        <VDataTable
          :headers="pensionParamChangeHistoryHeaders"
          :items="paginatedData"
          :items-per-page="-1"
          :loading="isLoading"
          class="elevation-0"
          :expanded="expandedRows"
          show-expand
        >
          <template #expanded-row="slotProps">
            <tr class="v-data-table__tr expanded-row">
              <td :colspan="pensionParamChangeHistoryHeaders.length">
                <p class="reviewComment font-italic">
                  {{ slotProps.item.reviewComments }}
                </p>
              </td>
            </tr>
          </template>

          <!-- Current value column formatting -->
          <template #item.currentValue="{ item }">
            <span v-if="item.currentValue === '[object Object]'">[object Object]</span>
            <span v-else-if="item.currentValue === '-'">-</span>
            <span v-else-if="typeof item.currentValue === 'string' && item.currentValue.endsWith('%')">
              {{ item.currentValue }}
            </span>
            <span v-else>{{ item.currentValue }}</span>
          </template>

          <!-- New value column formatting with color highlight -->
          <template #item.newValue="{ item }">
            <span
              v-if="item.field === 'email'"
              class="blue--text"
            >{{ item.newValue }}</span>
            <span
              v-else-if="item.field === 'date of birth'"
              class="blue--text"
            >{{ item.newValue }}</span>
            <span
              v-else-if="typeof item.newValue === 'string' && item.newValue.includes('%')"
              class="blue--text"
            >
              {{ item.newValue }}
            </span>
            <span v-else>{{ item.newValue }}</span>
          </template>

          <template #[`item.status`]="{ item }">
            <div class="d-flex flex-wrap chip-wrapper">
              <VChip
                v-if="item.status === 'rejected'"
                size="small"
                prepend-icon="tabler-x"
                color="error"
                text="Rejected"
              />
              <VChip
                v-if="item.status === 'approved'"
                size="small"
                prepend-icon="tabler-check"
                color="success"
                text="Approved"
              />
            </div>
          </template>

          <template #no-data>
            <div class="d-flex flex-column justify-center align-center pa-4">
              <span class="text-subtitle-1 mb-2">No change history found</span>
              <VBtn
                v-if="searchInput || Object.keys(appliedFilters).length > 0"
                density="compact"
                variant="outlined"
                size="small"
                color="primary"
                @click="resetAllFilters"
              >
                Clear Filters
              </VBtn>
            </div>
          </template>

          <!-- Pagination -->
          <template #bottom>
            <VDivider />
            <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3">
              <p class="text-sm text-disabled mb-0">
                {{ paginationMeta }}
              </p>

              <VPagination
                v-model="page"
                :length="Math.ceil(totalItems / itemsPerPage)"
                :total-visible="$vuetify ? ($vuetify.display.xs ? 1 : $vuetify.display.md ? 5 : 7) : 7"
              >
                <template #prev="slotProps">
                  <VBtn
                    variant="tonal"
                    color="default"
                    v-bind="slotProps"
                    :icon="false"
                  >
                    Previous
                  </VBtn>
                </template>

                <template #next="slotProps">
                  <VBtn
                    variant="tonal"
                    color="default"
                    v-bind="slotProps"
                    :icon="false"
                  >
                    Next
                  </VBtn>
                </template>
              </VPagination>

              <VSelect
                v-model="itemsPerPage"
                :items="[5, 10, 25, 50]"
                label="Per page"
                density="compact"
                variant="outlined"
                hide-details
                class="items-per-page-select"
                style="width: 100px;"
              />
            </div>
          </template>
        </VDataTable>
      </div>
    </VExpandTransition>
  </VCard>
</template>

<style scoped>
  .max-width-400 {
    max-width: 400px;
  }

  .blue--text {
    color: #1976d2 !important;
  }

  .items-per-page-select {
    min-width: 80px;
  }

  .expanded-row {
    background-color: rgba(254,243,242,0.8);
  }

  .v-data-table__tr + .expanded-row {
    border-top: none !important; /* Remove border between expanded row and its parent */
  }
  .wide-column {
    width: 400px !important;
  }
  .reviewComment{
    color: red;
  }
</style>
