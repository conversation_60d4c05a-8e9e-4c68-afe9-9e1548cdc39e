<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { usePersonalInfoReject } from '@/composables/personal-info/usePersonalInfoReject'
import { useParticipants } from '@/composables/participants/useParticipants'

interface FieldItem {
  id: string
  name: string
  field: string
  value: any
  disabled: boolean
  typename?: string
  rejectReason?: string
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  field: {
    type: Object as () => FieldItem | null,
    required: true,
  },
  participantName: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'close', 'rejected'])

const { rejectField: rejectPersonalInfoField, rejecting } = usePersonalInfoReject()
const { state: { currentUserId } } = useParticipants()

const dialog = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

const reasons = ref('')
const validationError = ref(false)

const closeDialog = () => {
  dialog.value = false
  emit('close')
}

const rejectField = async () => {
  if (!reasons.value.trim()) {
    validationError.value = true

    return
  }

  try {
    if (props.field) {
      const result = await rejectPersonalInfoField(
        props.field.id,
        props.field.field,
        reasons.value.trim(),
        currentUserId.value,
      )

      emit('rejected', {
        field: props.field.field,
        reason: reasons.value.trim(),
        personalInfoId: props.field.id,
        result,
      })
    }
  }
  catch (error) {
    console.error('Error rejecting field:', error)
  }
  finally {
    closeDialog()
  }
}

// Reset validation when dialog model value changes
watch(() => props.modelValue, () => {
  validationError.value = false
  reasons.value = ''
})
</script>

<template>
  <VDialog
    v-model="dialog"
    max-width="500"
  >
    <VCard v-if="field">
      <VCardTitle class="text-h5">
        Reject Field
      </VCardTitle>
      <VCardText>
        <div class="mb-4">
          You are about to reject the <strong>{{ field.name }}</strong> field for this participant.
        </div>
        <div class="d-flex flex-column gap-2">
          <div>
            <span class="text-subtitle-2 font-weight-medium">Field:</span>
            <span class="ml-2">{{ field.name }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Current Value:</span>
            <span class="ml-2">{{ field.value }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Participant:</span>
            <span class="ml-2">{{ participantName }}</span>
          </div>

          <div class="mb-1 mt-4">
            Reason for rejection <span class="text-error">*</span>
          </div>
          <VTextarea
            v-model="reasons"
            placeholder="Please provide a detailed explanation for rejecting this field..."
            :error="validationError"
            :error-messages="validationError ? 'Please provide a reason for rejection' : ''"
            variant="outlined"
            rows="5"
            auto-grow
            class="mt-1"
          />
        </div>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="error"
          @click="rejectField"
        >
          Reject Field
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
