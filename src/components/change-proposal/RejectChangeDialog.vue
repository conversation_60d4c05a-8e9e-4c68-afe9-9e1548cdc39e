<script setup lang="ts">
import { ref } from 'vue'

// Props to control dialog visibility and handle events
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  proposalId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['close', 'reject'])

const rejectionReason = ref('')
const validationError = ref(false)

const closeDialog = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  rejectionReason.value = ''
  validationError.value = false
  emit('close')
}

const confirmRejection = async () => {
  try {
    if (!rejectionReason.value.trim()) {
      validationError.value = true

      return
    }

    emit('reject', {
      proposalId: props.proposalId,
      reason: rejectionReason.value,
    })

    resetForm()
  }
  catch (error) {
    console.error('Error rejecting change proposal:', error)
  }
}
</script>

<template>
  <VDialog
    :model-value="isOpen"
    :width="$vuetify.display.smAndDown ? 'auto' : 550"
    class="reject-dialog"
    @update:model-value="closeDialog"
  >
    <VCard class="pa-6">
      <VCardTitle class="text-h5 font-weight-medium pb-2">
        Reject Change Proposal
      </VCardTitle>

      <VCardText class="pt-4 pb-0">
        <div class="mb-1">
          Reason for Rejection <span class="text-error">*</span>
        </div>
        <VTextarea
          v-model="rejectionReason"
          placeholder="Please provide a detailed explanation for rejecting this change proposal..."
          :error="validationError"
          :error-messages="validationError ? 'Please provide a reason for rejection' : ''"
          variant="outlined"
          rows="7"
          auto-grow
          class="mt-1"
        />
      </VCardText>

      <VDivider class="my-4" />

      <VCardActions class="pa-0 d-flex justify-end">
        <VBtn
          variant="text"
          color="default"
          @click="closeDialog"
        >
          Cancel
        </VBtn>

        <VBtn
          color="primary"
          variant="flat"
          @click="confirmRejection"
        >
          Confirm Rejection
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped>
  .reject-dialog :deep(.v-textarea .v-field__input) {
    min-height: 120px;
  }
</style>
