<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'
import { useAppStore } from '@/stores/app/appStore'

interface Props {
  modelValue: boolean
  certificationYear: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'reverted'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const appStore = useAppStore()

const {
  state: {
    loadingPreviewRevertChanges,
    loadingRevertChanges,
  },
  actions: {
    handlePreviewRevertChanges,
    handleRevertAllChanges,
  },
} = useCertifiedDataByYearAndYearBefore()

const previewData = ref<any>(null)
const isLoading = ref(false)

// Computed property for dialog visibility
const isDialogOpen = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value)
  },
})

watch(() => props.modelValue, async newValue => {
  if (newValue)
    await fetchPreviewData()
  else
    previewData.value = null
})

// Helper function to format entity type names
const formatEntityType = (entityType: string): string => {
  const typeMap: Record<string, string> = {
    certifiedPersonalInfo: 'Personal Info',
    certifiedPartnerInfo: 'Partner Info',
    certifiedChild: 'Child',
    certifiedAddress: 'Address',
    certifiedSalaryPensionBase: 'Salary Pension Base',
    certifiedPensionCalculationBase: 'Pension Calculation Base',
    certifiedBasicInformation: 'Basic Information',
    certifiedParticipantInformation: 'Participant Information',
  }

  return typeMap[entityType] || entityType.replace(/([A-Z])/g, ' $1').trim()
}

// Helper function to format field names
const formatFieldName = (fieldName: string): string => {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

// Computed property to check if there are any entities affected
const hasAffectedEntities = computed(() => {
  return previewData.value?.affectedEntities?.length > 0
})

const fetchPreviewData = async () => {
  try {
    isLoading.value = true

    const result = await handlePreviewRevertChanges()

    console.log('Preview result:', result) // Debug log
    previewData.value = result
  }
  catch (error) {
    console.error('Error fetching preview data:', error)
    appStore.showSnack('Error loading preview data')
  }
  finally {
    isLoading.value = false
  }
}

const handleRevert = async () => {
  try {
    const result = await handleRevertAllChanges()
    if (result?.success) {
      appStore.showSnack('Changes reverted successfully')
      emit('reverted')
      closeDialog()
    }
    else {
      appStore.showSnack('Error reverting changes')
    }
  }
  catch (error) {
    console.error('Error reverting changes:', error)
    appStore.showSnack('Error reverting changes')
  }
}

const closeDialog = () => {
  isDialogOpen.value = false
}
</script>

<template>
  <VDialog
    v-model="isDialogOpen"
    max-width="800px"
    persistent
  >
    <VCard>
      <VCardTitle class="text-h5 pa-4">
        Revert All Changes Preview
      </VCardTitle>

      <VCardText class="pa-4">
        <!-- Loading state -->
        <div
          v-if="isLoading || loadingPreviewRevertChanges"
          class="text-center py-8"
        >
          <VProgressCircular
            indeterminate
            size="64"
            color="primary"
          />
          <div class="mt-4 text-body-1">
            Loading preview data...
          </div>
        </div>

        <!-- Preview data -->
        <template v-else-if="previewData">
          <div class="text-subtitle-1 font-weight-bold mb-4">
            {{ previewData.estimatedImpact }}
          </div>

          <div class="mb-4">
            <VRow>
              <VCol cols="4">
                <VCard
                  variant="outlined"
                  class="pa-3 text-center"
                >
                  <div class="text-h6 text-primary">
                    {{ previewData.totalCertifiedDataRecords }}
                  </div>
                  <div class="text-caption">
                    Total Records
                  </div>
                </VCard>
              </VCol>
              <VCol cols="4">
                <VCard
                  variant="outlined"
                  class="pa-3 text-center"
                >
                  <div class="text-h6 text-warning">
                    {{ previewData.totalEntitiesAffected }}
                  </div>
                  <div class="text-caption">
                    Entities Affected
                  </div>
                </VCard>
              </VCol>
              <VCol cols="4">
                <VCard
                  variant="outlined"
                  class="pa-3 text-center"
                >
                  <div class="text-h6 text-error">
                    {{ previewData.totalRejectionReasons }}
                  </div>
                  <div class="text-caption">
                    Rejection Reasons
                  </div>
                </VCard>
              </VCol>
            </VRow>
          </div>

          <VExpansionPanels
            v-if="hasAffectedEntities"
            class="mb-4"
          >
            <VExpansionPanel>
              <VExpansionPanelTitle>
                <div class="d-flex align-center">
                  <VIcon class="mr-2">
                    mdi-account-group
                  </VIcon>
                  <span>Affected Entities ({{ previewData.affectedEntities.length }})</span>
                </div>
              </VExpansionPanelTitle>
              <VExpansionPanelText>
                <div class="mt-4">
                  <VCard
                    v-for="entity in previewData.affectedEntities"
                    :key="entity.entityId"
                    variant="outlined"
                    class="mb-4"
                  >
                    <VCardTitle class="pb-2">
                      <div class="d-flex align-center">
                        <VAvatar
                          color="primary"
                          size="small"
                          class="mr-3"
                        >
                          <VIcon size="small">
                            mdi-account
                          </VIcon>
                        </VAvatar>
                        <div>
                          <div class="text-h6">
                            {{ formatEntityType(entity.entityType) }}
                          </div>
                        </div>
                      </div>
                    </VCardTitle>

                    <VCardText class="pt-0">
                      <div class="mb-3">
                        <VChip
                          v-if="entity.approvedChangesCount > 0"
                          color="success"
                          size="small"
                          class="mr-2 mb-1"
                        >
                          <VIcon
                            start
                            size="small"
                          >
                            mdi-check
                          </VIcon>
                          {{ entity.approvedChangesCount }} Approved
                        </VChip>
                        <VChip
                          v-if="entity.requestedChangesCount > 0"
                          color="warning"
                          size="small"
                          class="mr-2 mb-1"
                        >
                          <VIcon
                            start
                            size="small"
                          >
                            mdi-clock
                          </VIcon>
                          {{ entity.requestedChangesCount }} Requested
                        </VChip>
                      </div>

                      <!-- Approved Changes -->
                      <div
                        v-if="entity.approvedChanges?.length"
                        class="mb-3"
                      >
                        <div class="text-subtitle-2 text-success mb-2">
                          <VIcon
                            size="small"
                            class="mr-1"
                          >
                            mdi-check-circle
                          </VIcon>
                          Approved Changes
                        </div>
                        <div class="ml-6">
                          <VChip
                            v-for="field in entity.approvedChanges"
                            :key="field"
                            color="success"
                            variant="outlined"
                            size="small"
                            class="mr-1 mb-1"
                          >
                            {{ formatFieldName(field) }}
                          </VChip>
                        </div>
                      </div>

                      <!-- Requested Changes -->
                      <div
                        v-if="entity.requestedChanges?.length"
                        class="mb-3"
                      >
                        <div class="text-subtitle-2 text-warning mb-2">
                          <VIcon
                            size="small"
                            class="mr-1"
                          >
                            mdi-clock-outline
                          </VIcon>
                          Requested Changes
                        </div>
                        <div class="ml-6">
                          <VChip
                            v-for="field in entity.requestedChanges"
                            :key="field"
                            color="warning"
                            variant="outlined"
                            size="small"
                            class="mr-1 mb-1"
                          >
                            {{ formatFieldName(field) }}
                          </VChip>
                        </div>
                      </div>
                    </VCardText>
                  </VCard>
                </div>
              </VExpansionPanelText>
            </VExpansionPanel>
          </VExpansionPanels>

          <VAlert
            type="warning"
            variant="tonal"
            class="mb-4"
          >
            <template #prepend>
              <VIcon>mdi-alert-triangle</VIcon>
            </template>
            <strong>Warning:</strong> This action will revert all approved and rejected changes for the certification year {{ certificationYear }}. This action cannot be undone.
          </VAlert>
        </template>

        <!-- No data state -->
        <div
          v-else
          class="text-center py-8"
        >
          <VIcon
            size="64"
            color="grey"
          >
            mdi-information-outline
          </VIcon>
          <div class="mt-4 text-body-1">
            No preview data available
          </div>
        </div>

        <!-- No affected entities state -->
        <VAlert
          v-if="previewData && !hasAffectedEntities"
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <template #prepend>
            <VIcon>mdi-information</VIcon>
          </template>
          <strong>No Changes to Revert:</strong> There are no approved or requested changes for the certification year {{ certificationYear }}.
        </VAlert>
      </VCardText>

      <VCardActions class="pa-4">
        <VSpacer />
        <VBtn
          color="grey"
          variant="text"
          :disabled="loadingRevertChanges"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="error"
          variant="flat"
          :loading="loadingRevertChanges"
          :disabled="!previewData || isLoading || !hasAffectedEntities"
          @click="handleRevert"
        >
          <VIcon class="mr-2">
            mdi-undo
          </VIcon>
          Revert All Changes
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
