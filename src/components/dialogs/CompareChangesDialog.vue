<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useChangeProposalStore } from '@/stores/change-proposals/changeProposalStore'
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import { usePensionStore } from '@/stores/pension/pensionStore'

const emit = defineEmits(['close', 'confirm'])

// Use the store instead of props
const store = useChangeProposalStore()
const pensionStore = usePensionStore()
const selectedChange = ref<'latest' | 'new' | null>(null)

// Computed property for dialog visibility based on store state
const isDialogOpen = computed({
  get: () => store.isCompareDialogOpen,
  set: value => {
    if (!value)
      closeDialog()
  },
})

const {
  actions: { handleApproveChangeProposal },
} = useChangeProposal()

const activeProposal = computed(() => store.getActiveProposal)

// Check if we have a latest approved change
const hasLatestChange = computed(() => {
  const latestApprovalChange = store.getLatestApprovalChange

  return !!latestApprovalChange && !!latestApprovalChange.changes && latestApprovalChange.changes.length > 0
})

// Watch for dialog opening to set default selection
watch(() => store.isCompareDialogOpen, newVal => {
  if (newVal) {
    // If there's no latest change, automatically select the new change
    // Otherwise, select the most recent one based on effective date
    selectedChange.value = !hasLatestChange.value ? 'new' : (isLatestMostRecent.value ? 'latest' : 'new')
  }
})

// Process latest approved change data
const latestChange = computed(() => {
  const latestApprovalChange = store.getLatestApprovalChange
  if (!latestApprovalChange)
    return {}

  return {
    id: latestApprovalChange.id,
    entityType: latestApprovalChange.entityType,
    path: latestApprovalChange.changes?.[0]?.path || '-',
    newValue: latestApprovalChange.changes?.[0]?.newValue || '-',
    effectiveDate: latestApprovalChange.effectiveDate,
    reviewedBy: latestApprovalChange.reviewedBy,
    reviewedAt: latestApprovalChange.reviewedAt,
    createdAt: latestApprovalChange.createdAt,
  }
})

// Process new proposed change data
const newChange = computed(() => {
  if (!activeProposal.value?.rawData?.proposal)
    return {}

  return {
    id: activeProposal.value.rawData.proposal.id,
    entityType: activeProposal.value.rawData.proposal.entityType,
    path: activeProposal.value.rawData.change?.path || activeProposal.value.field || '-',
    newValue: activeProposal.value.rawData.change?.newValue || activeProposal.value.newValue || '-',
    effectiveDate: activeProposal.value.rawData.proposal.effectiveDate || activeProposal.value.effectiveDate,
    proposedBy: activeProposal.value.rawData.proposal.createdBy || {
      email: activeProposal.value.proposedBy,
    },
    createdAt: activeProposal.value.rawData.proposal.createdAt,
  }
})

// Determine which change is most recent based on effective date
const isLatestMostRecent = computed(() => {
  if (!hasLatestChange.value || !latestChange.value.effectiveDate || !newChange.value.effectiveDate)
    return false

  const latestDate = new Date(latestChange.value.effectiveDate)
  const newDate = new Date(newChange.value.effectiveDate)

  return latestDate >= newDate
})

const entityTitle = computed(() => {
  const latestApprovalChange = store.getLatestApprovalChange

  // const activeProposal = store.getActiveProposal;

  const latestName = latestApprovalChange?.participantName
  const newName = activeProposal.value?.participant || activeProposal.value?.rawData?.proposal?.participantName

  return `${latestChange.value.entityType || newChange.value.entityType} - ${latestName || newName || ''}`
})

const formatDate = (dateString: string | null) => {
  if (!dateString)
    return '-'

  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

const formatValue = (value: any) => {
  if (value === null || value === undefined)
    return '-'

  if (typeof value === 'object' && value !== null) {
    if (value.day && value.month && value.year) {
      // Format date object
      return new Date(value.year, value.month - 1, value.day).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    }

    return '[object Object]'
  }

  return String(value)
}

const closeDialog = () => {
  store.closeCompareDialog()
  store.resetCompareData()
  emit('close')
}

const confirmSelection = async () => {
  if (!selectedChange.value)
    return

  const selectedData = selectedChange.value === 'latest'
    ? latestChange.value
    : newChange.value

  const isPropagated = selectedChange.value === 'new'
  const propId = activeProposal.value?.rawData?.proposal?.id || newChange.value.id

  await handleApproveChangeProposal(propId, isPropagated)
  console.log('selectedChange', propId, isPropagated, selectedChange.value)

  closeDialog()
}
</script>

<template>
  <VDialog
    v-model="isDialogOpen"
    max-width="1000px"
    persistent
  >
    <VCard>
      <VCardTitle class="d-flex align-center">
        <span class="text-h6">{{ hasLatestChange ? 'Approve Changes' : 'New Proposed Change' }}</span>
        <VSpacer />
        <VBtn
          variant="text"
          icon="tabler-close"
          @click="closeDialog"
        />
      </VCardTitle>

      <VDivider />

      <div
        v-if="hasLatestChange"
        class="ma-4"
      >
        <VAlert
          border="start"
          color="primary"
          variant="tonal"
          class="py-4"
        >
          <div class="d-flex flex-column ga-2">
            <div class="text-body-1">
              Change on <span class="font-weight-bold">email</span> for data to be certified will be approved with effective date
              <span class="font-weight-bold text-primary">Dec 31, {{ pensionStore.middleColumnYear }}</span>.
            </div>

            <div class="text-caption">
              <span class="font-italic">Note:</span> The database contains changes with effective date after Dec 31, {{ pensionStore.middleColumnYear }}.
            </div>

            <VDivider class="my-2" />

            <div class="text-body-2 font-weight-medium">
              Please select below:
            </div>

            <ul
              class="pl-4"
              style="list-style-type: disc;"
            >
              <li class="text-body-2 mb-1">
                To approve the change <span class="font-weight-bold">only</span> for the data to be certified (Dec 31, {{ pensionStore.middleColumnYear }}),
                select <span class="font-weight-bold text-primary">'Keep Latest Approved Change'</span>
              </li>
              <li class="text-body-2">
                To apply the change to the current Year <span class="font-weight-bold text-primary">{{ pensionStore?.rightColumnYear }}</span>,
                select <span class="font-weight-bold text-primary">'Apply Change to current Year {{ pensionStore?.rightColumnYear }}'</span>
              </li>
            </ul>
          </div>
        </VAlert>
      </div>

      <VCardText class="pt-4">
        <VSkeletonLoader
          v-if="store.isLoading"
          type="table"
        />
        <div v-else>
          <h3 class="mb-3">
            {{ entityTitle }}
          </h3>

          <VRow>
            <!-- Only show this column if there is a latest approved change -->
            <VCol
              v-if="hasLatestChange"
              cols="12"
              :md="hasLatestChange ? 6 : 12"
            >
              <VCard
                class="mb-4"
                :class="{ 'selected-change': selectedChange === 'latest' }"
                outlined
                @click="selectedChange = 'latest'"
              >
                <VCardTitle class="d-flex align-center">
                  <VIcon left>
                    mdi-history
                  </VIcon>
                  <span>Latest Approved Change</span>
                  <VSpacer />
                  <VChip
                    v-if="isLatestMostRecent"
                    small
                    color="green"
                    text-color="white"
                  >
                    Most Recent
                  </VChip>
                </VCardTitle>
                <VDivider />
                <VCardText>
                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Entity Type:
                    </div>
                    <div>{{ latestChange.entityType }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Changed Field:
                    </div>
                    <div>{{ latestChange.path }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Value:
                    </div>
                    <div>{{ formatValue(latestChange.newValue) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Effective Date:
                    </div>
                    <div>{{ formatDate(latestChange.effectiveDate) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Approved By:
                    </div>
                    <div v-if="latestChange.reviewedBy">
                      {{ latestChange.reviewedBy.firstname }} {{ latestChange.reviewedBy.lastname }}
                      ({{ latestChange.reviewedBy.email }})
                    </div>
                    <div v-else>
                      N/A
                    </div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Approval Date:
                    </div>
                    <div>{{ formatDate(latestChange.reviewedAt) }}</div>
                  </div>
                </VCardText>
              </VCard>
            </VCol>

            <!-- Always show the new proposed change, adjust width based on whether there's a latest change -->
            <VCol
              cols="12"
              :md="hasLatestChange ? 6 : 12"
            >
              <VCard
                class="mb-4"
                :class="{ 'selected-change': selectedChange === 'new' }"
                outlined
                @click="selectedChange = 'new'"
              >
                <VCardTitle class="d-flex align-center">
                  <VIcon left>
                    mdi-pencil
                  </VIcon>
                  <span>New Proposed Change</span>
                  <VSpacer />
                  <VChip
                    v-if="!isLatestMostRecent && hasLatestChange"
                    small
                    color="green"
                    text-color="white"
                  >
                    Most Recent
                  </VChip>
                  <VChip
                    v-if="!hasLatestChange"
                    small
                    color="blue"
                    text-color="white"
                  >
                    First Change
                  </VChip>
                </VCardTitle>
                <VDivider />
                <VCardText>
                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Entity Type:
                    </div>
                    <div>{{ newChange.entityType }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Changed Field:
                    </div>
                    <div>{{ newChange.path }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Value:
                    </div>
                    <div>{{ formatValue(newChange.newValue) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Effective Date:
                    </div>
                    <div>{{ formatDate(newChange.effectiveDate) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Proposed By:
                    </div>
                    <div v-if="newChange.proposedBy">
                      {{ newChange.proposedBy.firstname }} {{ newChange.proposedBy.lastname }}
                      ({{ newChange.proposedBy.email }})
                    </div>
                    <div v-else>
                      N/A
                    </div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">
                      Proposal Date:
                    </div>
                    <div>{{ formatDate(newChange.createdAt) }}</div>
                  </div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>

          <VCard
            class="mt-4"
            outlined
          >
            <VCardTitle>Selected Change Summary for current year {{ }}</VCardTitle>
            <VDivider />
            <VCardText>
              <template v-if="selectedChange === 'latest'">
                <div class="mb-2">
                  <span class="font-weight-medium">You have selected the latest approved change:</span>
                </div>
                <div class="mb-1">
                  <span class="text-grey">Field:</span> {{ latestChange.path }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Value:</span> {{ formatValue(latestChange.newValue) }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Effective Date:</span> {{ formatDate(latestChange.effectiveDate) }}
                </div>
              </template>

              <template v-else-if="selectedChange === 'new'">
                <div class="mb-2">
                  <span class="font-weight-medium">You have selected the new proposed change:</span>
                </div>
                <div class="mb-1">
                  <span class="text-grey">Field:</span> {{ newChange.path }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Value:</span> {{ formatValue(newChange.newValue) }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Effective Date:</span> {{ formatDate(newChange.effectiveDate) }}
                </div>
              </template>

              <template v-else>
                <div class="text-grey font-italic">
                  Please select one of the changes above
                </div>
              </template>
            </VCardText>
          </VCard>
        </div>
      </VCardText>

      <VDivider />

      <VCardActions class="pa-4">
        <VSpacer />
        <VBtn
          variant="tonal"
          color="secondary"
          class="mr-2"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          variant="tonal"
          :disabled="!selectedChange"
          @click="confirmSelection"
        >
          {{ hasLatestChange ? 'Confirm Selection' : 'Approve Change' }}
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped>
  .selected-change {
    border: 2px solid #1976d2 !important;
    background-color: #f5f9ff;
  }

  .v-card {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .v-card:hover {
    background-color: #f5f5f5;
  }
</style>
