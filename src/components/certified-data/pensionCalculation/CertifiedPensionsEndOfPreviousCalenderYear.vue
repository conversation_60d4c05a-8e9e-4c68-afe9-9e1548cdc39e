<script setup lang="ts">
import { ref } from 'vue'
import SingleColumnTableLayout from './SingleColumnTableLayout.vue'
import SingleColumnTableRow from './SingleColumnTableRow.vue'
import CertifiedPensionBaseHeader from './CertifiedPensionBaseHeader.vue'
import { useCertifiedData } from '@/composables/certified-data'

const props = defineProps({
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const isPreviousYearExpanded = ref(true)

const { state: { normalizedParticipantCertifiedData } } = useCertifiedData()

const getColumnData = (year: number | null) => {
  if (!year)
    return null

  return normalizedParticipantCertifiedData.value?.[year]
}

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined)
    return 'N/A'

  return new Intl.NumberFormat('nl-AW', {
    style: 'currency',
    currency: 'AWG',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}
</script>

<template>
  <CertifiedPensionBaseHeader />
  <VCardText class="py-0">
    <VExpansionPanels
      v-model="isPreviousYearExpanded"
      class="mb-6"
    >
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Pensions end of previous calendar year
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <SingleColumnTableLayout>
            <template #header-label>
              <div class="d-flex align-center justify-space-between">
                <span>DESCRIPTION</span>
                <div class="text-caption text-right">
                  {{ props.year }}
                </div>
              </div>
            </template>

            <SingleColumnTableRow
              label="Code"
              :is-alternate="true"
            >
              <template #right>
                {{ getColumnData(props.year)?.certifiedPensionInfo?.code ?? 'N/A' }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="OP-TE">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedPensionInfo?.accruedGrossAnnualOldAgePension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow
              label="OP-TB"
              :is-alternate="true"
            >
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedPensionInfo?.attainableGrossAnnualOldAgePension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="WP-TE">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedPensionInfo?.accruedGrossAnnualPartnersPension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow
              label="ONP-TE"
              :is-alternate="true"
            >
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedPensionInfo?.accruedGrossAnnualSinglesPension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="AOP-TE">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedPensionInfo?.grossAnnualDisabilityPension) }}
              </template>
            </SingleColumnTableRow>
          </SingleColumnTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
