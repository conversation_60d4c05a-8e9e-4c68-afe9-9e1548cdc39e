<script setup lang="ts">
import { computed, ref } from 'vue'
import SingleColumnTableLayout from './SingleColumnTableLayout.vue'
import SingleColumnTableRow from './SingleColumnTableRow.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
import { useParticipants } from '@/composables/participants/useParticipants'
import { formatCurrency, formatPercentage } from '@/utils/transformers'
import type { PensionAccrualItem } from '@/types/participant.types'
import { usePensionCalculations } from '@/composables/pension-calculations/usePensionCalculations'
import { useCertifiedData } from '@/composables/certified-data'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'

// Define props to accept year as a parameter
const props = defineProps({
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const { state: { normalizePensionParams } } = usePensionParameters()
const { state: { normalizedParticipantSalaryEntries } } = useParticipants()
const { state: { normalizedParticipantCertifiedData } } = useCertifiedData()
const { actions: { calculateOpTeAccrualToReferenceDate, calculateWpTeAccrualToReferenceDate, calculateOpTeAccrualAfterReferenceDate } } = usePensionCalculations()

// Helper function to split a date into year, month, day components
const splitDate = (date: Date | string) => {
  if (!date)
    return { year: 2000, month: 1, day: 1 }

  const parsedDate = typeof date === 'string' ? new Date(date) : date

  return {
    year: parsedDate.getFullYear(),
    month: parsedDate.getMonth() + 1,
    day: parsedDate.getDate(),
  }
}

const isPensionAccrualExpanded = ref(true)
const pensionStore = usePensionStore()

const { hasCertifiedData, shouldShowCertified } = usePensionBase()

const participantPersonalInfo = computed(() => pensionStore.activeParticipant.personalInfo)

const pensionAccrualData = computed<PensionAccrualItem>(() => {
  // Helper function to get parameters for a given column year
  const getColumnParams = (year: number) => {
    if (!year || !normalizedParticipantCertifiedData.value || !normalizePensionParams.value)
      return { accrualPercentage: 0, annualMultiplier: 0, offsetAmount: 0, retirementAge: 65 }

    const normalizedCertifiedPensionParams = computed(() => normalizedParticipantCertifiedData.value[year]?.certifiedPensionParameters)

    const params = hasCertifiedData(year)
      ? normalizedCertifiedPensionParams.value
      : normalizePensionParams.value[year]

    return params || { accrualPercentage: 0, annualMultiplier: 0, offsetAmount: 0, retirementAge: 65 }
  }

  // Helper function to get salary info for a given column year
  const getSalaryInfo = (year: number) => {
    if (!year || !normalizedParticipantSalaryEntries.value)
      return { amount: 0, startDate: '', partTimePercentage: 1 }

    // TODO::Replace with correct normalizedCertifiedSalaryEntry

    // const salaryEntry = hasCertifiedData(year) ? normalizedParticipantSalaryEntries.value[year].certifiedEmploymentInfo?.certifiedSalaryEntry
    //   : normalizedParticipantSalaryEntries.value[year] ;

    const salaryEntry = hasCertifiedData(year)
      ? normalizedParticipantSalaryEntries.value[year]
      : normalizedParticipantSalaryEntries.value[year]

    return {
      amount: hasCertifiedData(year) && salaryEntry.certifiedSalaryEntry
        ? salaryEntry.certifiedSalaryEntry.amount || 0
        : salaryEntry.amount || 0,
      startDate: salaryEntry.startDate || '',
      partTimePercentage: hasCertifiedData(year) && salaryEntry.certifiedSalaryEntry
        ? salaryEntry.certifiedSalaryEntry.partTimePercentage || 1
        : salaryEntry.partTimePercentage || 1,
    }
  }

  // Get parameters for each column
  const rightColumnParams = getColumnParams(pensionStore.rightColumnYear as number)
  const middleColumnParams = getColumnParams(pensionStore.middleColumnYear as number)
  const leftColumnParams = getColumnParams(pensionStore.leftColumnYear as number)

  // Get salary info for each column
  const rightSalaryInfo = getSalaryInfo(pensionStore.rightColumnYear as number)
  const middleSalaryInfo = getSalaryInfo(pensionStore.middleColumnYear as number)
  const leftSalaryInfo = getSalaryInfo(pensionStore.leftColumnYear as number)

  // Calculate common values
  const participantDateOfBirth = computed(() => ({
    year: participantPersonalInfo.value?.birthYear || 0,
    month: participantPersonalInfo.value?.birthMonth || 0,
    day: participantPersonalInfo.value?.birthDay || 0,
  }))

  // LEFT COLUMN CALCULATIONS

  const leftGrossFulltimeMonthly = computed(() => {
    // return leftSalaryInfo.amount / leftSalaryInfo.partTimePercentage;
    return leftSalaryInfo.amount
  })

  const leftGrossFulltimeAnnual = computed(() => {
    return (leftColumnParams.annualMultiplier || 0) * leftGrossFulltimeMonthly.value
  })

  const leftPensionBase = computed(() => {
    return leftGrossFulltimeAnnual.value - (leftColumnParams.offsetAmount || 0)
  })

  const safeCalculate = (fn: Function, ...args: any[]) => {
    try {
      return fn(...args) || 0
    }
    catch (error) {
      console.error('Calculation error:', error)

      return 0
    }
  }

  const leftOpteAccrualToReferenceDate = safeCalculate(
    calculateOpTeAccrualToReferenceDate,
    leftPensionBase.value,
    leftColumnParams.accrualPercentage || 0,
    splitDate(leftSalaryInfo.startDate || ''),
    pensionStore.leftColumnYear as number || 0,
    leftSalaryInfo.partTimePercentage || 1,
    splitDate(pensionStore.leftColumnDate as Date || new Date()),
  )

  const leftWpteAccrualToReferenceDate = safeCalculate(
    calculateWpTeAccrualToReferenceDate,
    leftPensionBase.value,
    leftColumnParams.accrualPercentage || 0,
    leftColumnParams.partnersPensionPercentage || 0, // TODO::Change for real value
    splitDate(leftSalaryInfo.startDate || ''),
    pensionStore.leftColumnYear as number || 0,
    leftSalaryInfo.partTimePercentage || 1,
    splitDate(pensionStore.leftColumnDate as Date || new Date()),
  )

  const leftOpteAccrualAfterReferenceDate = safeCalculate(
    calculateOpTeAccrualAfterReferenceDate,
    participantDateOfBirth.value,
    leftColumnParams?.retirementAge || 65,
    splitDate(pensionStore.leftColumnDate as Date || new Date()),
    leftColumnParams.accrualPercentage || 0,
    leftSalaryInfo.partTimePercentage || 1,
    leftPensionBase.value,
  )

  // MIDDLE COLUMN CALCULATIONS
  const middleGrossFulltimeMonthly = middleSalaryInfo.partTimePercentage ? middleSalaryInfo.amount / middleSalaryInfo.partTimePercentage : 0
  const middleGrossFulltimeAnnual = (middleColumnParams.annualMultiplier || 0) * middleGrossFulltimeMonthly
  const middlePensionBase = middleGrossFulltimeAnnual - (middleColumnParams.offsetAmount || 0)

  const middleOpteAccrualToReferenceDate = safeCalculate(
    calculateOpTeAccrualToReferenceDate,
    middlePensionBase,
    middleColumnParams.accrualPercentage || 0,
    splitDate(middleSalaryInfo.startDate || ''),
    pensionStore.middleColumnYear as number || 0,
    middleSalaryInfo.partTimePercentage || 1,
    splitDate(pensionStore.middleColumnDate as Date || new Date()),
  )

  const middleWpteAccrualToReferenceDate = safeCalculate(
    calculateWpTeAccrualToReferenceDate,
    middlePensionBase,
    middleColumnParams.accrualPercentage || 0,
    middleColumnParams.partnersPensionPercentage || 0,
    splitDate(middleSalaryInfo.startDate || ''),
    pensionStore.middleColumnYear as number || 0,
    middleSalaryInfo.partTimePercentage || 1,
    splitDate(pensionStore.middleColumnDate as Date || new Date()),
  )

  const middleOpteAccrualAfterReferenceDate = safeCalculate(
    calculateOpTeAccrualAfterReferenceDate,
    participantDateOfBirth.value,
    middleColumnParams?.retirementAge || 65,
    splitDate(pensionStore.middleColumnDate as Date || new Date()),
    middleColumnParams.accrualPercentage || 0,
    middleSalaryInfo.partTimePercentage || 1,
    middlePensionBase,
  )

  // RIGHT COLUMN CALCULATIONS
  const rightGrossFulltimeMonthly = rightSalaryInfo.partTimePercentage ? rightSalaryInfo.amount / rightSalaryInfo.partTimePercentage : 0
  const rightGrossFulltimeAnnual = (rightColumnParams.annualMultiplier || 0) * rightGrossFulltimeMonthly
  const rightPensionBase = rightGrossFulltimeAnnual - (rightColumnParams.offsetAmount || 0)

  const rightOpteAccrualToReferenceDate = safeCalculate(
    calculateOpTeAccrualToReferenceDate,
    rightPensionBase,
    rightColumnParams.accrualPercentage || 0,
    splitDate(rightSalaryInfo.startDate || ''),
    pensionStore.rightColumnYear as number || 0,
    rightSalaryInfo.partTimePercentage || 1,
    splitDate(pensionStore.rightColumnDate as Date || new Date()),
  )

  const rightWpteAccrualToReferenceDate = safeCalculate(
    calculateWpTeAccrualToReferenceDate,
    rightPensionBase,
    rightColumnParams.accrualPercentage || 0,
    rightColumnParams.partnersPensionPercentage || 0,
    splitDate(rightSalaryInfo.startDate || ''),
    pensionStore.rightColumnYear as number || 0,
    rightSalaryInfo.partTimePercentage || 1,
    splitDate(pensionStore.rightColumnDate as Date || new Date()),
  )

  const rightOpteAccrualAfterReferenceDate = safeCalculate(
    calculateOpTeAccrualAfterReferenceDate,
    participantDateOfBirth.value,
    rightColumnParams?.retirementAge || 65,
    splitDate(pensionStore.rightColumnDate as Date || new Date()),
    rightColumnParams.accrualPercentage || 0,
    rightSalaryInfo.partTimePercentage || 1,
    rightPensionBase,
  )

  return {
    leftColumn: {
      year: pensionStore.leftColumnYear || 0,
      partPen: leftColumnParams.partnersPensionPercentage || 0,
      accrualPercentage: formatPercentage(leftColumnParams.accrualPercentage) || 0,
      opteAccrualToReferenceDate: formatCurrency(leftOpteAccrualToReferenceDate || 0),
      wpteAccrualToReferenceDate: formatCurrency(leftWpteAccrualToReferenceDate || 0),
      opteAccrualAfterReferenceDate: formatCurrency(leftOpteAccrualAfterReferenceDate || 0),
      pensionBase: formatCurrency(leftPensionBase.value || 0),
      leftGrossFullTimeMonthly: formatCurrency(leftGrossFulltimeMonthly.value || 0),
      leftGrossFulltimeAnnual: formatCurrency(leftGrossFulltimeAnnual.value || 0),
    },
    rightColumn: {
      year: pensionStore.rightColumnYear || 0,
      accrualPercentage: formatPercentage(rightColumnParams.accrualPercentage) || 0,
      opteAccrualToReferenceDate: formatCurrency(rightOpteAccrualToReferenceDate || 0),
      wpteAccrualToReferenceDate: formatCurrency(rightWpteAccrualToReferenceDate || 0),
      opteAccrualAfterReferenceDate: formatCurrency(rightOpteAccrualAfterReferenceDate || 0),
      pensionBase: formatCurrency(rightPensionBase || 0),
    },
    middleColumn: {
      year: pensionStore.middleColumnYear || 0,
      accrualPercentage: formatPercentage(middleColumnParams.accrualPercentage) || 0,
      opteAccrualToReferenceDate: formatCurrency(middleOpteAccrualToReferenceDate || 0),
      wpteAccrualToReferenceDate: formatCurrency(middleWpteAccrualToReferenceDate || 0),
      opteAccrualAfterReferenceDate: formatCurrency(middleOpteAccrualAfterReferenceDate || 0),
      pensionBase: formatCurrency(middlePensionBase || 0),
    },
  }
})
</script>

<template>
  <VCardText class="py-0">
    <VExpansionPanels
      v-model="isPensionAccrualExpanded"
      class="mb-6"
    >
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Pension accrual
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <!-- Table -->
          <SingleColumnTableLayout>
            <template #header-label>
              <div class="d-flex align-center justify-space-between">
                <span>DESCRIPTION</span>
                <div class="text-caption text-right">
                  {{ props.year }}
                </div>
              </div>
            </template>

            <SingleColumnTableRow
              label="Pension base"
              :is-alternate="true"
            >
              <template #right>
                {{ props.year === pensionAccrualData.leftColumn.year ? pensionAccrualData.leftColumn.pensionBase
                  : props.year === pensionAccrualData.middleColumn.year ? pensionAccrualData.middleColumn.pensionBase
                    : pensionAccrualData.rightColumn.pensionBase }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="Accrual percentage">
              <template #right>
                {{ props.year === pensionAccrualData.leftColumn.year ? pensionAccrualData.leftColumn.accrualPercentage
                  : props.year === pensionAccrualData.middleColumn.year ? pensionAccrualData.middleColumn.accrualPercentage
                    : pensionAccrualData.rightColumn.accrualPercentage }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow
              label="OP-TE accrual to reference date"
              :is-alternate="true"
            >
              <template #right>
                {{ props.year === pensionAccrualData.leftColumn.year ? pensionAccrualData.leftColumn.opteAccrualToReferenceDate
                  : props.year === pensionAccrualData.middleColumn.year ? pensionAccrualData.middleColumn.opteAccrualToReferenceDate
                    : pensionAccrualData.rightColumn.opteAccrualToReferenceDate || 'missing data' }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="WP-TE accrual to reference date">
              <template #right>
                {{ props.year === pensionAccrualData.leftColumn.year ? pensionAccrualData.leftColumn.wpteAccrualToReferenceDate
                  : props.year === pensionAccrualData.middleColumn.year ? pensionAccrualData.middleColumn.wpteAccrualToReferenceDate
                    : pensionAccrualData.rightColumn.wpteAccrualToReferenceDate || 'missing data' }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow
              label="OP-TE accrual after reference date"
              :is-alternate="true"
            >
              <template #right>
                <div :class="{ 'bg-yellow-lighten-4 text-orange-darken-2': props.year === pensionAccrualData.leftColumn.year }">
                  {{ props.year === pensionAccrualData.leftColumn.year ? pensionAccrualData.leftColumn.opteAccrualAfterReferenceDate
                    : props.year === pensionAccrualData.middleColumn.year ? pensionAccrualData.middleColumn.opteAccrualAfterReferenceDate
                      : pensionAccrualData.rightColumn.opteAccrualAfterReferenceDate || 'missing data' }}
                </div>
              </template>
            </SingleColumnTableRow>
          </SingleColumnTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
