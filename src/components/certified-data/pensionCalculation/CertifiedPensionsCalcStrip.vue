<script setup lang="ts">
import { computed } from 'vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useCertifiedData } from '@/composables/certified-data'

const props = defineProps({
  dateSelectionDisabled: {
    type: Boolean,
    default: false,
  },
})

const pensionStore = usePensionStore()

const leftColumnYear = computed(() => pensionStore.leftColumnYear)
const rightColumnYear = computed(() => pensionStore.rightColumnYear)
const middleColumnYear = computed(() => pensionStore.middleColumnYear)
const leftColumnDate = computed(() => pensionStore.leftColumnDate)
const rightColumnDate = computed(() => pensionStore.rightColumnDate)
const middleColumnDate = computed(() => pensionStore.middleColumnDate)

const { state: { participantCertifiedData } } = useCertifiedData()

const leftColumnDateOptions = computed(() => {
  if (!Array.isArray(participantCertifiedData.value))
    return []

  return [...participantCertifiedData.value]
    .sort((a: any, b: any) => b.certificationYear - a.certificationYear)
    .slice(1)
    .map((entry: any) => ({
      text: `Dec 31, ${entry.certificationYear}`,
      value: new Date(entry.certificationYear, 11, 30),
    }))
})

const changeLeftColumnDate = (value: Date | string) => {
  let year: number

  if (value instanceof Date) {
    year = value.getFullYear()
  }
  else if (typeof value === 'string') {
    // Extract year from string like "Dec 31, 2023"
    const match = value.match(/\d{4}/)

    year = match ? Number.parseInt(match[0]) : new Date().getFullYear()
  }
  else {
    console.error('Invalid date value:', value)

    return
  }

  pensionStore.setLeftColumnYear(year)
  pensionStore.setLeftColumnDate(`Dec 31, ${year}`)
}

const changeMiddleColumnDate = (value: Date | string) => {
  let year: number

  if (value instanceof Date) {
    year = value.getFullYear()
  }
  else if (typeof value === 'string') {
    const match = value.match(/\d{4}/)

    year = match ? Number.parseInt(match[0]) : new Date().getFullYear()
  }
  else {
    console.error('Invalid date value:', value)

    return
  }

  pensionStore.setMiddleColumnYear(year)
  pensionStore.setMiddleColumnDate(value)
}

const changeRightColumnDate = (value: Date | string) => {
  let year: number

  if (value instanceof Date) {
    year = value.getFullYear()
  }
  else if (typeof value === 'string') {
    const match = value.match(/\d{4}/)

    year = match ? Number.parseInt(match[0]) : new Date().getFullYear()
  }
  else {
    console.error('Invalid date value:', value)

    return
  }

  pensionStore.setRightColumnYear(year)
  pensionStore.setRightColumnDate(value)
}
</script>

<template>
  <VCardText class="py-0">
    <div class="pension-control-strip">
      <!-- Calculation Year Row -->
      <div class="pension-control-row">
        <div class="pension-control-label">
          <span class="text-subtitle-2 font-weight-medium">Calculation year</span>
        </div>
        <div class="pension-control-fields">
          <div class="pension-control-field">
            <VTextField
              :model-value="leftColumnYear"
              readonly
              variant="filled"
              density="compact"
              hide-details
            />
          </div>
          <div class="pension-control-field">
            <VTextField
              :model-value="middleColumnYear"
              readonly
              variant="filled"
              density="compact"
              hide-details
            />
          </div>
          <div class="pension-control-field">
            <VTextField
              :model-value="rightColumnYear"
              readonly
              variant="filled"
              density="compact"
              hide-details
            />
          </div>
        </div>
      </div>

      <!-- Reference Date Row -->
      <div class="pension-control-row mb-6">
        <div class="pension-control-label">
          <span class="text-subtitle-2 font-weight-medium">Reference date</span>
        </div>
        <div class="pension-control-fields">
          <div class="pension-control-field">
            <VSelect
              v-model="leftColumnDate"
              :items="leftColumnDateOptions"
              item-title="text"
              item-value="value"
              variant="outlined"
              density="compact"
              hide-details
              @update:model-value="(value) => changeLeftColumnDate(value)"
            />
          </div>
          <div class="pension-control-field">
            <AppDateTimePicker
              v-model="middleColumnDate"
              :config="{ dateFormat: 'F j, Y' }"
              :readonly="props.dateSelectionDisabled"
              @update:model-value="(value) => changeMiddleColumnDate(value)"
            >
              <template #append>
                <VIcon>tabler-calendar-event</VIcon>
              </template>
            </AppDateTimePicker>
          </div>
          <div class="pension-control-field">
            <AppDateTimePicker
              v-model="rightColumnDate"
              :config="{ dateFormat: 'F j, Y' }"
              :readonly="props.dateSelectionDisabled"
              @update:model-value="(value) => changeRightColumnDate(value)"
            >
              <template #append>
                <VIcon>tabler-calendar-event</VIcon>
              </template>
            </AppDateTimePicker>
          </div>
        </div>
      </div>
    </div>
  </VCardText>
</template>

<style scoped>
.pension-control-strip {
  width: 100%;
}

.pension-control-row {
  display: flex;
  width: 100%;
  margin-bottom: 8px;
}

.pension-control-label {
  width: 30%;
  display: flex;
  align-items: center;
}

.pension-control-fields {
  width: 70%;
  display: flex;
}

.pension-control-field {
  flex: 1;
  padding: 0 4px;
}

.pension-control-field:first-child {
  padding-left: 0;
}

.pension-control-field:last-child {
  padding-right: 0;
}
</style>
