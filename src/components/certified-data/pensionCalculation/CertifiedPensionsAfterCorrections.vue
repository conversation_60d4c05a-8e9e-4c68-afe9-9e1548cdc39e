<script setup lang="ts">
import { computed, ref } from 'vue'
import SingleColumnTableLayout from './SingleColumnTableLayout.vue'
import SingleColumnTableRow from './SingleColumnTableRow.vue'
import EditCorrectionsDialog from '@/components/EditCorrectionsDialog.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'
import { useCertifiedData } from '@/composables/certified-data'
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import { useParticipants } from '@/composables/participants/useParticipants'

// Define props to accept year as a parameter
const props = defineProps({
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const isAfterCorrectionsExpanded = ref(true)
const pensionStore = usePensionStore()
const { shouldShowCertified, hasCertifiedData } = usePensionBase()
const { state: { normalizedParticipantCertifiedData } } = useCertifiedData()
const { actions: { refetchSingleParticipant } } = useParticipants()

const { state: { participantChangeProposalsList } } = useChangeProposal()

const currentParticipantData = computed(() => pensionStore.activeParticipant)

const pensionChangesWithCorrections = computed(() => {
  if (!participantChangeProposalsList.value?.length)
    return {}

  return participantChangeProposalsList.value
    .filter(change => change.rawData.proposal.entityType === 'PensionInfo')
    .reduce((acc, change) => {
      const field = change.field
      const newValue = Number.parseFloat(change.newValue)
      const currentValue = Number.parseFloat(change.currentValue)

      acc[field] = {
        newValue,
        currentValue,
        correction: newValue - currentValue,
      }

      return acc
    }, {} as Record<string, { newValue: number; currentValue: number; correction: number }>)
})

const getColumnData = (year: number | null) => {
  if (!year)
    return null

  // TODO::Update to use certified data if available
  // if(hasCertifiedData(year)){
  //   return normalizedParticipantCertifiedData.value?.[year]?.certifiedPensionCorrections;
  // }else{
  return currentParticipantData.value?.pensionInfo

  // }
}

const getStatusClass = (year: number | null) => {
  if (!year)
    return null
  if (pensionStore.certifiedDataYears?.includes(year))
    return 'bg-green-lighten-5 text-green-darken-2'

  return 'bg-orange-lighten-5 text-orange-darken-2'
}

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined)
    return '-'

  return new Intl.NumberFormat('nl-AW', {
    style: 'currency',
    currency: 'AWG',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

const editDialog = ref(false)

const selectedField = ref({
  field: '',
  value: 0,
  year: 0,
  entityId: '',
})

const openEditDialog = (field: string, value: number, year: number, entityId: string) => {
  // Don't do anything if editing is disabled
  if (!props.editable)
    return

  selectedField.value = {
    field,
    value,
    year,
    entityId,
  }
  editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
}

const updateField = (newValue: number) => {
  // TODO: Implement the actual update logic here, likely involving an API call
  console.log('Updating', selectedField.value.field, 'for year', selectedField.value.year, 'with', newValue)
  closeEditDialog()
}

const handleRefresh = async () => {
  await refetchSingleParticipant()
}

const hasPendingChanges = (pendingChanges: string[], field: string) => {
  if (!pendingChanges)
    return false

  return pendingChanges.includes(field)
}
</script>

<template>
  <VCardText class="py-0">
    <VExpansionPanels
      v-model="isAfterCorrectionsExpanded"
      class="mb-6"
    >
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Pensions after corrections (start of calendar year before indexation)
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <SingleColumnTableLayout>
            <template #header-label>
              <div class="d-flex align-center justify-space-between">
                <span>DESCRIPTION</span>
                <div class="text-caption text-right">
                  {{ props.year }}
                </div>
              </div>
            </template>

            <SingleColumnTableRow
              label="OP-TE"
              :is-alternate="true"
            >
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.accruedGrossAnnualOldAgePension) }}
                <VChip
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'accruedGrossAnnualOldAgePension')"
                  color="primary"
                  size="small"
                >
                  {{ formatCurrency(pensionChangesWithCorrections.accruedGrossAnnualOldAgePension?.correction) }}
                </VChip>
                <span v-if="props.editable">
                  <VBtn
                    v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'accruedGrossAnnualOldAgePension')"
                    icon
                    size="small"
                    variant="text"
                    color="primary"
                  >
                    <VIcon
                      size="16"
                      icon="tabler-alert-triangle"
                      class="edit-icon"
                      color="error"
                    />
                  </VBtn>
                  <VBtn
                    v-else
                    icon
                    size="small"
                    variant="text"
                    color="primary"
                    @click="openEditDialog('accruedGrossAnnualOldAgePension', getColumnData(props.year)?.accruedGrossAnnualOldAgePension, props.year, getColumnData(props.year)?.id)"
                  >
                    <VIcon
                      size="16"
                      icon="tabler-edit"
                      class="edit-icon"
                      color="primary"
                    />
                  </VBtn>
                </span>
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="OP-TB">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.attainableGrossAnnualOldAgePension) }}
                <VChip
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'attainableGrossAnnualOldAgePension')"
                  color="primary"
                  size="small"
                >
                  {{ formatCurrency(pensionChangesWithCorrections.attainableGrossAnnualOldAgePension?.correction) }}
                </VChip>
                <VBtn
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'attainableGrossAnnualOldAgePension')"
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                >
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else-if="props.editable"
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('attainableGrossAnnualOldAgePension', getColumnData(props.year)?.attainableGrossAnnualOldAgePension, props.year, getColumnData(props.year)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow
              label="WP-TE"
              :is-alternate="true"
            >
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.accruedGrossAnnualPartnersPension) }}
                <VChip
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'accruedGrossAnnualPartnersPension')"
                  color="primary"
                  size="small"
                >
                  {{ formatCurrency(pensionChangesWithCorrections.accruedGrossAnnualPartnersPension?.correction) }}
                </VChip>
                <VBtn
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'accruedGrossAnnualPartnersPension')"
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                >
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else-if="props.editable"
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('accruedGrossAnnualPartnersPension', getColumnData(props.year)?.accruedGrossAnnualPartnersPension, props.year, getColumnData(props.year)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="ONP-TE">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.accruedGrossAnnualSinglesPension) }}
                <VChip
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'accruedGrossAnnualSinglesPension')"
                  color="primary"
                  size="small"
                >
                  {{ formatCurrency(pensionChangesWithCorrections.accruedGrossAnnualSinglesPension?.correction) }}
                </VChip>
                <VBtn
                  v-if="hasPendingChanges(getColumnData(props.year)?.pendingChanges, 'accruedGrossAnnualSinglesPension')"
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                >
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else-if="props.editable"
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('accruedGrossAnnualSinglesPension', getColumnData(props.year)?.accruedGrossAnnualSinglesPension, props.year, getColumnData(props.year)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
            </SingleColumnTableRow>
          </SingleColumnTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>

    <EditCorrectionsDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.field"
      :current-value="selectedField.value.toString()"
      :entity-id="selectedField?.entityId"
      entity-type="PensionInfo"
      type="PARTICIPANT"
      :year="selectedField.year"
      @close="closeEditDialog"
      @update="updateField"
      @refresh="handleRefresh"
    />
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title {
    min-height: 48px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
