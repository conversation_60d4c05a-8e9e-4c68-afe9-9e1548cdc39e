<script>
export default {
  props: {
    year: {
      type: Number,
      default: 2024,
    },
    editable: {
      type: Boolean,
      default: true,
    },
  },
}
</script>

<template>
  <VCard class="mt-6">
    <VRow no-gutters>
      <VCol cols="12">
        <VCardText>
          <VWindow class="disable-tab-transition">
            <VWindowItem>
              <CertifiedPensionsEndOfPreviousCalenderYear
                :year="year"
                :editable="editable"
              />
              <CertifiedPensionsAfterCorrections
                :year="year"
                :editable="editable"
              />
              <CertifiedIndexationBegining
                :year="year"
                :editable="editable"
              />
              <CertifiedAccrualPeriodCalculation
                :year="year"
                :editable="editable"
              />
              <CertifiedPensionsAccrual
                :year="year"
                :editable="editable"
              />
              <CertifiedPensionsAsPerReferenceDate
                :year="year"
                :editable="editable"
              />
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>

<style scoped>
  .stepper-icon-step-bg {
    margin-bottom: 16px;
  }
</style>
