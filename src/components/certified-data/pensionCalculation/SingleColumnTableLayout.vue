<template>
  <VTable class="pension-table">
    <thead>
      <tr>
        <th
          class="text-left"
          style="width: 50%;"
        >
          <slot name="header-label">
            DESCRIPTION
          </slot>
        </th>
        <th
          class="text-right"
          style="width: 50%;"
        >
          <span>VALUE</span>
        </th>
      </tr>
    </thead>
    <tbody>
      <slot />
    </tbody>
  </VTable>
</template>

<style scoped>
.pension-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  table-layout: fixed;
  width: 100%;
}

.pension-table th {
  font-weight: 500;
  background-color: #f5f5f5;
}

/* Add padding to the right-aligned cells for better readability */
.pension-table th.text-right,
.pension-table td.text-right {
  padding-right: 16px;
}
</style>
