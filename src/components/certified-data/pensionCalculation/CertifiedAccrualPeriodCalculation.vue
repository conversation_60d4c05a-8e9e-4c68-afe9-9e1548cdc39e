<script setup lang="ts">
import { computed, ref } from 'vue'
import SingleColumnTableLayout from './SingleColumnTableLayout.vue'
import SingleColumnTableRow from './SingleColumnTableRow.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
import { useParticipants } from '@/composables/participants/useParticipants'
import type { SplitDate } from '@/types/participant.types'
import { usePensionCalculations } from '@/composables/pension-calculations/usePensionCalculations'
import { safeCalculate } from '@/utils/transformers'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'

// Define props to accept year as a parameter
const props = defineProps({
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const isAccrualExpanded = ref(0)
const pensionStore = usePensionStore()

const { state: { normalizePensionParams } } = usePensionParameters()
const { state: { normalizedParticipantSalaryEntries } } = useParticipants()

const { actions: { calculateAccrualPeriodToReferenceDate, calculateAccrualPeriodAfterReferenceDate } } = usePensionCalculations()

const normalizedParticipantCertifiedData = computed(() => {
  return pensionStore.normalizedCertifiedData
})

const { hasCertifiedData, shouldShowCertified } = usePensionBase()

const participantPersonalInfo = computed(() => pensionStore.activeParticipant.personalInfo)

// Helper function to split a date into year, month, day components
const splitDate = (date: Date | string): SplitDate => {
  if (!date)
    return { year: 2000, month: 1, day: 1 }

  const parsedDate = typeof date === 'string' ? new Date(date) : date

  return {
    year: parsedDate.getFullYear(),
    month: parsedDate.getMonth() + 1,
    day: parsedDate.getDate(),
  }
}

const accrualPeriodData = computed(() => {
  // Helper function to get participant info for a given year
  const getParticipantInfo = (year: number) => {
    // Use a dummy date for now - in a real implementation, this would be based on the year
    const columnDate = new Date(year, 11, 31)

    if (!year || !normalizedParticipantSalaryEntries.value || !normalizePensionParams.value) {
      return {
        startDate: 'Dec 31, 2019',
        endDate: 'May 11, 2025',
        birthDate: 'Jul 31, 1982',
        aovAge: 65,
        retirementDate: 'Jul 31, 2047',
        periodDuringYear: 1.00,
        periodAfterReferenceDate: 22.60,
      }
    }

    // TODO::Update to use certified data if available
    const employmentInfo = computed(() =>
      hasCertifiedData(year)
        ? pensionStore.activeParticipant.employmentInfo
        : pensionStore.activeParticipant.employmentInfo,
    )

    const byear = participantPersonalInfo.value.birthYear
    const bmonth = participantPersonalInfo.value.birthMonth
    const bday = participantPersonalInfo.value.birthDay

    return {
      startDate: formatDate(employmentInfo.value?.startDate),
      endDate: employmentInfo.value?.endDate || '-',
      birthDate: formatBirthDate(participantPersonalInfo.value),
      aovAge: getAovAge(year) || 65,
      retirementDate: calculateRetirementDate(participantPersonalInfo.value, getAovAge(year)),
      periodDuringYear: calculatePeriodDuringYear(year, splitDate(employmentInfo.value?.startDate || ''), splitDate(columnDate)),
      periodAfterReferenceDate: calculatePeriodAfterReferenceDate({ year: byear, month: bmonth, day: bday }, getAovAge(year), splitDate(columnDate)),
    }
  }

  // Get parameters for the specified year
  const yearInfo = getParticipantInfo(props.year)

  return {
    year: props.year,
    startDate: yearInfo.startDate,
    endDate: yearInfo.endDate || '-',
    birthDate: yearInfo.birthDate,
    aovAge: yearInfo.aovAge,
    retirementDate: yearInfo.retirementDate,
    periodDuringYear: yearInfo.periodDuringYear,
    periodAfterReferenceDate: yearInfo.periodAfterReferenceDate,
  }
})

// Utility functions
const formatBirthDate = (personalInfo: any) => {
  if (!personalInfo)
    return 'Jul 31, 1982'

  const month = personalInfo.birthMonth || 7
  const monthName = getMonthName(month)
  const day = personalInfo.birthDay || 31
  const year = personalInfo.birthYear || 1982

  return `${monthName} ${day}, ${year}`
}

const formatDate = (date: string) => {
  if (!date)
    return '-'

  const parsedDate = new Date(date)
  const day = parsedDate.getDate()
  const month = getMonthName(parsedDate.getMonth() + 1)
  const year = parsedDate.getFullYear()

  return `${month} ${day}, ${year}`
}

const getMonthName = (month: number) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

  return months[(month - 1) % 12]
}

const getAovAge = (year: number) => {
  if (normalizePensionParams.value && normalizePensionParams.value[year])
    return normalizePensionParams.value[year].retirementAge || 65

  return 65
}

const calculateRetirementDate = (personalInfo: any, aovAge: number) => {
  if (!personalInfo)
    return 'Jul 31, 2047'

  const birthYear = personalInfo.birthYear
  const birthMonth = personalInfo.birthMonth
  const birthDay = personalInfo.birthDay

  const retirementYear = birthYear + aovAge
  const monthName = 'Dec'
  const retirementDay = 1

  return `${monthName} ${retirementDay}, ${retirementYear}`
}

const calculatePeriodDuringYear = (year: number, startDate: SplitDate, columnDate: SplitDate) => {
  return safeCalculate(
    calculateAccrualPeriodToReferenceDate,
    startDate,
    year,
    columnDate,
  )
}

// const leftOpteAccrualAfterReferenceDate = safeCalculate(
//   calculateAccrualPeriodAfterReferenceDate,
//   participantDateOfBirth.value,
//   leftColumnParams?.retirementAge || 65,
//   splitDate(pensionStore.leftColumnDate as Date || new Date()),
// );

const calculatePeriodAfterReferenceDate = (dateOfBirth: SplitDate, retirementAge: number, columnDate: SplitDate) => {
  // This would calculate the period from reference date to retirement
  return safeCalculate(
    calculateAccrualPeriodAfterReferenceDate,
    dateOfBirth,
    retirementAge,
    columnDate,
  )
}

const getStatusClass = (year: number) => {
  if (hasCertifiedData(year))
    return 'bg-green-lighten-5 text-green-darken-2'

  return 'bg-orange-lighten-5 text-orange-darken-2'
}

const getStatusText = (year: number) => {
  return hasCertifiedData(year) ? 'CERTIFIED' : 'CERTIFICATION NOT STARTED'
}
</script>

<template>
  <VCard
    class="pension-calculation-container"
    elevation="0"
  >
    <VCardText>
      <!-- Accrual Period Section -->
      <VExpansionPanels v-model="isAccrualExpanded">
        <VExpansionPanel elevation="2">
          <VExpansionPanelTitle class="py-3">
            <template #default="{ expanded }">
              <VRow no-gutters>
                <VCol
                  cols="8"
                  class="d-flex align-center"
                >
                  <h4 class="text-subtitle-1 font-weight-bold">
                    Accrual period calculation
                  </h4>
                </VCol>
                <VCol
                  cols="4"
                  class="text-right"
                >
                  <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
                </VCol>
              </VRow>
            </template>
          </VExpansionPanelTitle>
          <VExpansionPanelText>
            <!-- Table -->
            <SingleColumnTableLayout>
              <template #header-label>
                <div class="d-flex align-center justify-space-between">
                  <span>DESCRIPTION</span>
                  <div class="text-caption text-right">
                    {{ props.year }}
                  </div>
                </div>
              </template>

              <SingleColumnTableRow
                label="Start date of participation"
                :is-alternate="true"
              >
                <template #right>
                  {{ accrualPeriodData.startDate }}
                </template>
              </SingleColumnTableRow>

              <SingleColumnTableRow label="End date of employment">
                <template #right>
                  {{ accrualPeriodData.endDate }}
                </template>
              </SingleColumnTableRow>

              <SingleColumnTableRow
                label="Birth date"
                :is-alternate="true"
              >
                <template #right>
                  {{ accrualPeriodData.birthDate }}
                </template>
              </SingleColumnTableRow>

              <SingleColumnTableRow label="AOV age">
                <template #right>
                  {{ accrualPeriodData.aovAge }}
                </template>
              </SingleColumnTableRow>

              <SingleColumnTableRow
                label="Retirement date"
                :is-alternate="true"
              >
                <template #right>
                  {{ accrualPeriodData.retirementDate }}
                </template>
              </SingleColumnTableRow>

              <SingleColumnTableRow label="Period during calculation year">
                <template #right>
                  {{ accrualPeriodData.periodDuringYear.toFixed(2) }}
                </template>
              </SingleColumnTableRow>

              <SingleColumnTableRow
                label="Period after reference date"
                :is-alternate="true"
              >
                <template #right>
                  {{ accrualPeriodData.periodAfterReferenceDate.toFixed(2) }}
                </template>
              </SingleColumnTableRow>
            </SingleColumnTableLayout>
          </VExpansionPanelText>
        </VExpansionPanel>
      </VExpansionPanels>
    </VCardText>
  </VCard>
</template>

<style scoped>
  .pension-calculation-container {
    border-radius: 8px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
