<script setup lang="ts">
import { ref } from 'vue'
import SingleColumnTableLayout from './SingleColumnTableLayout.vue'
import SingleColumnTableRow from './SingleColumnTableRow.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'
import { useCertifiedData } from '@/composables/certified-data'

// Define props to accept year as a parameter
const props = defineProps({
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const isIndexationExpanded = ref(true)
const pensionStore = usePensionStore()
const { shouldShowCertified } = usePensionBase()
const { state: { normalizedParticipantCertifiedData } } = useCertifiedData()

const getColumnData = (year: number) => {
  return normalizedParticipantCertifiedData.value?.[year]
}

const getStatusClass = (year: number) => {
  if (pensionStore.certifiedDataYears?.includes(year))
    return 'bg-green-lighten-5 text-green-darken-2'

  return 'bg-orange-lighten-5 text-orange-darken-2'
}

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined)
    return 'N/A'

  return new Intl.NumberFormat('nl-AW', {
    style: 'currency',
    currency: 'AWG',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}
</script>

<template>
  <VCardText class="py-0">
    <VExpansionPanels v-model="isIndexationExpanded">
      <VExpansionPanel elevation="2">
        <VExpansionPanelTitle class="py-3">
          <template #default="{ expanded }">
            <VRow no-gutters>
              <VCol
                cols="8"
                class="d-flex align-center"
              >
                <h4 class="text-subtitle-1 font-weight-bold">
                  Indexation beginning of calendar year
                </h4>
              </VCol>
              <VCol
                cols="4"
                class="text-right"
              >
                <VIcon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'" />
              </VCol>
            </VRow>
          </template>
        </VExpansionPanelTitle>
        <VExpansionPanelText>
          <SingleColumnTableLayout>
            <template #header-label>
              <div class="d-flex align-center justify-space-between">
                <span>DESCRIPTION</span>
                <div class="text-caption text-right">
                  {{ props.year }}
                </div>
              </div>
            </template>

            <SingleColumnTableRow
              label="OP-TE"
              :is-alternate="true"
            >
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedIndexationStartOfYear?.accruedGrossAnnualOldAgePension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="WP-TE">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedIndexationStartOfYear?.accruedGrossAnnualPartnersPension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow
              label="ONP-TE"
              :is-alternate="true"
            >
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedIndexationStartOfYear?.extraAccruedGrossAnnualOldAgePension) }}
              </template>
            </SingleColumnTableRow>

            <SingleColumnTableRow label="AOP-TE">
              <template #right>
                {{ formatCurrency(getColumnData(props.year)?.certifiedIndexationStartOfYear?.accruedGrossAnnualSinglesPension) }}
              </template>
            </SingleColumnTableRow>
          </SingleColumnTableLayout>
        </VExpansionPanelText>
      </VExpansionPanel>
    </VExpansionPanels>
  </VCardText>
</template>

<style scoped>
  .v-expansion-panel-title {
    min-height: 48px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>
