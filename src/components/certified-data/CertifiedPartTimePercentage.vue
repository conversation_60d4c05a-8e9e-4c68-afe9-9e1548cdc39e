<script setup lang="ts">
import { computed } from 'vue'
import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'

interface SalaryEntryDifference {
  year: number
  fields: string[]
}

const props = defineProps<{
  year?: number
  editable?: boolean
}>()

const {
  state: { certifiedDataByYearAndYearBeforeData },
  actions: { handleApproveCertifiedField, handleRejectCertifiedField },
} = useCertifiedDataByYearAndYearBefore()

const certifiedData = computed(() => {
  if (!certifiedDataByYearAndYearBeforeData.value || !props.year)
    return null

  return certifiedDataByYearAndYearBeforeData.value[props.year]?.[0] || null
})

const salaryEntries = computed(() => {
  return certifiedData.value?.certifiedEmploymentInfo?.certifiedSalaryEntries || []
})

const salaryEntriesDifferences = computed(() => {
  return certifiedData.value?.certifiedEmploymentInfo?.certifiedSalaryEntriesDifferences || []
})

const handleApprove = async (entryId: string, field: string) => {
  if (!certifiedData.value)
    return
  await handleApproveCertifiedField(
    certifiedData.value.id,
    field,
    'certifiedSalaryEntry',
    entryId,
  )
}

const handleReject = async (entryId: string, field: string, reason: string) => {
  if (!certifiedData.value)
    return
  await handleRejectCertifiedField(
    certifiedData.value.id,
    field,
    'certifiedSalaryEntry',
    reason,
    entryId,
  )
}

const isFieldDifferent = (entryIndex: number, field: string) => {
  return salaryEntriesDifferences.value.some((diff: SalaryEntryDifference) => diff.year === entryIndex && diff.fields.includes(field))
}

const getFieldStatus = (entry: any, field: string) => {
  if (entry.approvedChanges?.includes(field))
    return 'approved'
  if (entry.requestedChanges?.includes(field))
    return 'pending'
  if (isFieldDifferent(entry.year, field))
    return 'different'

  return 'unchanged'
}

const formatPercentage = (value: number) => {
  return `${(value * 100).toFixed(0)}%`
}
</script>

<template>
  <VCard class="mb-4">
    <VCardTitle class="d-flex align-center justify-space-between">
      <span>Part-Time Percentage</span>
    </VCardTitle>
    <VCardText>
      <VTable>
        <thead>
          <tr>
            <th>Year</th>
            <th>Percentage</th>
            <th v-if="editable">
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="entry in salaryEntries"
            :key="entry.id"
          >
            <td>{{ entry.year }}</td>
            <td>
              <!--              <CertifiedField -->
              <!--                :value="formatPercentage(entry.partTimePercentage)" -->
              <!--                :status="getFieldStatus(entry, 'partTimePercentage')" -->
              <!--                :editable="editable" -->
              <!--                @approve="handleApprove(entry.id, 'partTimePercentage')" -->
              <!--                @reject="handleReject(entry.id, 'partTimePercentage', $event)" -->
              <!--              /> -->
            </td>
            <td v-if="editable">
              <div class="d-flex gap-2">
                <VBtn
                  v-if="getFieldStatus(entry, 'partTimePercentage') === 'different'"
                  size="small"
                  color="success"
                  variant="text"
                  @click="handleApprove(entry.id, 'partTimePercentage')"
                >
                  Approve
                </VBtn>
                <VBtn
                  v-if="getFieldStatus(entry, 'partTimePercentage') === 'different'"
                  size="small"
                  color="error"
                  variant="text"
                  @click="$emit('reject', entry.id, 'partTimePercentage')"
                >
                  Reject
                </VBtn>
              </div>
            </td>
          </tr>
        </tbody>
      </VTable>
    </VCardText>
  </VCard>
</template>
