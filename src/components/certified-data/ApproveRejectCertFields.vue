<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAppStore } from '@/stores/app/appStore'
import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  field: {
    type: Object as () => FieldItem | null,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
  participantName: {
    type: String,
    required: true,
  },
  entityType: {
    type: String,
    required: true,
  },
  certifiedDataId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'close', 'approved', 'rejected'])

const { canApproveChanges, canRejectChanges } = useRoleAccess()

interface FieldItem {
  __typename?: string
  typename?: string
  id: string
  name: string
  field: string
  value: any
  disabled: boolean
  isDifferent?: boolean
  changeRequested?: boolean
  approvedChanges?: boolean
}

const appStore = useAppStore()

const { actions: { handleApproveCertifiedField, handleRejectCertifiedField } } = useCertifiedDataByYearAndYearBefore()

const dialog = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

const reasons = ref('')

const validationError = ref(false)

const closeDialog = () => {
  dialog.value = false
  emit('close')
}

const approveChanges = async () => {
  try {
    if (props.field) {
      const result = await handleApproveCertifiedField(
        props.certifiedDataId,
        props.field.field,
        props.entityType,
        props.field.id,
      )

      appStore.showSnack('Field approved successfully')
      emit('approved', { field: props.field.field, result })
    }
  }
  catch (error) {
    console.error('Error approving field:', error)
    appStore.showSnack('Error approving field')
  }
  finally {
    closeDialog()
  }
}

const rejectChanges = async () => {
  if (!reasons.value.trim()) {
    validationError.value = true

    return
  }
  try {
    if (props.field) {
      const result = await handleRejectCertifiedField(
        props.certifiedDataId,
        props.field.field,
        props.field.typename || props.field.__typename,
        reasons.value.trim(),
        props.field.id,
      )

      appStore.showSnack('Changes rejected successfully')
      emit('rejected', { field: props.field.field, result })
    }
  }
  catch (error) {
    console.error('Error rejecting changes:', error)
    appStore.showSnack('Error rejecting changes')
  }
  finally {
    closeDialog()
  }
}

// if dialog model value changes, reset validation
watch(() => props.modelValue, () => {
  validationError.value = false
  reasons.value = ''
})
</script>

<template>
  <VDialog
    v-model="dialog"
    max-width="500"
  >
    <VCard v-if="field">
      <VCardTitle class="text-h5">
        Approve Changes
      </VCardTitle>
      <VCardText>
        <div class="mb-4">
          Would you like to approve or reject the changes for <strong>{{ field.name }}</strong>?
        </div>
        <div class="d-flex flex-column gap-2">
          <div>
            <span class="text-subtitle-2 font-weight-medium">Field:</span>
            <span class="ml-2">{{ field.name }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Value:</span>
            <span class="ml-2">{{ field.value }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Participant:</span>
            <span class="ml-2">{{ participantName }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Year:</span>
            <span class="ml-2">{{ year }}</span>
          </div>

          <div class="mb-1">
            Reason for rejection <span class="text-error">*</span>
          </div>
          <VTextarea
            v-model="reasons"
            placeholder="Please provide a detailed explanation for rejecting this change..."
            :error="validationError"
            :error-messages="validationError ? 'Please provide a reason for rejection' : ''"
            variant="outlined"
            rows="7"
            auto-grow
            class="mt-1"
          />
        </div>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="error"
          @click="rejectChanges"
        >
          Reject
        </VBtn>
        <VBtn
          color="primary"
          @click="approveChanges"
        >
          Approve
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
