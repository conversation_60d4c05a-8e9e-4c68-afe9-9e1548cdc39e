<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'
import { useCertifiedData } from '@/composables/certified-data'
import { useAppStore } from '@/stores/app/appStore'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const router = useRouter()

const route = useRoute()
const appStore = useAppStore()
const { getCertificationStatus } = usePensionBase()
const { actions: { updateCertificationStatus }, state: { loadingUpdateCertificationStatus } } = useCertifiedData()
const { canManageCertifications } = useRoleAccess()

const certificationYear = computed(() => {
  return route.params.year ? Number.parseInt(route.params.year as string) : new Date().getFullYear() - 1
})

const certificationStatus = computed(() => {
  return getCertificationStatus(certificationYear.value)
})

const isEditable = computed(() => {
  return route.path.includes('/started') && certificationStatus.value === 'started'
})

const confirmDialog = ref(false)
const isSwitchingToManaged = ref<boolean | null>(null)

const handleShowDialog = () => {
  confirmDialog.value = true
}

const onSetCertificationStatus = async (status: string) => {
  try {
    // Get the certification ID from the route params
    const certificationId = route.params.cid as string

    if (!certificationId) {
      appStore.showSnack('Certification ID not found')

      return
    }

    // Call the mutation to update the certification status
    await updateCertificationStatus(certificationId, status)

    // Show success message
    appStore.showSnack(`Certification status updated to ${status}`)

    // Navigate to the updated certification page
    router.push(`/certification-management/${route.params.id}/certification/${route.params.cid}/${certificationYear.value}/${status}`)

    // Close the dialog
    confirmDialog.value = false
  }
  catch (error) {
    console.error('Error updating certification status:', error)
    appStore.showSnack('Failed to update certification status')
  }
}

// const goToCertification = () => {
//   router.push(`/certification-management/${route.params.id}/certification/${route.params.cid}/${certificationYear.value}/${certificationStatus.value}`)
// }

const onCloseDialog = () => {
  confirmDialog.value = false
  isSwitchingToManaged.value = null
}

const nextStatus = computed(() => {
  return certificationStatus.value === 'pending' ? 'started' : 'completed'
})

// Get alert properties based on certification status
const alertProps = computed(() => {
  switch (certificationStatus.value) {
    case 'completed':
      return {
        title: `Certification for ${certificationYear.value} has been completed.`,
        type: 'success',
        message: 'The certification process for this year has been completed.',
        showAction: false,
    }
    case 'started':
      return {
        title: `Certification for ${certificationYear.value} is in progress.`,
        type: 'warning',
        message: 'Would you like to complete the certification process?',
        showAction: isEditable.value,
        actionText: 'Complete Now',
        actionStatus: 'completed',
    }
    case 'pending':
    default:
      return {
        title: `Data for ${certificationYear.value} is not ready for Certification.`,
        type: 'info',
        message: 'Would you like to set as ready certification?',
        showAction: true,
        actionText: 'Ready for certification',
        actionStatus: 'started',
    }
  }
})
</script>

<template>
  <div>
    <VAlert
      :title="alertProps.title"
      :type="alertProps.type"
      variant="tonal"
    >
      <VRow
        align="center"
        no-gutters
      >
        <VCol
          cols="8"
          sm="9"
          md="10"
        >
          <div class="d-flex align-center">
            <VChip
              size="small"
              :color="alertProps.type"
              class="status-chip mr-2"
            >
              {{ certificationStatus }}
            </VChip>
            <div class="text-caption">
              {{ alertProps.message }}
            </div>
          </div>
        </VCol>

        <VCol
          v-if="alertProps.showAction && canManageCertifications"
          cols="4"
          sm="3"
          md="2"
          class="text-right"
        >
          <VBtn
            size="small"
            :color="alertProps.type === 'info' ? 'success' : 'warning'"
            variant="flat"
            class="ml-2"
            @click="handleShowDialog"
          >
            {{ alertProps.actionText }}
            <VIcon
              end
              icon="mdi-arrow-right"
            />
          </VBtn>
        </VCol>
      </VRow>
    </VAlert>

    <VDialog
      v-model="confirmDialog"
      persistent
      width="auto"
    >
      <VCard>
        <VCardTitle>
          <span class="text-h5">Confirmation</span>
        </VCardTitle>
        <VCardText>
          Are you sure you want to {{ nextStatus === 'started' ? 'start' : 'complete' }} certification for {{ certificationYear }}?
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            color="grey-darken-1"
            variant="text"
            @click="onCloseDialog"
          >
            Cancel
          </VBtn>
          <VBtn
            :color="nextStatus === 'started' ? 'primary' : 'success'"
            :loading="loadingUpdateCertificationStatus"
            :disabled="loadingUpdateCertificationStatus"
            @click="onSetCertificationStatus(nextStatus)"
          >
            {{ nextStatus === 'started' ? 'Start' : 'Complete' }} Certification
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<style scoped lang="scss">
  .v-alert {
    margin-bottom: 16px;
  }

  .status-chip {
    text-transform: capitalize;
  }
</style>
