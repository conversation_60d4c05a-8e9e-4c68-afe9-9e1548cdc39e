<script setup lang="ts">
import { computed, ref, toRaw } from 'vue'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { ChangeType } from '@/gql/graphql'
import RevertApproveRejectCertFields from '@/components/certified-data/RevertApproveRejectCertFields.vue'

interface FieldItem {
  __typename?: string
  typename?: string
  id: string
  name: string
  field: string
  value: any
  disabled: boolean
  isDifferent?: boolean
  changeRequested?: boolean
  approvedChanges?: boolean
  rejectReason?: any
  childIndex?: number
}

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
  activeCertification: {
    type: Boolean,
    default: false,
  },
  year: {
    type: Number,
    default: 2024,
  },
})

const emit = defineEmits(['update:children'])
const pensionStore = usePensionStore()
const appStore = useAppStore()

// Get children info from certifiedData
const childrenInfoToBeCertified = computed(() => {
  try {
    if (!pensionStore || !pensionStore.certifiedDataByYearAndYearBefore) {
      console.warn('Pension store or certified data not available')

      return []
    }

    const yearData = pensionStore.certifiedDataByYearAndYearBefore[props.year]
    if (!yearData || !Array.isArray(yearData) || yearData.length === 0) {
      console.warn(`No certified data found for year ${props.year}`)

      return []
    }

    const certifiedData = yearData[0]
    if (!certifiedData?.certifiedChild)
      return []

    const childrenInfo = certifiedData.certifiedChild

    return Array.isArray(childrenInfo) ? childrenInfo : []
  }
  catch (error) {
    console.error('Error getting children info:', error)

    return []
  }
})

// Get differences array from parent certified data
const childrenDifferences = computed(() => {
  try {
    if (!pensionStore || !pensionStore.certifiedDataByYearAndYearBefore)
      return []

    const yearData = pensionStore.certifiedDataByYearAndYearBefore[props.year]
    if (!yearData || !Array.isArray(yearData) || yearData.length === 0)
      return []

    const certifiedData = yearData[0]

    return certifiedData.certifiedChildDifferences || []
  }
  catch (error) {
    console.error('Error getting children differences:', error)

    return []
  }
})

// Parse differences to extract index and field
const parsedDifferences = computed(() => {
  const parsed: { index: number; field: string }[] = []

  childrenDifferences.value.forEach((diff: string) => {
    const match = diff.match(/\[(\d+)\]\.(.+)/)
    if (match) {
      parsed.push({
        index: Number.parseInt(match[1]),
        field: match[2],
      })
    }
  })

  return parsed
})

const yearInReview = computed(() => props.year)
const normalizedData = computed(() => pensionStore.normalizedCertifiedData)
const certifiedDataId = computed(() => normalizedData.value[yearInReview.value]?.id)
const onGoingCertification = computed(() => pensionStore.onGoingCertification)
const activeCertification = computed(() => onGoingCertification.value && !!props.editable)

// Collect all reject reasons for all children
const allRejectReasons = computed(() => {
  const reasons: Array<{ field: string; fieldKey: string; reason: string; disabled: boolean; childName: string }> = []

  try {
    if (!Array.isArray(childrenInfoToBeCertified.value))
      return reasons

    childrenInfoToBeCertified.value.forEach((childInfo: any, index: number) => {
      const childName = `${childInfo.firstName || ''} ${childInfo.lastName || ''}`.trim() || `Child ${index + 1}`

      if (childInfo?.certificationRejectReason && Array.isArray(childInfo.certificationRejectReason)) {
        childInfo.certificationRejectReason.forEach((item: any) => {
          if (item.reason && item.status === 'VALID') {
            const fieldData = processedChildrenData.value.find(f =>
              f.field === item.field && f.childIndex === index,
            )

            reasons.push({
              field: fieldKeyToName(item.field),
              fieldKey: item.field,
              reason: item.reason,
              disabled: fieldData?.disabled || false,
              childName,
            })
          }
        })
      }
    })
  }
  catch (error) {
    console.error('Error collecting reject reasons:', error)
  }

  return reasons
})

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

const processedChildrenData = computed((): FieldItem[] => {
  const transformed: FieldItem[] = []

  try {
    if (!Array.isArray(childrenInfoToBeCertified.value))
      return transformed

    childrenInfoToBeCertified.value.forEach((childInfo: any, childIndex: number) => {
      if (!childInfo)
        return

      const pendingChanges = childInfo.pendingChanges || []
      const requestedChanges = childInfo.requestedChanges || []
      const approvedChanges = childInfo.approvedChanges || []

      // Check which fields have differences for this child index
      const fieldsWithDifferences = parsedDifferences.value
        .filter(diff => diff.index === childIndex)
        .map(diff => diff.field)

      const relevantFields = ['firstName', 'lastName', 'dateOfBirth', 'isOrphan', 'isStudying']

      relevantFields.forEach(key => {
        if (key === 'id' || key === 'certifiedDataId')
          return

        if (Object.prototype.hasOwnProperty.call(childInfo, key) && childInfo[key] !== undefined && childInfo[key] !== null) {
          let value = childInfo[key]
          const name = fieldKeyToName(key)

          // Format dates
          if (key === 'dateOfBirth' && value) {
            try {
              const date = new Date(value)
              const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' }

              value = date.toLocaleDateString(undefined, options)
            }
            catch (e) {
              console.error(`Error formatting ${key}:`, e)
            }
          }

          // Format boolean values
          if (key === 'isOrphan' || key === 'isStudying')
            value = value ? 'Yes' : 'No'

          transformed.push({
            typename: 'CertifiedChild',
            id: childInfo.id,
            name,
            field: key,
            value,
            disabled: pendingChanges.includes(key),
            isDifferent: fieldsWithDifferences.includes(key),
            changeRequested: requestedChanges.includes(key),
            approvedChanges: approvedChanges.includes(key),
            rejectReason: childInfo.certificationRejectReason?.find((item: any) => item.field === key)?.reason,
            childIndex,
          })
        }
      })
    })
  }
  catch (error) {
    console.error('Error processing children data:', error)
  }

  return transformed
})

// Group processed data by child
const groupedChildrenData = computed(() => {
  const grouped: Record<number, { info: any; fields: FieldItem[] }> = {}

  childrenInfoToBeCertified.value.forEach((child: any, index: number) => {
    grouped[index] = {
      info: child,
      fields: processedChildrenData.value.filter(item => item.childIndex === index),
    }
  })

  return grouped
})

const editDialog = ref(false)
const selectedField = ref<FieldItem | null>(null)

const openEditDialog = (item: FieldItem) => {
  if (item.changeRequested)
    return openDialog(item)

  if (!isFieldEditable(item))
    return false

  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return false
  }

  openDialog(item)
}

const openDialog = (item: FieldItem) => {
  selectedField.value = item
  editDialog.value = true
}

const isFieldEditable = (item: FieldItem): boolean => {
  if (!props.editable)
    return false

  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return false
  }

  return true
}

const closeEditDialog = () => {
  editDialog.value = false
  selectedField.value = null
}

const updateField = (updatedValue: string) => {
  console.log('Field updated (placeholder):', selectedField.value?.field, updatedValue)
  closeEditDialog()
}

const showApprovalDialog = ref(false)
const fieldToApprove = ref<FieldItem | null>(null)

const showRevertDialog = ref(false)
const fieldToRevert = ref<FieldItem | null>(null)

// Chip and button styling functions (same as other components)
const getChipColor = (field: FieldItem): string => {
  if (field.approvedChanges)
    return 'success'
  if (field.changeRequested)
    return 'primary'
  if (field.isDifferent)
    return 'error'

  return 'default'
}

const getChipIcon = (field: FieldItem): string => {
  if (field.approvedChanges && field.isDifferent)
    return 'tabler-check'
  if (field.changeRequested)
    return 'tabler-clock'
  if (field.isDifferent)
    return 'tabler-alert-triangle'

  return ''
}

const getChipTooltip = (field: FieldItem): string => {
  if (field.approvedChanges)
    return 'Changes approved'
  if (field.changeRequested)
    return 'Changes requested'
  if (field.isDifferent)
    return 'There is a difference in values'

  return ''
}

const shouldShowActionButton = (field: FieldItem): boolean => {
  return Boolean(field.isDifferent || field.changeRequested)
}

const isActionButtonDisabled = (field: FieldItem): boolean => {
  return Boolean(field.changeRequested || field.approvedChanges)
}

const getActionButtonColor = (field: FieldItem): string => {
  if (field.changeRequested || field.approvedChanges)
    return 'grey'
  if (field.isDifferent)
    return 'warning'

  return 'primary'
}

const getActionButtonIcon = (field: FieldItem): string => {
  if (field.changeRequested)
    return 'tabler-clock'
  if (field.approvedChanges)
    return 'tabler-check'
  if (field.isDifferent)
    return 'tabler-gavel'

  return 'tabler-dots-vertical'
}

const getActionButtonTooltip = (field: FieldItem): string => {
  if (field.changeRequested)
    return 'Change request pending'
  if (field.approvedChanges)
    return 'Changes already approved'
  if (field.isDifferent)
    return 'Review and approve/reject changes'

  return 'Actions'
}

const handleActionClick = (field: FieldItem) => {
  console.log('Action button clicked for field:', toRaw(field))
  console.log('Field details:', {
    field: field.field,
    isDifferent: field.isDifferent,
    changeRequested: field.changeRequested,
    approvedChanges: field.approvedChanges,
    childIndex: field.childIndex,
  })

  if (isActionButtonDisabled(field)) {
    console.log('Action button is disabled, returning')

    return
  }

  if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
    console.log('Opening approval dialog for field:', field.field)
    fieldToApprove.value = field
    showApprovalDialog.value = true
  }
}

const getChildFullName = (child: any): string => {
  return `${child.firstName || ''} ${child.lastName || ''}`.trim() || 'N/A'
}

// Create a sorted array of children for UI display only
const sortedChildren = computed(() => {
  // Convert the grouped object to an array with index
  return Object.entries(groupedChildrenData.value)
    .map(([key, value]) => ({
      key,
      ...value,

      // Add full name for easier access in template
      fullName: getChildFullName(value.info),
    }))

  // Sort by firstName (this is the only sorting we need)
    .sort((a, b) => (a.info.firstName || '').localeCompare(b.info.firstName || ''))
})

// Get action button menu items
const getActionMenuItems = (field: FieldItem) => {
  const items = []

  if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
    items.push({
      title: 'Approve/Reject',
      icon: 'tabler-gavel',
      action: () => {
        fieldToApprove.value = field
        showApprovalDialog.value = true
      },
    })
  }

  if (field.changeRequested || field.approvedChanges) {
    items.push({
      title: 'Revert Changes',
      icon: 'tabler-rotate-clockwise',
      action: () => {
        fieldToRevert.value = field
        showRevertDialog.value = true
      },
    })
  }

  return items
}
</script>

<template>
  <div class="children-container">
    <div class="header-container">
      <h2>Children</h2>
      <div
        v-if="childrenInfoToBeCertified.length > 0"
        class="text-caption text-disabled"
      >
        {{ childrenInfoToBeCertified.length }} child{{ childrenInfoToBeCertified.length > 1 ? 'ren' : '' }} found
      </div>
    </div>

    <!-- Reject reasons alerts -->
    <div
      v-if="allRejectReasons.length > 0 && !activeCertification"
      class="mb-4"
    >
      <VAlert
        v-for="(item, index) in allRejectReasons"
        :key="`reject-${item.fieldKey}-${index}`"
        :type="item.disabled ? 'info' : 'error'"
        :variant="item.disabled ? 'tonal' : 'tonal'"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            :icon="item.disabled ? 'tabler-check' : 'tabler-x'"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.childName }} - {{ item.field }}:</strong>
          <span>{{ item.reason }}</span>
          <VChip
            v-if="item.disabled"
            size="small"
            color="primary"
            class="ml-auto"
          >
            Follow-up submitted
          </VChip>
        </div>
      </VAlert>
    </div>

    <div v-if="childrenInfoToBeCertified.length > 0">
      <div
        v-for="(childData, childIndex) in sortedChildren"
        :key="childData.info.id"
        class="child-section mb-4"
      >
        <div class="child-header">
          <h3>{{ childData.fullName }}</h3>
          <div class="text-caption text-disabled">
            Child {{ childIndex + 1 }}
            <span
              v-if="childData.info.dateOfBirth"
              class="ml-2"
            >
              • Born {{ new Date(childData.info.dateOfBirth).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' }) }}
            </span>
          </div>
        </div>

        <div v-if="activeCertification">
          <!-- Active certification view -->
          <VDataTable
            :headers="[
              { title: 'Field', key: 'name' },
              { title: 'Value', key: 'value' },
              { title: 'Actions', key: 'actions', sortable: false },
            ]"
            :items="childData.fields"
            hide-default-footer
            class="elevation-0"
            density="compact"
          >
            <template #item.value="{ item }">
              <VChip
                v-if="item.isDifferent || item.changeRequested"
                :model-value="true"
                class="ma-2"
                size="small"
                :color="getChipColor(item)"
                :prepend-icon="getChipIcon(item)"
              >
                {{ item.value }}
                <VTooltip
                  activator="parent"
                  location="top"
                >
                  {{ getChipTooltip(item) }}
                </VTooltip>
              </VChip>
              <span v-else>{{ item.value }}</span>
            </template>
            <template #item.actions="{ item }">
              <VMenu v-if="shouldShowActionButton(item)">
                <template #activator="{ props }">
                  <VBtn
                    icon
                    size="small"
                    variant="text"
                    :color="getActionButtonColor(item)"
                    v-bind="props"
                    :disabled="isActionButtonDisabled(item)"
                  >
                    <VIcon :icon="getActionButtonIcon(item)" />
                  </VBtn>
                </template>

                <VList>
                  <VListItem
                    v-for="menuItem in getActionMenuItems(item)"
                    :key="menuItem.title"
                    @click="menuItem.action"
                  >
                    <template #prepend>
                      <VIcon :icon="menuItem.icon" />
                    </template>
                    <VListItemTitle>{{ menuItem.title }}</VListItemTitle>
                  </VListItem>
                </VList>
              </VMenu>
            </template>
          </VDataTable>
        </div>
        <div v-else>
          <!-- Regular view -->
          <VDataTable
            :headers="[
              { title: 'Field', key: 'name' },
              { title: 'Value', key: 'value' },
              { title: 'Actions', key: 'actions', sortable: false },
            ]"
            :items="childData.fields"
            hide-default-footer
            class="elevation-0"
            density="compact"
          >
            <template #item.value="{ item }">
              <template v-if="item.changeRequested">
                <VBadge
                  v-if="!item.disabled && item.changeRequested"
                  color="error"
                  size="small"
                  icon="tabler-exclamation-mark"
                >
                  <VChip
                    class="font-weight-bold"
                    label
                    :color="item.disabled ? 'primary' : 'error'"
                    density="compact"
                  >
                    {{ item.value }}
                  </VChip>
                </VBadge>
                <VChip
                  v-else
                  class="font-weight-bold"
                  label
                  :color="item.disabled ? 'primary' : 'error'"
                  density="compact"
                >
                  {{ item.value }}
                </VChip>
              </template>
              <template v-else>
                {{ item.value }}
              </template>
            </template>
            <template #item.actions="{ item }">
              <VBtn
                v-if="props.editable || item.changeRequested"
                icon
                size="small"
                variant="text"
                color="primary"
                @click="openEditDialog(item)"
              >
                <VIcon
                  v-if="item?.disabled"
                  size="16"
                  icon="tabler-alert-triangle"
                  class="edit-icon ms-1 ml-4"
                  color="error"
                />
                <VIcon
                  v-else
                  size="16"
                  icon="tabler-edit"
                  class="edit-icon ms-1 ml-4"
                  color="primary"
                />
              </VBtn>
            </template>
          </VDataTable>
        </div>
      </div>
    </div>

    <div
      v-else
      class="text-caption text-disabled pa-4 text-center"
    >
      No children information available.
    </div>

    <!-- Dynamic Dialogs -->
    <EditCertificationFieldDialog
      v-if="editDialog && selectedField"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.name"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      entity-type="CertifiedChild"
      :participant-name="selectedField ? getChildFullName(groupedChildrenData[selectedField.childIndex || 0].info) : ''"
      :year="yearInReview"
      :type="ChangeType.CertifiedData"
      @close="closeEditDialog"
      @update="updateField"
    />

    <ApproveRejectCertFields
      v-model="showApprovalDialog"
      :field="fieldToApprove"
      :year="yearInReview"
      entity-type="certifiedChild"
      :participant-name="fieldToApprove ? getChildFullName(groupedChildrenData[fieldToApprove.childIndex || 0].info) : ''"
      :certified-data-id="certifiedDataId"
      @close="showApprovalDialog = false"
    />

    <RevertApproveRejectCertFields
      v-model="showRevertDialog"
      :field="fieldToRevert"
      :year="yearInReview"
      entity-type="certifiedChild"
      :participant-name="fieldToRevert ? getChildFullName(groupedChildrenData[fieldToRevert.childIndex || 0].info) : ''"
      :certified-data-id="certifiedDataId"
      @close="showRevertDialog = false"
    />
  </div>
</template>

<style scoped>
  .children-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .child-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .child-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #f0f0f0;
  }

  .child-header h3 {
    margin: 0;
    font-size: 1.1rem;
  }
</style>
