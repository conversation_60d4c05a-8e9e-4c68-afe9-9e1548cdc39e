<script setup lang="ts">
import { computed, ref, toRaw } from 'vue'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { ChangeType } from '@/gql/graphql'
import RevertApproveRejectCertFields from '@/components/certified-data/RevertApproveRejectCertFields.vue'

interface FieldItem {
  __typename?: string
  typename?: string
  id: string
  name: string
  field: string
  value: any
  disabled: boolean
  isDifferent?: boolean
  changeRequested?: boolean
  approvedChanges?: boolean
  rejectReason?: any
}

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
  activeCertification: {
    type: Boolean,
    default: false,
  },
  year: {
    type: Number,
    default: 2024,
  },
})

const emit = defineEmits(['update:maritalStatus'])
const pensionStore = usePensionStore()
const appStore = useAppStore()

// Get partner info from certifiedData instead of participantPartnerInfo
const partnerInfoToBeCertified = computed(() => {
  try {
    // Check if pensionStore and its data are available
    if (!pensionStore || !pensionStore.certifiedDataByYearAndYearBefore) {
      console.warn('Pension store or certified data not available')

      return []
    }

    // Check if the year data exists
    const yearData = pensionStore.certifiedDataByYearAndYearBefore[props.year]
    if (!yearData || !Array.isArray(yearData) || yearData.length === 0) {
      console.warn(`No certified data found for year ${props.year}`)

      return []
    }

    const certifiedData = yearData[0]
    if (!certifiedData?.certifiedPartnerInfo)
      return []

    // Ensure partnerInfo is an array
    const partnerInfo = certifiedData.certifiedPartnerInfo
    if (Array.isArray(partnerInfo))
      return partnerInfo.filter((p: any) => p.isCurrent)
    if (Array.isArray(partnerInfo))
      return partnerInfo

    if (partnerInfo && typeof partnerInfo === 'object')
      return [partnerInfo]

    return []
  }
  catch (error) {
    console.error('Error getting partner info:', error)

    return []
  }
})

// Get differences array from parent certified data
const partnerInfoDifferences = computed(() => {
  try {
    if (!pensionStore || !pensionStore.certifiedDataByYearAndYearBefore)
      return []

    const yearData = pensionStore.certifiedDataByYearAndYearBefore[props.year]
    if (!yearData || !Array.isArray(yearData) || yearData.length === 0)
      return []

    const certifiedData = yearData[0]

    return certifiedData.certifiedPartnerInfoDifferences || []
  }
  catch (error) {
    console.error('Error getting partner info differences:', error)

    return []
  }
})

// Parse differences to extract index and field
const parsedDifferences = computed(() => {
  const parsed: { index: number; field: string }[] = []

  partnerInfoDifferences.value.forEach((diff: string) => {
    // Check for array index format like "[0].firstName"
    const match = diff.match(/\[(\d+)\]\.(.+)/)
    if (match) {
      parsed.push({
        index: Number.parseInt(match[1]),
        field: match[2],
      })
    }
    else if (diff === '_count') {
      // Handle count difference
      parsed.push({
        index: -1,
        field: '_count',
      })
    }
  })

  return parsed
})

const yearInReview = computed(() => props.year)
const normalizedData = computed(() => pensionStore.normalizedCertifiedData)
const certifiedDataId = computed(() => normalizedData.value[yearInReview.value]?.id)
const onGoingCertification = computed(() => pensionStore.onGoingCertification)
const activeCertification = computed(() => onGoingCertification.value && !!props.editable)

// Collect all reject reasons
const allRejectReasons = computed(() => {
  const reasons: Array<{ field: string; fieldKey: string; reason: string; disabled: boolean }> = []

  try {
    if (!Array.isArray(partnerInfoToBeCertified.value))
      return reasons

    partnerInfoToBeCertified.value.forEach((partnerInfo: any) => {
      if (partnerInfo?.certificationRejectReason && Array.isArray(partnerInfo.certificationRejectReason)) {
        partnerInfo.certificationRejectReason.forEach((item: any) => {
          if (item.reason && item.status === 'VALID') {
            const fieldData = processedParticipantData.value.find(f => f.field === item.field)

            reasons.push({
              field: fieldKeyToName(item.field),
              fieldKey: item.field,
              reason: item.reason,
              disabled: fieldData?.disabled || false,
            })
          }
        })
      }
    })
  }
  catch (error) {
    console.error('Error collecting reject reasons:', error)
  }

  return reasons
})

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

const processedParticipantData = computed((): FieldItem[] => {
  const transformed: FieldItem[] = []

  try {
    if (!Array.isArray(partnerInfoToBeCertified.value))
      return transformed

    partnerInfoToBeCertified.value.forEach((partnerInfo: any, partnerIndex: number) => {
      if (!partnerInfo || !partnerInfo.isCurrent)
        return // Only show current partner

      const pendingChanges = partnerInfo.pendingChanges || []
      const requestedChanges = partnerInfo.requestedChanges || []
      const approvedChanges = partnerInfo.approvedChanges || []

      // Check which fields have differences for this partner index
      const fieldsWithDifferences = parsedDifferences.value
        .filter(diff => diff.index === partnerIndex)
        .map(diff => diff.field)

      const relevantFields = ['firstName', 'lastName', 'dateOfBirth', 'isDeceased', 'startDate']

      relevantFields.forEach(key => {
        if (key === 'id' || key === 'isCurrent' || key === 'certifiedPersonalInfoId')
          return

        if (Object.prototype.hasOwnProperty.call(partnerInfo, key) && partnerInfo[key] !== undefined && partnerInfo[key] !== null) {
          let value = partnerInfo[key]
          const name = fieldKeyToName(key)

          // Format dates
          if ((key === 'dateOfBirth' || key === 'startDate') && value) {
            try {
              const date = new Date(value)
              const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' }

              value = date.toLocaleDateString(undefined, options)
            }
            catch (e) {
              console.error(`Error formatting ${key}:`, e)
            }
          }

          // Format boolean values
          if (key === 'isDeceased')
            value = value ? 'Yes' : 'No'

          transformed.push({
            typename: 'CertifiedPartnerInfo',
            id: partnerInfo.id,
            name,
            field: key,
            value,
            disabled: pendingChanges.includes(key),
            isDifferent: fieldsWithDifferences.includes(key),
            changeRequested: requestedChanges.includes(key),
            approvedChanges: approvedChanges.includes(key),
            rejectReason: partnerInfo.certificationRejectReason?.find((item: any) => item.field === key)?.reason,
          })
        }
      })
    })
  }
  catch (error) {
    console.error('Error processing participant data:', error)
  }

  return transformed
})

const fieldsToExclude = ['id', 'isCurrent', 'certifiedPersonalInfoId']

const displayableFields = computed(() => {
  return processedParticipantData.value.filter(item => !fieldsToExclude.includes(item.field))
})

const editDialog = ref(false)
const selectedField = ref<FieldItem | null>(null)

const openEditDialog = (item: FieldItem) => {
  // Early return if the field has requested changes (allow viewing even if not editable)
  if (item.changeRequested)
    return openDialog(item)

  // Check editability conditions
  if (!isFieldEditable(item))
    return false

  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return false
  }

  // All checks passed, open the dialog
  openDialog(item)
}

// Helper function to open the dialog
const openDialog = (item: FieldItem) => {
  selectedField.value = item
  editDialog.value = true
}

// Helper function to check editability conditions
const isFieldEditable = (item: FieldItem): boolean => {
  if (!props.editable)
    return false

  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return false
  }

  return true
}

const closeEditDialog = () => {
  editDialog.value = false
  selectedField.value = null
}

const updateField = (updatedValue: string) => {
  console.log('Field updated (placeholder):', selectedField.value?.field, updatedValue)
  closeEditDialog()
}

const showApprovalDialog = ref(false)
const fieldToApprove = ref<FieldItem | null>(null)

const showRevertDialog = ref(false)
const fieldToRevert = ref<FieldItem | null>(null)

// Determine chip color based on field state
const getChipColor = (field: FieldItem): string => {
  if (field.approvedChanges)
    return 'success'
  if (field.changeRequested)
    return 'primary'
  if (field.isDifferent)
    return 'error'

  return 'default'
}

// Determine chip icon based on field state
const getChipIcon = (field: FieldItem): string => {
  if (field.approvedChanges && field.isDifferent)
    return 'tabler-check'
  if (field.changeRequested)
    return 'tabler-clock'
  if (field.isDifferent)
    return 'tabler-alert-triangle'

  return ''
}

// Get tooltip text for chips
const getChipTooltip = (field: FieldItem): string => {
  if (field.approvedChanges)
    return 'Changes approved'
  if (field.changeRequested)
    return 'Changes requested'
  if (field.isDifferent)
    return 'There is a difference in values'

  return ''
}

// Determine if action button should be shown
const shouldShowActionButton = (field: FieldItem): boolean => {
  return Boolean(field.isDifferent || field.changeRequested)
}

// Determine if action button should be disabled
const isActionButtonDisabled = (field: FieldItem): boolean => {
  return Boolean(field.changeRequested || field.approvedChanges)
}

// Get action button color
const getActionButtonColor = (field: FieldItem): string => {
  if (field.changeRequested || field.approvedChanges)
    return 'grey'
  if (field.isDifferent)
    return 'warning'

  return 'primary'
}

// Get action button icon
const getActionButtonIcon = (field: FieldItem): string => {
  if (field.changeRequested)
    return 'tabler-clock'
  if (field.approvedChanges)
    return 'tabler-check'
  if (field.isDifferent)
    return 'tabler-gavel'

  return 'tabler-dots-vertical'
}

// Get action button tooltip
const getActionButtonTooltip = (field: FieldItem): string => {
  if (field.changeRequested)
    return 'Change request pending'
  if (field.approvedChanges)
    return 'Changes already approved'
  if (field.isDifferent)
    return 'Review and approve/reject changes'

  return 'Actions'
}

// Handle action button click
const handleActionClick = (field: FieldItem) => {
  // Use toRaw to unwrap the proxy for cleaner logging
  console.log('Action button clicked for field:', toRaw(field))
  console.log('Field details:', {
    field: field.field,
    isDifferent: field.isDifferent,
    changeRequested: field.changeRequested,
    approvedChanges: field.approvedChanges,
  })

  if (isActionButtonDisabled(field)) {
    console.log('Action button is disabled, returning')

    return
  }

  if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
    console.log('Opening approval dialog for field:', field.field)
    fieldToApprove.value = field
    showApprovalDialog.value = true
  }
}

const fullName = computed(() => {
  try {
    if (!Array.isArray(partnerInfoToBeCertified.value) || partnerInfoToBeCertified.value.length === 0)
      return 'N/A'

    const currentPartner = partnerInfoToBeCertified.value.find((p: any) => p.isCurrent)
    if (currentPartner)
      return `${currentPartner.firstName || ''} ${currentPartner.lastName || ''}`.trim() || 'N/A'

    return 'N/A'
  }
  catch (error) {
    console.error('Error computing full name:', error)

    return 'N/A'
  }
})

// Get action button menu items
const getActionMenuItems = (field: FieldItem) => {
  const items = []

  if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
    items.push({
      title: 'Approve/Reject',
      icon: 'tabler-gavel',
      action: () => {
        fieldToApprove.value = field
        showApprovalDialog.value = true
      },
    })
  }

  if (field.changeRequested || field.approvedChanges) {
    items.push({
      title: 'Revert Changes',
      icon: 'tabler-rotate-clockwise',
      action: () => {
        fieldToRevert.value = field
        showRevertDialog.value = true
      },
    })
  }

  return items
}
</script>

<template>
  <div class="marital-status-container">
    <div class="header-container">
      <h2>Marital Status</h2>
      <div
        v-if="fullName !== 'N/A'"
        class="text-caption text-disabled"
      >
        Current Partner: {{ fullName }}
      </div>
    </div>

    <!-- Individual alerts for each rejected field -->
    <div
      v-if="allRejectReasons.length > 0 && !activeCertification"
      class="mb-4"
    >
      <VAlert
        v-for="(item, index) in allRejectReasons"
        :key="`reject-${item.fieldKey}-${index}`"
        :type="item.disabled ? 'info' : 'error'"
        :variant="item.disabled ? 'tonal' : 'tonal'"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            :icon="item.disabled ? 'tabler-check' : 'tabler-x'"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.field }}:</strong>
          <span>{{ item.reason }}</span>
          <VChip
            v-if="item.disabled"
            size="small"
            color="primary"
            class="ml-auto"
          >
            Follow-up submitted
          </VChip>
        </div>
      </VAlert>
    </div>

    <div v-if="activeCertification">
      <!-- Active certification view with chips and action buttons -->
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="displayableFields"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.value="{ item }">
          <VChip
            v-if="item.isDifferent || item.changeRequested"
            :model-value="true"
            class="ma-2"
            size="small"
            :color="getChipColor(item)"
            :prepend-icon="getChipIcon(item)"
          >
            {{ item.value }}
            <VTooltip
              activator="parent"
              location="top"
            >
              {{ getChipTooltip(item) }}
            </VTooltip>
          </VChip>
          <span v-else>{{ item.value }}</span>
        </template>
        <template #item.actions="{ item }">
          <VMenu v-if="shouldShowActionButton(item)">
            <template #activator="{ props }">
              <VBtn
                icon
                size="small"
                variant="text"
                :color="getActionButtonColor(item)"
                v-bind="props"
                :disabled="isActionButtonDisabled(item)"
              >
                <VIcon :icon="getActionButtonIcon(item)" />
              </VBtn>
            </template>

            <VList>
              <VListItem
                v-for="menuItem in getActionMenuItems(item)"
                :key="menuItem.title"
                @click="menuItem.action"
              >
                <template #prepend>
                  <VIcon :icon="menuItem.icon" />
                </template>
                <VListItemTitle>{{ menuItem.title }}</VListItemTitle>
              </VListItem>
            </VList>
          </VMenu>
        </template>
      </VDataTable>
    </div>
    <div v-else>
      <!-- Regular view with edit capability -->
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="displayableFields"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.value="{ item }">
          <template v-if="item.changeRequested">
            <VBadge
              v-if="!item.disabled && item.changeRequested"
              color="error"
              size="small"
              icon="tabler-exclamation-mark"
            >
              <VChip
                class="font-weight-bold"
                label
                :color="item.disabled ? 'primary' : 'error'"
                density="compact"
              >
                {{ item.value }}
              </VChip>
            </VBadge>
            <VChip
              v-else
              class="font-weight-bold"
              label
              :color="item.disabled ? 'primary' : 'error'"
              density="compact"
            >
              {{ item.value }}
            </VChip>
          </template>
          <template v-else>
            {{ item.value }}
          </template>
        </template>
        <template #item.actions="{ item }">
          <VBtn
            v-if="props.editable || item.changeRequested"
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <VIcon
              v-if="item?.disabled"
              size="16"
              icon="tabler-alert-triangle"
              class="edit-icon ms-1 ml-4"
              color="error"
            />
            <VIcon
              v-else
              size="16"
              icon="tabler-edit"
              class="edit-icon ms-1 ml-4"
              color="primary"
            />
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <div
      v-if="displayableFields.length === 0"
      class="text-caption text-disabled pa-2"
    >
      No partner information available.
    </div>

    <!-- Dynamic Dialog -->
    <EditCertificationFieldDialog
      v-if="editDialog && selectedField"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.name"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      entity-type="CertifiedPartnerInfo"
      :participant-name="fullName"
      :year="yearInReview"
      :type="ChangeType.CertifiedData"
      @close="closeEditDialog"
      @update="updateField"
    />

    <ApproveRejectCertFields
      v-model="showApprovalDialog"
      :field="fieldToApprove"
      :year="yearInReview"
      entity-type="certifiedPartnerInfo"
      :participant-name="fullName"
      :certified-data-id="certifiedDataId"
      @close="showApprovalDialog = false"
    />

    <RevertApproveRejectCertFields
      v-model="showRevertDialog"
      :field="fieldToRevert"
      :year="yearInReview"
      entity-type="certifiedPartnerInfo"
      :participant-name="fullName"
      :certified-data-id="certifiedDataId"
      @close="showRevertDialog = false"
    />
  </div>
</template>

<style scoped>
  .marital-status-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .marital-status-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }
</style>
