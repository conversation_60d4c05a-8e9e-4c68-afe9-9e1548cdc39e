<script setup lang="ts">
import { computed, ref } from 'vue'
import avatar from '../../../assets/images/avatars/avatar-0.png'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { ChangeType } from '@/gql/graphql'
import RevertApproveRejectCertFields from '@/components/certified-data/RevertApproveRejectCertFields.vue'

interface FieldItem {
  __typename?: string
  typename?: string
  id: string
  name: string
  field: string
  value: any
  disabled: boolean
  isDifferent?: boolean
  changeRequested?: boolean
  approvedChanges?: boolean
  rejectReason?: any
}

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
  activeCertification: {
    type: Boolean,
    default: false,
  },
  year: {
    type: Number,
    default: 2024,
  },
})

const emit = defineEmits(['update:information'])
const { state: { participantDetails, loadingParticipant }, actions: { getYearInReview: getYearInReviewAction } } = useParticipants()
const pensionStore = usePensionStore()
const appStore = useAppStore()

const editDialog = ref(false)
const selectedField = ref<FieldItem | null>(null)

const yearInReview = computed(() => props.year)
const normalizedData = computed(() => pensionStore.normalizedCertifiedData)
const certifiedDataId = computed(() => normalizedData.value[yearInReview.value]?.id)
const onGoingCertification = computed(() => pensionStore.onGoingCertification)
const activeCertification = computed(() => onGoingCertification.value && !!props.editable)
const personalInfoToBeCertified = computed(() => pensionStore.certifiedDataByYearAndYearBefore[props.year][0].certifiedPersonalInfo)

// Collect all reject reasons
const allRejectReasons = computed(() => {
  const reasons: Array<{ field: string; fieldKey: string; reason: string; disabled: boolean }> = []
  const certifiedInfo = personalInfoToBeCertified.value

  if (certifiedInfo?.certificationRejectReason && Array.isArray(certifiedInfo.certificationRejectReason)) {
    certifiedInfo.certificationRejectReason.forEach((item: any) => {
      // Only include reject reasons with VALID status
      if (item.reason && item.status === 'VALID') {
        // Find if this field is in processedParticipantData to get its disabled status
        const fieldData = processedParticipantData.value.find(f => f.field === item.field)

        reasons.push({
          field: fieldKeyToName(item.field),
          fieldKey: item.field,
          reason: item.reason,
          disabled: fieldData?.disabled || false,
        })
      }
    })
  }

  return reasons
})

function fieldKeyToName(fieldKey: string): string {
  if (!fieldKey)
    return ''
  const result = fieldKey.replace(/([A-Z])/g, ' $1')

  return result.charAt(0).toUpperCase() + result.slice(1).trim()
}

const processedParticipantData = computed((): FieldItem[] => {
  const certifiedInfo = personalInfoToBeCertified.value

  if (!certifiedInfo)
    return []

  const transformed: FieldItem[] = []
  const relevantFields: (keyof typeof certifiedInfo)[] = ['firstName', 'lastName', 'email', 'phone', 'maritalStatus']
  const pendingChanges = certifiedInfo.pendingChanges || []
  const differences = certifiedInfo.differences || []
  const requestedChanges = certifiedInfo.requestedChanges || []
  const approvedChanges = certifiedInfo.approvedChanges || []

  // certificationRejectReason: [{"id":"9f98084c-8bad-49d5-9289-f8ec83da584f","field":"firstName","reason":"rejected here","createdAt":"2025-05-27T13:11:01.029Z","updatedAt":"2025-05-27T13:11:01.029Z","certifiedDataId":null,"certifiedPensionInfoId":null,"certifiedSalaryEntryId":null,"certifiedEmploymentInfoId":null,"certifiedPersonalInfoId":"ca176df6-f999-414b-8969-0d14fc1913a0","certifiedIndexationStartOfYearId":null,"certifiedPensionCorrectionsId":null,"certifiedVoluntaryContributionsId":null,"certifiedPensionParametersId":null},{"id":"5efe142e-7660-4079-8513-18b453bc2bec","field":"lastName","reason":"rejected here","createdAt":"2025-05-27T13:11:35.488Z","updatedAt":"2025-05-27T13:11:35.488Z","certifiedDataId":null,"certifiedPensionInfoId":null,"certifiedSalaryEntryId":null,"certifiedEmploymentInfoId":null,"certifiedPersonalInfoId":"ca176df6-f999-414b-8969-0d14fc1913a0","certifiedIndexationStartOfYearId":null,"certifiedPensionCorrectionsId":null,"certifiedVoluntaryContributionsId":null,"certifiedPensionParametersId":null}]

  relevantFields.forEach(key => {
    if (Object.prototype.hasOwnProperty.call(certifiedInfo, key) && certifiedInfo[key] !== undefined && certifiedInfo[key] !== null) {
      transformed.push({
        typename: 'CertifiedPersonalInfo',
        id: certifiedInfo.id,
        name: fieldKeyToName(key as string),
        field: key as string,
        value: certifiedInfo[key],
        disabled: pendingChanges.includes(key),
        isDifferent: differences.includes(key),
        changeRequested: requestedChanges.includes(key),
        approvedChanges: approvedChanges.includes(key),
        rejectReason: certifiedInfo.certificationRejectReason?.find((item: any) => item.field === key)?.reason,
      })
    }
  })

  if (certifiedInfo.birthYear && certifiedInfo.birthMonth && certifiedInfo.birthDay) {
    try {
      const date = new Date(certifiedInfo.birthYear, certifiedInfo.birthMonth - 1, certifiedInfo.birthDay)
      const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' }

      transformed.push({
        typename: 'CertifiedPersonalInfo',
        id: certifiedInfo.id,
        name: 'Birth Date',
        field: 'birthDate',
        value: date.toLocaleDateString(undefined, options),
        disabled: pendingChanges.includes('birthDate'),
        isDifferent: differences.includes('birthDate'),
        changeRequested: requestedChanges.includes('birthDate'),
        approvedChanges: approvedChanges.includes('birthDate'),
        rejectReason: certifiedInfo.certificationRejectReason?.find((item: any) => item.field === 'birthDate')?.reason,
      })
    }
    catch (e) {
      console.error('Error formatting birth date from certified info:', e)
    }
  }

  return transformed
})

const fieldsToExclude = [
  'pendingChanges',
  'birthDay',
  'birthMonth',
  'birthYear',
  'id',
  'participantId',
  'partnerInfo',
  'children',
  'personalInfo',
  'address',
]

const displayableFields = computed(() => {
  return processedParticipantData.value.filter(item => !fieldsToExclude.includes(item.field))
})

const openEditDialog = (item: FieldItem) => {
  // Early return if the field has requested changes (allow viewing even if not editable)
  if (item.changeRequested)
    return openDialog(item)

  // Check editability conditions
  if (!isFieldEditable(item))
    return false

  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return false
  }

  // All checks passed, open the dialog
  openDialog(item)
}

// Helper function to open the dialog
const openDialog = (item: FieldItem) => {
  selectedField.value = item
  editDialog.value = true
}

// Helper function to check editability conditions
const isFieldEditable = (item: FieldItem): boolean => {
  if (!props.editable)
    return false

  console.log('Item disabled:', item.disabled)

  if (item.disabled) {
    appStore.showSnack('Sorry you cannot edit this field')

    return false
  }

  return true
}

const closeEditDialog = () => {
  editDialog.value = false
  selectedField.value = null
}

const updateField = (updatedValue: string) => {
  console.log('Field updated (placeholder):', selectedField.value?.field, updatedValue)
  closeEditDialog()
}

const fullName = computed(() => {
  const info = personalInfoToBeCertified.value
  if (info)
    return `${info.firstName || ''} ${info.lastName || ''}`.trim()

  return 'N/A'
})

const getIconForField = (fieldName: string): string => {
  const iconMap: Record<string, string> = {
    phone: 'tabler-phone',
    birthDate: 'tabler-calendar-event',
    firstName: 'tabler-user-circle',
    lastName: 'tabler-user-circle',
    email: 'tabler-mail',
    maritalStatus: 'tabler-heart',
    Address: 'tabler-home',
  }

  return iconMap[fieldName] || 'tabler-info-circle'
}

const formatFieldValue = (field: FieldItem): string => {
  if (field.field.toLowerCase().includes('date') && field.value) {
    try {
      return new Date(field.value).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' })
    }
    catch (e) {
      return String(field.value)
    }
  }

  return String(field.value ?? '')
}

const showApprovalDialog = ref(false)
const fieldToApprove = ref<FieldItem | null>(null)

const showRevertDialog = ref(false)
const fieldToRevert = ref<FieldItem | null>(null)

// Determine chip color based on field state
const getChipColor = (field: FieldItem): string => {
  if (field.approvedChanges)
    return 'success'
  if (field.changeRequested)
    return 'primary'
  if (field.isDifferent)
    return 'error'

  return 'default'
}

// Determine chip icon based on field state
const getChipIcon = (field: FieldItem): string => {
  if (field.approvedChanges && field.isDifferent)
    return 'tabler-check'
  if (field.changeRequested)
    return 'tabler-clock'
  if (field.isDifferent)
    return 'tabler-alert-triangle'

  return ''
}

// Get tooltip text for chips
const getChipTooltip = (field: FieldItem): string => {
  if (field.approvedChanges)
    return 'Changes approved'
  if (field.changeRequested)
    return 'Changes requested'
  if (field.isDifferent)
    return 'There is a difference in values'

  return ''
}

// Determine if action button should be shown
const shouldShowActionButton = (field: FieldItem): boolean => {
  // Show button only if there are differences or change requests
  return Boolean(field.isDifferent || field.changeRequested)
}

// Determine if action button should be disabled
const isActionButtonDisabled = (field: FieldItem): boolean => {
  // Disable if there are already change requests or approved changes
  return Boolean(field.changeRequested || field.approvedChanges)
}

// Get action button color
const getActionButtonColor = (field: FieldItem): string => {
  if (field.changeRequested || field.approvedChanges)
    return 'grey'
  if (field.isDifferent)
    return 'warning'

  return 'primary'
}

// Get action button icon
const getActionButtonIcon = (field: FieldItem): string => {
  if (field.changeRequested)
    return 'tabler-clock'
  if (field.approvedChanges)
    return 'tabler-check'
  if (field.isDifferent)
    return 'tabler-gavel' // or 'tabler-clipboard-check'

  return 'tabler-dots-vertical'
}

// Get action button tooltip
const getActionButtonTooltip = (field: FieldItem): string => {
  if (field.changeRequested)
    return 'Change request pending'
  if (field.approvedChanges)
    return 'Changes already approved'
  if (field.isDifferent)
    return 'Review and approve/reject changes'

  return 'Actions'
}

// Handle action button click
const handleActionClick = (field: FieldItem) => {
  if (isActionButtonDisabled(field))
    return

  console.log('Action button clicked for field:', field)

  if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
    fieldToApprove.value = field
    showApprovalDialog.value = true
  }
}

// Get action button menu items
const getActionMenuItems = (field: FieldItem) => {
  const items = []

  if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
    items.push({
      title: 'Approve/Reject',
      icon: 'tabler-gavel',
      action: () => {
        fieldToApprove.value = field
        showApprovalDialog.value = true
      },
    })
  }

  if (field.changeRequested || field.approvedChanges) {
    items.push({
      title: 'Revert Changes',
      icon: 'tabler-rotate-clockwise',
      action: () => {
        fieldToRevert.value = field
        showRevertDialog.value = true
      },
    })
  }

  return items
}
</script>

<template>
  <VSkeletonLoader
    v-if="loadingParticipant"
    class="mx-auto border py-6"
    type="list-item-avatar-three-line"
  />
  <VCard
    v-else
    class="basic-information-container"
  >
    <VCardText>
      <!-- Individual alerts for each rejected field -->
      <div
        v-if="allRejectReasons.length > 0 && !activeCertification"
        class="mb-4"
      >
        <VAlert
          v-for="(item, index) in allRejectReasons"
          :key="`reject-${item.fieldKey}-${index}`"
          :type="item.disabled ? 'info' : 'error'"
          :variant="item.disabled ? 'tonal' : 'tonal'"
          class="mb-2"
          closable
        >
          <template #prepend>
            <VIcon
              :icon="item.disabled ? 'tabler-check' : 'tabler-x'"
              size="10"
            />
          </template>
          <div class="d-flex align-center">
            <strong class="mr-2">{{ item.field }}:</strong>
            <span>{{ item.reason }}</span>
            <VChip
              v-if="item.disabled"
              size="small"
              color="primary"
              class="ml-auto"
            >
              Follow-up submitted
            </VChip>
          </div>
        </VAlert>
      </div>

      <div class="d-flex align-bottom flex-sm-row flex-column justify-center gap-x-5">
        <div class="d-flex">
          <VAvatar
            size="100"
            :image="avatar"
            class="mx-auto my-auto"
          />
        </div>
        <div class="w-100 mt-8 pt-4 mt-sm-0">
          <h6 class="text-h4 text-center text-sm-start font-weight-medium mb-3">
            {{ fullName }}
          </h6>

          <div class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4">
            <div
              v-if="activeCertification"
              class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4"
            >
              <div
                v-for="field in displayableFields"
                :key="field.field"
                class="d-flex align-center field-container"
              >
                <VIcon
                  size="20"
                  :icon="getIconForField(field.field)"
                  class="me-1"
                />
                <span class="text-body-1 mr-1">
                  <template v-if="field.field === 'status'">
                    <VChip
                      class="ml-2 font-weight-bold"
                      label
                      color="success"
                      density="compact"
                    >
                      {{ formatFieldValue(field) }}
                    </VChip>
                  </template>
                  <template v-else-if="field.isDifferent || field.changeRequested">
                    <VChip
                      :model-value="true"
                      class="ma-2"
                      size="small"
                      :color="getChipColor(field)"
                      :prepend-icon="getChipIcon(field)"
                    >
                      {{ formatFieldValue(field) }}
                      <VTooltip
                        activator="parent"
                        location="top"
                      >
                        {{ getChipTooltip(field) }}
                      </VTooltip>
                    </VChip>
                  </template>
                  <template v-else>
                    {{ formatFieldValue(field) }}
                  </template>
                </span>

                <!-- Action Button for fields that need approval/rejection -->
                <VMenu v-if="shouldShowActionButton(field)">
                  <template #activator="{ props }">
                    <VBtn
                      icon
                      size="small"
                      variant="text"
                      :color="getActionButtonColor(field)"
                      v-bind="props"
                      :disabled="isActionButtonDisabled(field)"
                    >
                      <VIcon :icon="getActionButtonIcon(field)" />
                    </VBtn>
                  </template>

                  <VList>
                    <VListItem
                      v-for="menuItem in getActionMenuItems(field)"
                      :key="menuItem.title"
                      @click="menuItem.action"
                    >
                      <template #prepend>
                        <VIcon :icon="menuItem.icon" />
                      </template>
                      <VListItemTitle>{{ menuItem.title }}</VListItemTitle>
                    </VListItem>
                  </VList>
                </VMenu>
              </div>

              <div
                v-if="displayableFields.length === 0 && processedParticipantData.length > 0"
                class="text-caption text-disabled pa-2"
              >
                No details to display based on current filters.
              </div>
              <div
                v-if="!loadingParticipant && processedParticipantData.length === 0"
                class="text-caption text-disabled pa-2"
              >
                No participant details available.
              </div>
            </div>

            <div
              v-else
              class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4"
            >
              <span
                v-for="field in displayableFields"
                :key="field.field"
                class="d-flex align-center"
                :class="[{
                  'cursor-pointer': (props.editable && !field.disabled) || field.changeRequested,
                  'cursor-default': !props.editable || field.disabled,
                }]"
                @click="(!field.disabled) ? openEditDialog(field) : null"
              >
                <VTemplate v-if="field.changeRequested">
                  <VIcon
                    size="20"
                    :icon="getIconForField(field.field)"
                    class="me-1"
                  />
                  <span class="text-body-1 mr-1">
                    <template v-if="field.field === 'status'">
                      <VChip
                        class="ml-2 font-weight-bold"
                        label
                        :color="getChipColor(field)"
                        density="compact"
                      >
                        {{ formatFieldValue(field) }}
                      </VChip>
                    </template>
                    <template v-else>
                      <span>
                        <VBadge
                          v-if="!field.disabled && field.changeRequested"
                          color="error"
                          size="small"
                          icon="tabler-exclamation-mark"
                        >
                          <VChip
                            class="ml-2 font-weight-bold"
                            label
                            :color="field.disabled ? 'primary' : 'error'"
                            density="compact"
                          >
                            {{ formatFieldValue(field) }}
                          </VChip>
                        </VBadge>
                        <VChip
                          v-else
                          class="ml-2 font-weight-bold"
                          label
                          :color="field.disabled ? 'primary' : 'error'"
                          density="compact"
                        >
                          {{ formatFieldValue(field) }}
                        </VChip>
                      </span>
                    </template>
                  </span>

                  <template v-if="props.editable || field.changeRequested">
                    <VIcon
                      v-if="field.disabled"
                      v-tooltip="'Editing this field is currently disabled'"
                      size="16"
                      icon="tabler-alert-triangle"
                      class="edit-icon ms-1"
                      color="error"
                    />
                    <VIcon
                      v-else
                      v-tooltip="`Edit ${field.name}`"
                      size="16"
                      icon="tabler-edit"
                      class="edit-icon ms-1"
                      color="primary"
                    />
                  </template>
                </VTemplate>

                <VTemplate v-else>
                  <VIcon
                    size="20"
                    :icon="getIconForField(field.field)"
                    class="me-1"
                  />
                  <span class="text-body-1 mr-1">
                    <template v-if="field.field === 'status'">
                      <VChip
                        class="ml-2 font-weight-bold"
                        label
                        density="compact"
                      >
                        {{ formatFieldValue(field) }}
                      </VChip>
                    </template>
                    <template v-else>
                      {{ formatFieldValue(field) }}
                    </template>
                  </span>

                  <template v-if="props.editable || field.changeRequested">
                    <VIcon
                      v-if="field.disabled"
                      v-tooltip="'Editing this field is currently disabled'"
                      size="16"
                      icon="tabler-alert-triangle"
                      class="edit-icon ms-1"
                      color="error"
                    />
                    <VIcon
                      v-else
                      v-tooltip="`Edit ${field.name}`"
                      size="16"
                      icon="tabler-edit"
                      class="edit-icon ms-1"
                      color="primary"
                    />
                  </template>
                </VTemplate>
              </span>

              <div
                v-if="displayableFields.length === 0 && processedParticipantData.length > 0"
                class="text-caption text-disabled pa-2"
              >
                No details to display based on current filters.
              </div>
              <div
                v-if="!loadingParticipant && processedParticipantData.length === 0"
                class="text-caption text-disabled pa-2"
              >
                No participant details available.
              </div>
            </div>
          </div>
        </div>
      </div>
    </VCardText>

    <EditCertificationFieldDialog
      v-if="editDialog && selectedField"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.name"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      entity-type="CertifiedPersonalInfo"
      :participant-name="fullName"
      :year="yearInReview"
      :type="ChangeType.CertifiedData"
      @close="closeEditDialog"
      @update="updateField"
    />

    <ApproveRejectCertFields
      v-model="showApprovalDialog"
      :field="fieldToApprove"
      :year="yearInReview"
      entity-type="certifiedPersonalInfo"
      :participant-name="fullName"
      :certified-data-id="certifiedDataId"
      @close="showApprovalDialog = false"
    />

    <RevertApproveRejectCertFields
      v-model="showRevertDialog"
      :field="fieldToRevert"
      :year="yearInReview"
      entity-type="certifiedPersonalInfo"
      :participant-name="fullName"
      :certified-data-id="certifiedDataId"
      @close="showRevertDialog = false"
    />
  </VCard>
</template>

<style scoped>
  .basic-information-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: white;
  }

  .info-item {
    position: relative;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .info-item.cursor-pointer:hover {
    background-color: rgba(var(--v-theme-primary), 0.08);
  }

  .edit-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .info-item.cursor-pointer:hover .edit-icon {
    opacity: 1;
  }

  .field-container {
    position: relative;
    padding: 2px 4px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .field-container:hover {
    background-color: rgba(var(--v-theme-surface), 0.04);
  }
</style>
