<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCertifiedData } from '@/composables/certified-data'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const router = useRouter()
const pensionStore = usePensionStore()

const participantId = computed(() => {
  return (router.currentRoute.value.params.id as string)
           || (pensionStore.activeParticipant?.id as string)
})

const { isAccountant } = useRoleAccess()

const {
  state: {
    participantCertifiedData,
    loadingParticipantCertifiedData,
    latestCertification,
    loadingLatestCertification,
  },
  actions: {
    refetchParticipantCertifiedData,
  },
} = useCertifiedData()

// Ensure we have the latest data for the active participant
onMounted(() => {
  if (participantId.value)
    refetchParticipantCertifiedData({ participantId: participantId.value })
})

const headers = [
  { title: 'Year', key: 'certificationYear', sortable: true },
  { title: 'Certified At', key: 'certifiedAt', sortable: true },
  { title: 'Status', key: 'status', sortable: true },
  { title: 'Notes', key: 'notes', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false },
]

// Helper function to determine certification status
const getCertificationStatus = (cert: any) => {
  // If certificationStatus is available, use it
  if (cert.certificationStatus)
    return cert.certificationStatus

  // If certifiedAt is not available, it's pending
  if (!cert.certifiedAt)
    return 'pending'

  // Check if all required fields are present
  const hasEmploymentInfo = !!cert.certifiedEmploymentInfo
  const hasPersonalInfo = !!cert.certifiedPersonalInfo
  const hasPensionInfo = !!cert.certifiedPensionInfo

  // If all required info is present, it's completed
  if (hasEmploymentInfo && hasPersonalInfo && hasPensionInfo)
    return 'completed'

  // Otherwise, it's started but not completed
  return 'started'
}

// Get certified data from the Pinia store or from the composable
const activeCertifications = computed(() => {
  // First try to get data from the Pinia store
  if (pensionStore.certifiedData && Array.isArray(pensionStore.certifiedData)) {
    return pensionStore.certifiedData
      .filter(cert => cert.participantId === participantId.value)
      .map(cert => ({
        id: cert.id,
        certificationYear: cert.certificationYear,
        certifiedAt: cert.certifiedAt
          ? new Date(cert.certifiedAt).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })
          : '',

        // status: getCertificationStatus(cert),
        status: getCertificationStatus(cert),
        notes: cert.notes || '',

        // Keep the original data for status determination
        certificationStatus: cert.certificationStatus,
        certifiedEmploymentInfo: cert.certifiedEmploymentInfo,
        certifiedPersonalInfo: cert.certifiedPersonalInfo,
        certifiedPensionInfo: cert.certifiedPensionInfo,
      }))
  }

  // Fallback to data from the composable
  if (!participantCertifiedData.value || participantCertifiedData.value.length === 0)
    return []

  return participantCertifiedData.value
    .filter(cert => cert.participantId === participantId.value)
    .map(cert => ({
      id: cert.id,
      certificationYear: cert.certificationYear,
      certifiedAt: cert.certifiedAt
        ? new Date(cert.certifiedAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })
        : '',
      status: getCertificationStatus(cert),
      notes: cert.notes || '',

      // Keep the original data for status determination
      certificationStatus: cert.certificationStatus,
      certifiedEmploymentInfo: cert.certifiedEmploymentInfo,
      certifiedPersonalInfo: cert.certifiedPersonalInfo,
      certifiedPensionInfo: cert.certifiedPensionInfo,
    }))
})

// Function to determine the color of the status chip
const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'started':
      return 'warning'
    case 'pending':
      return 'grey' // Using gray for pending status
    default:
      return '' // No color
  }
}

const viewCertification = (certificationId: string, certificationYear: number) => {
  pensionStore.certifiedDataId = certificationId
  router.push(`/certification-management/${participantId.value}/certification/${certificationId}/${certificationYear}`)
}

const reviewCertification = (certificationId: string, certificationYear: number, status: string) => {
  pensionStore.certifiedDataId = certificationId
  router.push(`/certification-management/${participantId.value}/certification/${certificationId}/${certificationYear}/started`)
}
</script>

<template>
  <div>
    <VCard
      v-if="loadingParticipantCertifiedData"
      class="mb-4"
    >
      <VCardText>
        <VProgressCircular indeterminate />
        <span class="ml-2">Loading certified data...</span>
      </VCardText>
    </VCard>

    <template v-else-if="activeCertifications.length > 0">
      <VCard>
        <VCardText>
          <div class="header-container">
            <div>
              <h1 class="text-h4">
                Certified Data List
              </h1>
              <p class="text-subtitle-1 text-grey-darken-1">
                List of all Certified Data for this participant
              </p>
            </div>
          </div>
        </VCardText>
        <VCardText>
          <VDataTable
            :headers="headers"
            :items="activeCertifications"
            :loading="loadingParticipantCertifiedData"
            class="elevation-1"
          >
            <template #item.status="{ item }">
              <VChip
                :color="getStatusColor(item.status)"
                size="small"
                class="text-capitalize"
              >
                {{ item.status }}
              </VChip>
            </template>
            <template #item.actions="{ item }">
              <VBtn
                v-if="!isAccountant"
                size="small"
                variant="text"
                color="primary"
                @click="viewCertification(item.id, item.certificationYear)"
              >
                View
              </VBtn>

              <VBtn
                v-else
                size="small"
                variant="text"
                color="primary"
                @click="reviewCertification(item.id, item.certificationYear, item.status)"
              >
                Review
              </VBtn>
            </template>
          </VDataTable>
        </VCardText>
      </VCard>
    </template>

    <VAlert
      v-else
      type="info"
      class="mt-4"
    >
      No certified data available for this participant.
    </VAlert>
  </div>
</template>
