<script setup lang="ts">
import CertifiedSalaryEntries from './SalaryPensionCertifiedSalaryEntries.vue'
import CertifiedPartTimePercentage from './SalaryPensionCertifiedPartTimePercentage.vue'
import SalaryEntityDialog from './CertifiedSalaryEntityDialog.vue'
import CertifiedPensionPrimaryCalc from './CertifiedPensionPrimaryCalc.vue'
import type { SalaryEntry } from '@/gql/graphql'

const props = defineProps({
  participant: {
    type: Object,
    default: null,
  },
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:salaryData', 'update:percentageData', 'update:pensionData'])

const dialogVisible = ref(false)
const selectedEntry = ref<SalaryEntry | null>(null)

const isCollapsed = ref(false)

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// Helper function to determine status color
const getStatusColor = (status: string) => {
  if (status === 'Current')
    return 'primary'
  if (status === 'Certified')
    return 'success'

  return 'default'
}

const salaryEntries = ref<SalaryEntry[]>([])
const loadingSalaryEntries = ref(false)

const fetchSalaryEntries = () => {
  if (!props.participant || !props.participant.employmentInfo)
    return

  loadingSalaryEntries.value = true

  if (props.participant.employmentInfo.salaryEntries)
    salaryEntries.value = [...props.participant.employmentInfo.salaryEntries].sort((a, b) => b.year - a.year)
  else
    salaryEntries.value = []

  loadingSalaryEntries.value = false
}

onMounted(() => {
  if (props.participant)
    fetchSalaryEntries()
})

watch(() => props.participant, newVal => {
  if (newVal)
    fetchSalaryEntries()
}, { deep: true })
</script>

<template>
  <VCard
    class="salary-pension-container"
    elevation="0"
  >
    <VCardTitle class="header-container d-flex justify-space-between align-center">
      <div>
        <h2 class="text-h4 font-weight-medium">
          Salary & Pension Base
        </h2>
        <div class="text-caption text-medium-emphasis">
          Year: {{ props.year }}
        </div>
      </div>
      <VBtn
        variant="outlined"
        color="primary"
        size="small"
        :prepend-icon="isCollapsed ? 'tabler-chevron-down' : 'tabler-chevron-up'"
        @click="toggleCollapse"
      >
        {{ isCollapsed ? 'Expand' : 'Collapse' }}
      </VBtn>
    </VCardTitle>

    <VCardText v-if="!isCollapsed">
      <!-- Salary Information Section -->
      <div class="section-container mb-6">
        <div class="section-header d-flex justify-space-between align-start mb-4">
          <div>
            <h3 class="text-h6 font-weight-medium">
              Salary Information
            </h3>
            <div class="text-caption text-medium-emphasis">
              Overview of annual salary and part-time percentage
            </div>
          </div>
        </div>

        <!-- Salary Entries Component -->
        <CertifiedSalaryEntries
          v-if="participant && participant.employmentInfo"
          :participant-id="participant.id"
          :employment-info-id="participant.employmentInfo.id"
          :can-edit="props.editable"
          :year="props.year"
          class="mb-6"
          @refresh="fetchSalaryEntries"
        />

        <!-- Part-time Percentage Component -->
        <CertifiedPartTimePercentage
          v-if="participant && participant.employmentInfo && salaryEntries.length > 0"
          :entries="salaryEntries"
          :participant-name="`${participant?.personalInfo?.firstName} ${participant?.personalInfo?.lastName}`"
          :employment-info-id="participant.employmentInfo.id"
          :loading="loadingSalaryEntries"
          :can-edit="props.editable"
          :year="props.year"
          class="mb-6"
          @refresh="fetchSalaryEntries"
        />
      </div>

      <VDivider class="my-6" />

      <!-- Pension Base Calculation Section -->
      <CertifiedPensionPrimaryCalc
        :participant="participant"
        :year="props.year"
        :editable="props.editable"
      />
    </VCardText>

    <SalaryEntityDialog
      v-model="dialogVisible"
      :edit-mode="false"
      path="new"
      entity-id="new"
      entity-type="salaryEntry"
      :entry="selectedEntry as any"
      form-type="new"
      year="2026"
      @refresh="$emit('refresh')"
    />
  </VCard>
</template>

<style scoped>
  .salary-pension-container {
    border-radius: 8px;
  }

  .header-container {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 20px
  }

  .section-header {
    margin-bottom: 16px;
  }

  .salary-table,
  .percentage-table,
  .pension-table {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
  }

  .salary-table th,
  .percentage-table th,
  .pension-table th {
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }

  @media (max-width: 960px) {
    .pl-md-3, .pr-md-3 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
</style>
