<script setup lang="ts">
import { useRoute } from 'vue-router'
import { useQuery } from '@vue/apollo-composable'
import { computed } from 'vue'
import CertifiedSalaryPension from './CertifiedSalaryPension.vue'
import { GET_PARTICIPANT_BY_ID } from '@/api/graphql/queries/participantQueries'
import AppLoadingIndicator from '@/components/AppLoadingIndicator.vue'

const props = defineProps({
  year: {
    type: Number,
    default: 2024,
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

const route = useRoute()
const participantId = route.params.id as string

const { result, loading } = useQuery(
  GET_PARTICIPANT_BY_ID,
  { id: participantId },
  { fetchPolicy: 'network-only' },
)

const participant = computed(() => result.value?.getParticipantById || null)
</script>

<template>
  <VCard class="mt-6">
    <VRow no-gutters>
      <VCol cols="12">
        <VCardText>
          <VWindow class="disable-tab-transition">
            <VWindowItem>
              <AppLoadingIndicator v-if="loading" />
              <CertifiedSalaryPension
                v-else
                :year="props.year"
                :participant="participant"
                :editable="props.editable"
              />
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>

<style scoped>
  .stepper-icon-step-bg {
    margin-bottom: 16px;
  }
</style>
