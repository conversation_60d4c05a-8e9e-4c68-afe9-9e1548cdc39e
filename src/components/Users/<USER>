<script setup lang="ts">
import useUsers from '@/composables/users/useUsers'
import { userTableHeaders } from '@/components/Users/<USER>'
import { useUserStore } from '@/stores/users/userStore'
import type { UserInput } from '@/types/user.types'
import { confirmUserDeleteAlert } from '@/composables/useSweetAlert'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const userStore = useUserStore()
const { canManageUsers } = useRoleAccess()

const { state: { loadingDelete }, actions: { handleDeleteUser } } = useUsers()

const editUsers = (user: UserInput) => {
  userStore.showUserFormDialog()
  userStore.setUserFormDetails(user)
}

const showDialog = () => {
  userStore.resetUserFormDetails()
  userStore.showUserFormDialog()
}

const deleteUser = async (user: UserInput) => {
  try {
    const confirmDelete = await confirmUserDeleteAlert(true)
    if (confirmDelete.isConfirmed) {
      userStore.setUserFormDetails(user)
      await handleDeleteUser()
    }
  }
  catch (error) {
    console.error('Error deleting user:', error)
  }
}

const { state: { userList } } = useUsers()

const sortBy = ref([{ key: 'email', order: 'asc' }])
</script>

<template>
  <UserDialog />
  <VCard>
    <template #title>
      <VRow class="px-2">
        <VCol
          cols="6"
          class="text-h4 font-weight-medium"
        >
          Manage Users
        </VCol>
        <VCol
          cols="6"
          class="d-flex justify-end"
        >
          <VBtn
            v-if="canManageUsers"
            size="small"
            variant="outlined"
            prepend-icon="tabler-plus"
            @click="showDialog"
          >
            Add User
          </VBtn>
        </VCol>
      </VRow>
    </template>
    <VDivider />
    <VRow>
      <VCol>
        <VDataTable
          v-model:sort-by="sortBy"
          :headers="userTableHeaders"
          :items="userList"
          :items-per-page="5"
          :search-props="{ outlined: true }"
        >
          <template #item.actions="{ item }">
            <VBtn
              v-if="canManageUsers"
              icon
              size="small"
              class="mx-2"
              variant="text"
              @click="editUsers(item)"
            >
              <VIcon icon="tabler-edit" />
            </VBtn>
            <VBtn
              v-if="canManageUsers"
              icon
              size="small"
              variant="text"
              @click="deleteUser(item)"
            >
              <VIcon icon="tabler-trash" />
            </VBtn>
          </template>
        </VDataTable>
      </VCol>
    </VRow>
  </VCard>
</template>
