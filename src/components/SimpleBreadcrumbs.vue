<script setup lang="ts">
interface BreadcrumbItem {
  title: string
  to?: string
  disabled?: boolean
  icon?: string
}

interface Props {
  items: BreadcrumbItem[]
  divider?: string
}

const props = withDefaults(defineProps<Props>(), {
  divider: 'tabler-chevron-right',
})
</script>

<template>
  <VBreadcrumbs
    v-if="items.length > 0"
    :items="items"
    class="px-0 mb-4"
    density="compact"
  >
    <template #divider>
      <VIcon
        :icon="divider"
        size="16"
        class="text-medium-emphasis"
      />
    </template>

    <template #item="{ item }">
      <RouterLink
        v-if="item.to && !item.disabled"
        :to="item.to"
        class="d-flex align-center text-decoration-none text-primary"
      >
        <VIcon
          v-if="item.icon"
          :icon="item.icon"
          size="16"
          class="me-2"
        />
        <span>{{ item.title }}</span>
      </RouterLink>
      <div
        v-else
        class="d-flex align-center"
        :class="item.disabled ? 'text-disabled' : 'text-medium-emphasis'"
      >
        <VIcon
          v-if="item.icon"
          :icon="item.icon"
          size="16"
          class="me-2"
        />
        <span>{{ item.title }}</span>
      </div>
    </template>
  </VBreadcrumbs>
</template>
