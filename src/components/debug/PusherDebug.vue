<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'
import { useAuthStore } from '@/stores/auth/authStore'
import { pusherService } from '@/services/pusher'

const authStore = useAuthStore()
const { isConnected, connectionState, forceReconnect: composableReconnect } = usePusherNotifications()

const testing = ref(false)
const reconnecting = ref(false)
const logs = ref('')

const userId = computed(() => authStore.claims.pensionUserId)
const channelName = computed(() => userId.value ? `user-${userId.value}` : null)

const connectionColor = computed(() => {
  switch (connectionState.value) {
  case 'connected': return 'success'
  case 'connecting': return 'warning'
  case 'disconnected': return 'error'
  case 'unavailable': return 'error'
  case 'failed': return 'error'
  default: return 'info'
  }
})

const connectionIcon = computed(() => {
  switch (connectionState.value) {
  case 'connected': return 'tabler-check'
  case 'connecting': return 'tabler-loader'
  case 'disconnected': return 'tabler-x'
  case 'unavailable': return 'tabler-alert-triangle'
  case 'failed': return 'tabler-alert-circle'
  default: return 'tabler-help'
  }
})

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()

  logs.value += `[${timestamp}] ${message}\n`
}

const testConnection = () => {
  testing.value = true
  addLog('Testing connection...')

  try {
    const state = pusherService.getCurrentConnectionState()

    addLog(`Current connection state: ${state}`)

    if (userId.value) {
      addLog(`User ID available: ${userId.value}`)
      addLog(`Channel name: user-${userId.value}`)
    }
    else {
      addLog('No user ID available')
    }

    addLog('Using public channels (no authentication required)')
  }
  catch (error) {
    addLog(`Error during test: ${error}`)
  }
  finally {
    testing.value = false
  }
}

const forceReconnect = () => {
  reconnecting.value = true
  addLog('Forcing reconnection...')

  try {
    composableReconnect()
    addLog('Reconnection initiated')
  }
  catch (error) {
    addLog(`Error during reconnection: ${error}`)
  }
  finally {
    reconnecting.value = false
  }
}

const clearLogs = () => {
  logs.value = ''
  addLog('Logs cleared')
}

onMounted(() => {
  addLog('Pusher Debug component mounted')
  testConnection()
})
</script>

<template>
  <VCard class="ma-4">
    <VCardTitle>Pusher Connection Debug</VCardTitle>
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          md="6"
        >
          <VAlert
            :color="connectionColor"
            :icon="connectionIcon"
            class="mb-4"
          >
            Connection State: {{ connectionState }}
          </VAlert>

          <VAlert
            :color="isConnected ? 'success' : 'error'"
            :icon="isConnected ? 'tabler-check' : 'tabler-x'"
            class="mb-4"
          >
            Is Connected: {{ isConnected }}
          </VAlert>

          <div class="mb-4">
            <strong>User ID:</strong> {{ userId || 'Not available' }}
          </div>

          <div class="mb-4">
            <strong>Channel:</strong> {{ channelName || 'Not subscribed' }}
          </div>

          <div class="mb-4">
            <strong>Channel Type:</strong> Public (no auth required)
          </div>
        </VCol>

        <VCol
          cols="12"
          md="6"
        >
          <VBtn
            color="primary"
            class="mb-2 mr-2"
            :loading="testing"
            @click="testConnection"
          >
            Test Connection
          </VBtn>

          <VBtn
            color="warning"
            class="mb-2 mr-2"
            :loading="reconnecting"
            @click="forceReconnect"
          >
            Force Reconnect
          </VBtn>

          <VBtn
            color="success"
            class="mb-2"
            @click="clearLogs"
          >
            Clear Logs
          </VBtn>

          <VTextarea
            v-model="logs"
            label="Connection Logs"
            rows="10"
            readonly
            class="mt-4"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>
