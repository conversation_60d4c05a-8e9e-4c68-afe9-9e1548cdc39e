<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import { ChangeType } from '@/gql/graphql'

const props = defineProps({
  field: { type: String, required: true },
  fieldName: { type: String, required: true },
  currentValue: { type: String || Number, required: true },
  year: { type: Number, required: true },
  modelValue: { type: Boolean, required: true },
  entityType: { type: String, required: true },
  entityId: { type: String, required: true },
  type: { type: ChangeType, required: true },
  participantName: { type: String, required: false },
})

const emit = defineEmits(['update:modelValue', 'save', 'close', 'update'])

const { state: { loadingCreateChangeProposal }, actions: { createChangeProposal } } = useChangeProposal()
const { actions: { validatePartnerDates } } = useParticipants()

const appStore = useAppStore()
const newValue = ref(props.currentValue)

// Initialize date picker value as Date object
const datePickerValue = ref(new Date(props.year - 1, 11, 31)) // Month is 0-indexed

// Display format (MM/DD/YYYY)
const effectiveDate = ref(`12/31/${props.year - 1}`)

// Menu state for date picker
const dateMenu = ref(false)

// Convert Date object to display format
const formatDateForDisplay = (date: Date) => {
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const year = date.getFullYear()

  return `${month}/${day}/${year}`
}

// Convert display format to Date object
const formatDateForPicker = (dateString: string) => {
  const [month, day, year] = dateString.split('/')

  return new Date(Number.parseInt(year), Number.parseInt(month) - 1, Number.parseInt(day))
}

// Watch for date picker changes
watch(datePickerValue, newDate => {
  if (newDate instanceof Date)
    effectiveDate.value = formatDateForDisplay(newDate)
})

const isValueUnchanged = computed(() => {
  return newValue.value === props.currentValue
})

const isPercentageField = computed(() => {
  return props.type === ChangeType.Parameters && (props.field === 'accrualPercentage'
    || props.field === 'voluntaryContributionInterestRate' || props.field === 'partnersPensionPercentage')
})

const isPartnerDateField = computed(() => {
  return props.entityType === 'PartnerInfo' && (props.field === 'startDate' || props.field === 'endDate')
})

const partnerDateValidation = computed(() => {
  if (!isPartnerDateField.value || !newValue.value)
    return { isValid: true }

  // For partner date validation, we need to consider both startDate and endDate
  // If editing startDate, use current endDate; if editing endDate, use current startDate
  const startDateToValidate = props.field === 'startDate' ? newValue.value : null
  const endDateToValidate = props.field === 'endDate' ? newValue.value : null

  // We need to get the other date from the current partner data
  // This is a simplified approach - in a real scenario, you might need to pass more context
  return validatePartnerDates(startDateToValidate, endDateToValidate, props.entityId)
})

const rules = computed(() => {
  const baseRules = [(v: any) => !!v || `${props.field} is required`]

  if (isPercentageField.value) {
    baseRules.push((v: any) => !isNaN(Number.parseFloat(v)) || 'Must be a number')
    baseRules.push((v: any) => Number.parseFloat(v) <= 1 || 'Value must be less than or equal to 1')
  }

  if (isPartnerDateField.value) {
    baseRules.push((v: any) => {
      const validation = partnerDateValidation.value

      return validation.isValid || validation.errorMessage || 'Invalid date range'
    })
  }

  return baseRules
})

const saveChanges = async () => {
  // Check partner date validation before saving
  if (isPartnerDateField.value) {
    const validation = partnerDateValidation.value
    if (!validation.isValid) {
      appStore.showSnack(validation.errorMessage || 'Partner dates overlap with existing partner', 'error')

      return
    }
  }

  try {
    await createChangeProposal({
      participantName: props.participantName,
      entityId: props.entityId,
      entityType: props.entityType,
      path: props.field,
      newValue: newValue.value,
      oldValue: props.currentValue,
      effectiveDate: effectiveDate.value,
      type: props.type,
    })
    appStore.showSnack('Change proposal created successfully')
    emit('update')
    emit('close')
  }
  catch (error) {
    // Handle backend validation errors
    const errorMessage = error instanceof Error ? error.message : 'An error occurred'
    if (errorMessage.includes('overlap'))
      appStore.showSnack(errorMessage, 'error')
    else
      appStore.showSnack('Failed to create change proposal', 'error')
  }
}

const closeDialog = () => {
  emit('close')
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 500"
    :model-value="modelValue"
    @update:model-value="(value) => emit('update:modelValue', value)"
  >
    <VCard class="pa-4">
      <VCardTitle class="text-h5 pb-2">
        Edit for {{ entityType }}
      </VCardTitle>

      <VCardText>
        <div class="text-subtitle-1 font-weight-bold mb-1">
          Current Value
        </div>
        <VTextField
          :value="currentValue"
          variant="solo-filled"
          hide-details="auto"
          density="comfortable"
          class="mb-4"
          readonly
        />

        <div class="text-subtitle-1 font-weight-bold mb-1">
          New Value
        </div>
        <VTextField
          v-model="newValue"
          :placeholder="currentValue"
          :value="newValue"
          variant="outlined"
          hide-details="auto"
          density="comfortable"
          class="mb-1"
          :rules="rules"
          :hint="isPercentageField ? 'Enter value as a decimal (e.g., 0.20 for 20%)' : ''"
          persistent-hint
        />

        <div
          v-if="isValueUnchanged"
          class="text-warning mb-4"
        >
          New value is the same as current value
        </div>

        <div
          v-if="isPartnerDateField && !partnerDateValidation.isValid"
          class="text-error mb-4"
        >
          {{ partnerDateValidation.errorMessage }}
        </div>

        <div class="text-subtitle-1 font-weight-medium mt-4 mb-1">
          Effective Date
        </div>
        <div class="d-flex align-center">
          <VMenu
            v-model="dateMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template #activator="{ props }">
              <VTextField
                v-model="effectiveDate"
                variant="outlined"
                hide-details
                density="comfortable"
                class="flex-grow-1"
                readonly
                v-bind="props"
                prepend-inner-icon="tabler-calendar"
                placeholder="MM/DD/YYYY"
              />
            </template>
            <VDatePicker
              v-model="datePickerValue"
              color="primary"
              @update:model-value="dateMenu = false"
            />
          </VMenu>
          <div
            v-if="entityType === 'changeProposal'"
            class="ml-3 d-flex align-center text-grey"
          >
            <VIcon
              icon="tabler-info-circle"
              class="mr-1"
              size="small"
            />
            <span>Default: January 1st of the year</span>
          </div>
        </div>
      </VCardText>

      <VCardActions class="pb-4 px-4">
        <VSpacer />
        <VBtn
          color="secondary"
          min-width="100"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          variant="elevated"
          color="primary"
          :loading="loadingCreateChangeProposal"
          min-width="100"
          :disabled="!newValue || isValueUnchanged || loadingCreateChangeProposal || (isPartnerDateField && !partnerDateValidation.isValid)"
          class="ml-3"
          @click="saveChanges"
        >
          Save
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
