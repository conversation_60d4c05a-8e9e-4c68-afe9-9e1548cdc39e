<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

interface Props {
  requiredPermission?: string
  requiredRoute?: string
}

const props = withDefaults(defineProps<Props>(), {
  requiredPermission: '',
  requiredRoute: '',
})

const { currentRole } = useRoleAccess()
const router = useRouter()

const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'success'
    case 'reviewer':
      return 'primary'
    case 'editor':
      return 'info'
    case 'accountant':
      return 'warning'
    default:
      return 'secondary'
  }
}

const goBack = () => {
  router.go(-1)
}

const goToDashboard = () => {
  router.push({ name: 'root' })
}
</script>

<template>
  <div class="permission-restricted-container">
    <VCard class="permission-restricted-card">
      <VCardText class="text-center py-12">
        <div class="permission-lock-container mb-6">
          <div class="permission-lock-circle">
            <VIcon
              icon="tabler-shield-lock"
              size="60"
              color="error"
              class="lock-icon"
            />
          </div>
        </div>

        <h2 class="text-h4 mb-4">
          Access Restricted
        </h2>

        <p class="text-body-1 mb-6 text-medium-emphasis">
          You don't have permission to access this page.
        </p>

        <div class="permission-details mb-6">
          <VChip
            :color="getRoleColor(currentRole)"
            size="small"
            class="mb-2"
          >
            Current Role: {{ currentRole }}
          </VChip>

          <p class="text-body-2 text-medium-emphasis">
            Contact your administrator to request access to this resource.
          </p>
        </div>

        <div class="permission-actions">
          <VBtn
            variant="elevated"
            color="primary"
            @click="goBack"
          >
            <VIcon
              icon="tabler-arrow-left"
              class="me-2"
            />
            Go Back
          </VBtn>

          <VBtn
            variant="outlined"
            color="primary"
            class="ms-4"
            @click="goToDashboard"
          >
            <VIcon
              icon="tabler-home"
              class="me-2"
            />
            Dashboard
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
.permission-restricted-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

.permission-restricted-card {
  max-width: 500px;
  width: 100%;
}

.permission-restricted-icon {
  display: flex;
  justify-content: center;
}

.permission-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.permission-actions {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
}

@media (max-width: 600px) {
  .permission-actions {
    flex-direction: column;
    align-items: center;
  }

  .permission-actions .v-btn {
    width: 100%;
    max-width: 200px;
  }
}

.permission-lock-container {
  position: relative;
  margin: 0 auto;
  width: fit-content;
}

.permission-lock-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: rgba(var(--v-theme-error), 0.1);
  margin: 0 auto;
}

.lock-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.permission-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

/* Animation for the lock icon */
.permission-lock-circle {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--v-theme-error), 0.2);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(var(--v-theme-error), 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--v-theme-error), 0);
  }
}
</style>
