<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { formatDistance } from 'date-fns'
import { useAppStore } from '@/stores/app/appStore'
import { useNotifications } from '@/composables/notifications/useNotifications'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'

const searchQuery = ref('')

const {
  state: {
    formattedNotifications,
    loadingUserNotifications,
    loadingOperations,
  },
  actions: {
    handleMarkAsRead,
    handleMarkAllAsRead,
    refreshNotifications,
  },
} = useNotifications()

const appStore = useAppStore()

// Get Pusher functionality
const { subscribeToNotifications } = usePusherNotifications()

const notificationsHeader = computed(() => {
  return [
    {
      title: 'Date',
      key: 'createdAt',
      sortable: true,
      width: '140px',
    },
    {
      title: 'Type',
      key: 'type',
      sortable: true,
      width: '140px',
    },
    {
      title: 'Message',
      key: 'message',
      sortable: false,
    },
    {
      title: 'From',
      key: 'createdBy',
      sortable: true,
      width: '160px',
    },
    {
      title: 'Status',
      key: 'read',
      sortable: true,
      width: '120px',
    },
    {
      title: 'Actions',
      key: 'actions',
      sortable: false,
      width: '150px',
    },
  ]
})

const handleMarkAllRead = async () => {
  await handleMarkAllAsRead()
  await refreshNotifications()
  appStore.showSnack('All notifications marked as read')
}

const onMarkAsRead = async (notificationId: string) => {
  await handleMarkAsRead(notificationId)
  await refreshNotifications()
}

const filteredNotifications = computed(() => {
  if (!searchQuery.value)
    return formattedNotifications.value

  const query = searchQuery.value.toLowerCase()

  return formattedNotifications.value.filter((notification: any) =>
    notification.message.toLowerCase().includes(query)
      || notification.creatorName.toLowerCase().includes(query)
      || notification.typeDisplayName.toLowerCase().includes(query),
  )
})

const itemsPerPage = ref(10)
const page = ref(1)

const totalNotifications = computed(() =>
  filteredNotifications.value ? filteredNotifications.value.length : 0,
)

const paginatedNotifications = computed(() => {
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return filteredNotifications.value.slice(start, end)
})

const formatDate = (date: string) => {
  return formatDistance(new Date(date), new Date(), { addSuffix: true })
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'SYSTEM': return 'tabler-alert-circle'
    case 'MESSAGE': return 'tabler-message'
    case 'CHANGE_PROPOSAL_REJECTED': return 'tabler-x'
    case 'CHANGE_PROPOSAL_APPROVED': return 'tabler-check'
    case 'TASK': return 'tabler-checklist'
    case 'WARNING': return 'tabler-alert-triangle'
    case 'INFO': return 'tabler-info-circle'
    default: return 'tabler-bell'
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'SYSTEM': return 'warning'
    case 'CHANGE_PROPOSAL_CREATED': return 'info'
    case 'CHANGE_PROPOSAL_REJECTED': return 'error'
    case 'CHANGE_PROPOSAL_APPROVED': return 'success'
    case 'TASK': return 'primary'
    case 'WARNING': return 'orange'
    case 'INFO': return 'indigo'
    default: return 'secondary'
  }
}

onMounted(() => {
  refreshNotifications()

  // Subscribe to real-time notifications via Pusher
  subscribeToNotifications(notification => {
    // Refresh notifications when a new one is received
    refreshNotifications()
  })
})
</script>

<template>
  <VCard
    elevation="20"
    title="Notifications"
  >
    <template #append>
      <VBtn
        prepend-icon="tabler-eye-check"
        size="small"
        :disabled="loadingOperations || formattedNotifications.length === 0 || !formattedNotifications.some((n: any) => !n.read)"
        variant="tonal"
        color="primary"
        @click="handleMarkAllRead"
      >
        Mark all as read
      </VBtn>
    </template>

    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Search notifications..."
          density="compact"
          prepend-inner-icon="tabler-search"
          class="me-3"
          clearable
          hide-details
          single-line
        />
      </div>
    </VCardText>

    <VDataTable
      :loading="loadingUserNotifications || loadingOperations"
      :headers="notificationsHeader"
      :items="paginatedNotifications"
      :item-value="item => item.id"
      class="px-2 notification-table"
      hover
      :items-per-page="itemsPerPage"
    >
      <template #loading>
        <div class="text-center py-4">
          <VProgressCircular
            indeterminate
            color="primary"
          />
          <p class="mt-2">
            Loading notifications...
          </p>
        </div>
      </template>

      <template #no-data>
        <div class="text-center py-4">
          <VIcon
            icon="tabler-bell-off"
            size="48"
            class="text-disabled mb-2"
          />
          <p class="text-disabled">
            No notifications available
          </p>
        </div>
      </template>

      <!-- Type column -->
      <template #[`item.type`]="{ item }">
        <VChip
          :color="getTypeColor(item.type)"
          :prepend-icon="getTypeIcon(item.type)"
          size="small"
          class="text-capitalize font-weight-medium"
          variant="flat"
        >
          {{ item.typeDisplayName }}
        </VChip>
      </template>

      <!-- Created At column -->
      <template #[`item.createdAt`]="{ item }">
        <div class="d-flex align-center">
          <VIcon
            icon="tabler-clock"
            size="18"
            class="me-1 text-disabled"
          />
          <span class="text-sm">{{ formatDate(item.createdAt) }}</span>
        </div>
      </template>

      <!-- Message column -->
      <template #[`item.message`]="{ item }">
        <div
          class="message-content"
          :class="{ 'font-weight-bold': !item.read }"
        >
          {{ item.message }}
        </div>
      </template>

      <!-- Created By column -->
      <template #[`item.createdBy`]="{ item }">
        <div class="d-flex align-center">
          <VIcon
            icon="tabler-user"
            size="18"
            class="me-1 text-disabled"
          />
          <span>{{ item.creatorName }}</span>
        </div>
      </template>

      <!-- Read Status column -->
      <template #[`item.read`]="{ item }">
        <VChip
          :prepend-icon="item.read ? 'tabler-eye-check' : 'tabler-eye'"
          :color="item.read ? 'secondary' : 'primary'"
          size="small"
          variant="flat"
          class="font-weight-medium"
        >
          {{ item.read ? 'Read' : 'Unread' }}
        </VChip>
      </template>

      <!-- Actions column -->
      <template #[`item.actions`]="{ item }">
        <VBtn
          :disabled="item.read || loadingOperations"
          :prepend-icon="item.read ? 'tabler-eye-check' : 'tabler-eye'"
          :color="!item.read ? 'primary' : 'grey'"
          density="compact"
          variant="tonal"
          size="small"
          @click="() => onMarkAsRead(item.id)"
        >
          {{ item.read ? 'Viewed' : 'Mark as read' }}
        </VBtn>
      </template>

      <template #bottom>
        <VDivider />
        <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3">
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalNotifications) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalNotifications / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalNotifications / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
                prepend-icon="tabler-chevron-left"
              >
                Previous
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
                append-icon="tabler-chevron-right"
              >
                Next
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>

    <VDivider />
  </VCard>
</template>

<style lang="scss" scoped>
  .notification-table {
    .v-data-table-header {
      th {
        font-weight: 600;
        color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity)) !important;
      }
    }

    .v-data-table__tr {
      .message-content {
        white-space: normal;
        line-height: 1.4;
      }

      &:not(:hover) {
        .v-btn--disabled {
          opacity: 0;
        }
      }
    }

    .v-chip {
      .v-icon {
        opacity: 1 !important;
      }
    }
  }

  .v-card {
    .v-card-title {
      font-weight: 600;
      padding-bottom: 0;
    }

    .v-card-text {
      padding-top: 12px;
      padding-bottom: 12px;
    }
  }
</style>
