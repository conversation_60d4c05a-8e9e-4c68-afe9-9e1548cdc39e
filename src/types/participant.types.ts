// src/types/participant.types.ts

export interface ParticipantChange {
  id: string
  participant: string
  field: string
  currentValue: string | number | object | null
  newValue: string | number | object | null
  effectiveDate: string
  proposedBy: string
  status: 'pending' | 'approved' | 'rejected'
}

export interface SalaryEntry {
  id: string
  year: number
  amount: number
  partTimePercentage: number
  employmentInfoId?: string
  pendingChanges?: string[]
  startDate?: string
}

export interface EmploymentInfo {
  id: string
  employeeId?: string
  department?: string
  position?: string
  regNum?: number
  havNum?: number
  startDate?: string
  status?: string
  salaryEntries?: SalaryEntry[]
}

export interface PensionInfo {
  id: string
  code: number
  codeDescription: string
  pensionBase: number
  pendingChanges: string[]
}

export interface PersonalInfo {
  id: string
  firstName: string
  lastName: string
  email: string
  birthDay: number
  birthMonth: number
  birthYear: number
  address: any
  partnerInfo: any[]
  children: any[]
}

export interface Participant {
  id: string
  personalInfo: PersonalInfo
  employmentInfo?: EmploymentInfo
  pensionInfo?: PensionInfo
  status: string
  approvalStatus: ApprovalStatus
  lastModified: string
}

export interface ParticipantDisplay extends Participant {
  fullName: string
  birthDate: string
  id: string
  firstName: string
  lastName: string
  email: string
  birthDay: number
  birthMonth: number
  birthYear: number
}

export interface PensionItem {
  id: string
  year: string
  grossMonthlySalary: string
  annualMultiplier: string
  grossAnnualSalary: string
  accrualPercentage: number
  opteAccrualToReferenceDate: string
  wpteAccrualToReferenceDate: string
  opteAccrualAfterReferenceDate: string
  offset: string
  pensionBase: string
}

export interface PensionAccrualSingle {
  id: string
  year: string
  grossMonthlySalary: string
  annualMultiplier: string
  grossAnnualSalary: string
  accrualPercentage: string
  pensionBase: string
}

export interface PensionAccrualItem {
  leftColumn: PensionItem
  rightColumn: PensionItem
  middleColumn: PensionItem
}

export interface SplitDate {
  year: number
  month: number
  day: number
}
