interface User {
  __typename: string
  id: string
  firstname: string
  lastname: string
  email: string
}

interface ChangeData {
  __typename: string
  id: string
  path: string
  newValue: any
  oldValue: any
}

interface ChangeProposal {
  __typename: string
  id: string
  createdAt: string
  createdBy: User
  effectiveDate: string
  entityId: string
  entityType: string
  status: string
  reviewComments: string | null
  reviewedAt: string | null
  reviewedBy: User | null
  updatedAt: string
  changes: ChangeData[]
}

export interface ParticipantChange {
  id: string
  key: string
  participant: string
  field: string
  currentValue: string
  newValue: string
  effectiveDate: string
  proposedBy: string
  reviewedAt: string
  reviewedBy: string
  reviewComments: string
  status: string
  rawData?: {
    proposal: ChangeProposal
    change: {
      path: string
      oldValue: any
      newValue: any
    }
  }
}
