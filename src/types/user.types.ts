export interface User {
  id: string
  firebaseUid: string
  createdAt: string
  updatedAt: string
  email: string
  firstname: string | null
  lastname: string | null
  lastLogin: string | null
  roleId: string
}

export interface UserInput {
  id?: string
  email: string
  firebaseUid: string
  firstname?: string | null
  lastname?: string | null
  roleId?: string
  role?: Role
}

export interface UpdateUserInput extends Partial<UserInput> {
  id: string
}

export interface Role {
  id: string
  name: string
}

export type UserTableItem = User & {
  fullName: string
  formattedLastLogin: string
}
