import { provideApolloClient, useMutation, useQuery } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import type { Role } from '@/types/user.types'
import { GET_ALL_ROLES, GET_ALL_USERS, GET_USER_BY_ID } from '@/api/graphql/queries/userQueries'
import { CREATE_USER, DELETE_USER, UPDATE_USER } from '@/api/graphql/mutations/userMutations'

export const useUserGraph = () => {
  provideApolloClient(apolloClient)

  const {
    result: allUsersResult,
    loading: loadingUsers,
    refetch: refetchUsers,
    error: usersError,
  } = useQuery(GET_ALL_USERS)

  const userList = computed(() => allUsersResult.value?.getAllUsers || [])

  const {
    result: allRolesResult,
    loading: loadingRoles,
    refetch: refetchRoles,
    error: rolesError,
  } = useQuery(GET_ALL_ROLES)

  const rolesList = computed<Role[]>(() => allRolesResult.value?.roles.map((role: { id: string; name: string }) => {
    return {
      id: role.id,
      name: role.name,
    }
  }) || [])

  const getUserById = (id: string) => {
    const { result, loading, error } = useQuery(
      GET_USER_BY_ID,
      { id },
    )

    return {
      result,
      loading,
      error,
    }
  }

  const {
    mutate: createUserMutation,
    loading: loadingCreate,
  } = useMutation(CREATE_USER, {
    refetchQueries: [GET_ALL_USERS],
  })

  const {
    mutate: updateUserMutation,
    loading: loadingUpdate,
  } = useMutation(UPDATE_USER, {
    refetchQueries: [GET_ALL_USERS],
  })

  const {
    mutate: deleteUserMutation,
    loading: loadingDelete,
  } = useMutation(DELETE_USER, {
    refetchQueries: [GET_ALL_USERS],
  })

  return {
    state: {
      userList,
      rolesList,
      loadingUsers,
      loadingRoles,
      loadingCreate,
      loadingUpdate,
      loadingDelete,
      usersError,
      rolesError,
    },
    actions: {
      refetchUsers,
      refetchRoles,
      getUserById,
      createUserMutation,
      updateUserMutation,
      deleteUserMutation,
    },
  }
}
