import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import { PASSWORD_RESET } from '@/api/graphql/queries/authQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import {
  REGISTER_USER,
  SET_CUSTOM_CLAIMS,
} from '@/api/graphql/mutations/userMutations'
import {
  CREATE_COMPANY_USER,
  CREATE_FIREBASE_AND_COMPANY_USER,
} from '@/api/graphql/mutations/companyUserMutations'

export const useAuthGraph = () => {
  provideApolloClient(apolloClient)

  const {
    mutate: registerUser,
    loading: registerLoading,
    onDone: registerDone,
  } = useMutation(REGISTER_USER)

  const {
    mutate: resetPassword,
    loading: resetPasswordLoading,
    onDone: resetPasswordDone,
  } = useMutation(PASSWORD_RESET)

  const {
    mutate: saveCompanyUser,
    loading: companyUserLoading,
    onDone: companyUserDone,
  } = useMutation(CREATE_COMPANY_USER)

  const {
    mutate: createFirebaseBridgeUser,
    loading: firebaseBridgeUserLoading,
  } = useMutation(CREATE_FIREBASE_AND_COMPANY_USER)

  const { mutate: setCustomClaims } = useMutation(SET_CUSTOM_CLAIMS)

  return {
    state: {
      resetPasswordLoading,
      registerLoading,
      companyUserLoading,
      firebaseBridgeUserLoading,
    },
    actions: {
      resetPassword,
      registerUser,
      setCustomClaims,
      registerDone,
      saveCompanyUser,
      companyUserDone,
      resetPasswordDone,
      createFirebaseBridgeUser,
    },
  }
}
