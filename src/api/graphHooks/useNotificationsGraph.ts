import { computed } from 'vue'
import { provideApolloClient, useMutation, useQuery } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { GET_NOTIFICATIONS_BY_RECIPIENT } from '@/api/graphql/queries/notificationsQuery'
import {
  MARK_ALL_NOTIFICATIONS_AS_READ,
  MARK_NOTIFICATION_AS_READ,
} from '@/api/graphql/mutations/notificationsMutation'
import { useAuthStore } from '@/stores/auth/authStore'

export const useNotificationsGraph = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()
  const currentUserId = computed(() => authStore.claims.pensionUserId as string)

  const {
    result: userNotificationsResult,
    loading: loadingUserNotifications,
    refetch: refetchUserNotifications,
  } = useQuery(GET_NOTIFICATIONS_BY_RECIPIENT,
    { recipientId: currentUserId.value },
    { enabled: !!currentUserId.value },
  )

  const userNotifications = computed(() =>
    userNotificationsResult.value?.getNotificationsByRecipient || [],
  )

  const unreadCount = computed(() => {
    if (!userNotifications.value)
      return 0

    return userNotifications.value.filter((notification: any) => !notification.read).length
  })

  const {
    mutate: markAsReadMutation,
    loading: loadingMarkAsRead,
  } = useMutation(MARK_NOTIFICATION_AS_READ)

  const {
    mutate: markAllAsReadMutation,
    loading: loadingMarkAllAsRead,
  } = useMutation(MARK_ALL_NOTIFICATIONS_AS_READ)

  // Actions
  const markNotificationAsRead = async (id: string) => {
    const response: any = await markAsReadMutation(
      { id },
    )

    await refetchUserNotifications()

    return response.data.markNotificationAsRead
  }

  const markAllNotificationsAsRead = async () => {
    if (!currentUserId.value)
      return

    const response: any = await markAllAsReadMutation(
      { recipientId: currentUserId.value },
    )

    await refetchUserNotifications()

    return response.data.markAllNotificationsAsRead
  }

  return {
    state: {
      userNotifications,
      unreadCount,
      loadingUserNotifications,
      loadingMarkAsRead,
      loadingMarkAllAsRead,
    },
    actions: {
      markNotificationAsRead,
      markAllNotificationsAsRead,
      refetchUserNotifications,
    },
  }
}
