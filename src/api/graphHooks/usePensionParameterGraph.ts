import { provideApolloClient, useMutation, useQuery } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { GET_PENSION_PARAMETERS } from '@/api/graphql/queries/pensionParameterQueries'
import { CREATE_PENSION_PARAMETERS } from '@/api/graphql/mutations/pensionParameterMutations'

export const usePensionParameterGraph = () => {
  provideApolloClient(apolloClient)

  const {
    result: pensionParameters,
    loading: loadingPensionParameters,
    refetch: refetchPensionParameters,
  } = useQuery(GET_PENSION_PARAMETERS)

  const {
    mutate: createPensionParameters,
    loading: loadingCreatePensionParameters,
  } = useMutation(CREATE_PENSION_PARAMETERS)

  const allPensionParameters = computed(() =>
    pensionParameters.value?.getAllPensionParameters || [],
  )

  return {
    state: {
      allPensionParameters,
      loadingPensionParameters,
      loadingCreatePensionParameters,
    },
    actions: {
      refetchPensionParameters,
      createPensionParameters,
    },
  }
}
