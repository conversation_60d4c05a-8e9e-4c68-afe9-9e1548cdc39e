import { provideApolloClient, useMutation, useQuery } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { GET_PENSION_PARAMETERS, GET_PENDING_PENSION_PARAMETERS } from '@/api/graphql/queries/pensionParameterQueries'
import { CREATE_PENSION_PARAMETERS, APPROVE_PENSION_PARAMETERS, REJECT_PENSION_PARAMETERS } from '@/api/graphql/mutations/pensionParameterMutations'

export const usePensionParameterGraph = () => {
  provideApolloClient(apolloClient)

  const {
    result: pensionParameters,
    loading: loadingPensionParameters,
    refetch: refetchPensionParameters,
  } = useQuery(GET_PENSION_PARAMETERS)

  const {
    result: pendingPensionParameters,
    loading: loadingPendingPensionParameters,
    refetch: refetchPendingPensionParameters,
  } = useQuery(GET_PENDING_PENSION_PARAMETERS)

  const {
    mutate: createPensionParameters,
    loading: loadingCreatePensionParameters,
  } = useMutation(CREATE_PENSION_PARAMETERS)

  const {
    mutate: approvePensionParameters,
    loading: loadingApprovePensionParameters,
  } = useMutation(APPROVE_PENSION_PARAMETERS)

  const {
    mutate: rejectPensionParameters,
    loading: loadingRejectPensionParameters,
  } = useMutation(REJECT_PENSION_PARAMETERS)

  const allPensionParameters = computed(() =>
    pensionParameters.value?.getAllPensionParameters || [],
  )

  const pendingPensionParametersData = computed(() =>
    pendingPensionParameters.value?.getPendingPensionParameters || [],
  )

  return {
    state: {
      allPensionParameters,
      pendingPensionParameters: pendingPensionParametersData,
      loadingPensionParameters,
      loadingPendingPensionParameters,
      loadingCreatePensionParameters,
      loadingApprovePensionParameters,
      loadingRejectPensionParameters,
    },
    actions: {
      refetchPensionParameters,
      refetchPendingPensionParameters,
      createPensionParameters,
      approvePensionParameters,
      rejectPensionParameters,
    },
  }
}
