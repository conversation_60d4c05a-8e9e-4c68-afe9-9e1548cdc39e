import { computed } from 'vue'
import { provideApolloClient, useMutation, useQuery } from '@vue/apollo-composable'
import { useRoute, useRouter } from 'vue-router'
import {
  GET_ALL_CERTIFIED_DATA,
  GET_AUTO_APPROVE_ELIGIBLE,
  GET_CERTIFICATION_STATS,
  GET_CERTIFIED_DATA_BY_ID,
  GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE,
  GET_COMMON_CHANGE_PATTERNS,
  GET_LATEST_PARTICIPANT_CERTIFICATION,
  GET_PARTICIPANT_CERTIFIED_DATA,
  PREVIEW_REVERT_CHANGES,
} from '@/api/graphql/queries/certifiedDataQueries'
import {
  BULK_APPROVE_CERTIFICATION,
  BULK_REJECT_CERTIFICATION,
  BULK_START_CERTIFICATION,
  REVERT_APPROVED_REJECTED_CHANGES,
  UPDATE_CERTIFICATION_STATUS,
  UPDATE_CERTIFIED_DATA_APPROVED_CHANGES,
  UPDATE_CERTIFIED_DATA_REJECTED_CHANGES,
} from '@/api/graphql/mutations/certifiedDataMutations'
import { apolloClient } from '@/api/middleware/apolloClient'
import type { CertifiedData } from '@/gql/graphql'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useAuthStore } from '@/stores/auth/authStore'

export const useCertifiedDataGraph = () => {
  provideApolloClient(apolloClient)

  const router = useRouter()
  const pensionStore = usePensionStore()

  const route = useRoute()

  const certificationYear = computed(() => {
    return Number.parseInt(route.params.year as string)
  })

  const participantId = computed(() => router.currentRoute.value.params.id as string)

  const {
    result: allCertifiedDataResult,
    loading: loadingCertifiedData,
    refetch: refetchCertifiedData,
    error: certifiedDataError,
  } = useQuery(GET_ALL_CERTIFIED_DATA, {
    findAllCertifiedDataInput: {},
  })

  const certifiedDataList = computed<any[]>(() =>
    allCertifiedDataResult.value?.getAllCertifiedData.items || [],
  )

  const totalCertifiedData = computed<number>(() =>
    allCertifiedDataResult.value?.getAllCertifiedData.totalCount || 0,
  )

  const certifiedDataId = computed(() => pensionStore.certifiedDataId)

  const {
    result: certifiedDataByIdResult,
    loading: loadingCertifiedDataById,
    error: certifiedDataByIdError,
  } = useQuery(
    GET_CERTIFIED_DATA_BY_ID,
    { findOneCertifiedDataInput: { id: certifiedDataId.value } },
    { enabled: !!certifiedDataId.value },
  )

  const singleCertifiedData = computed<CertifiedData | null>(() =>
    certifiedDataByIdResult.value?.getCertifiedDataById || null,
  )

  const {
    result: participantCertifiedDataResult,
    loading: loadingParticipantCertifiedData,
    error: participantCertifiedDataError,
    refetch: refetchParticipantCertifiedData,
    onResult: onParticipantCertifiedDataResult,
  } = useQuery(
    GET_PARTICIPANT_CERTIFIED_DATA,
    { participantId: participantId.value },
    {
      enabled: !!participantId.value,
      fetchPolicy: 'network-only',
    },
  )

  onParticipantCertifiedDataResult(result => {
    if (result.data.getParticipantCertifiedData[0]?.certifiedEmploymentInfo) {
      pensionStore.setCertifiedData(result.data.getParticipantCertifiedData)

      // Normalize participant certified data based on `certificationYear` and set it to pension store
      const normalizedCertData = result.data.getParticipantCertifiedData.reduce((acc: Record<string, any>, certData: any) => {
        if (certData.certificationYear)
          acc[certData.certificationYear] = certData

        return acc
      }, {})

      pensionStore.setNormalizedCertifiedData(normalizedCertData)
    }
  })

  const participantCertifiedData = computed<CertifiedData[]>(() => {
    const data = participantCertifiedDataResult.value?.getParticipantCertifiedData || []
    if (data.length > 0) {
      // pensionStore.setCertifiedData(data)
    }

    return data
  })

  const {
    result: latestCertificationResult,
    loading: loadingLatestCertification,
    error: latestCertificationError,
    refetch: refetchLatestCertification,
  } = useQuery(
    GET_LATEST_PARTICIPANT_CERTIFICATION,
    { participantId: participantId.value },
    { enabled: !!participantId.value },
  )

  const latestCertification = computed<CertifiedData | null>(() =>
    latestCertificationResult.value?.latestParticipantCertification || null,
  )

  const {
    result: certifiedDataByYearAndYearBeforeResult,
    loading: loadingCertifiedDataByYearAndYearBefore,
    error: certifiedDataByYearAndYearBeforeError,
    refetch: refetchCertifiedDataByYearAndYearBefore,
  } = useQuery(
    GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE,
    { certificationYear: certificationYear.value },
    { fetchPolicy: 'network-only' },
  )

  const certifiedDataByYearAndYearBefore = computed(() => {
    return certifiedDataByYearAndYearBeforeResult.value?.getCertifiedDataByYearAndYearBefore?.data || null
  })

  // Mutations
  const authStore = useAuthStore()
  const userId = computed(() => authStore.claims.pensionUserId as string)

  const {
    mutate: updateCertificationStatusMutation,
    loading: loadingUpdateCertificationStatus,
  } = useMutation(UPDATE_CERTIFICATION_STATUS, {
    refetchQueries: [
      GET_PARTICIPANT_CERTIFIED_DATA,
      GET_LATEST_PARTICIPANT_CERTIFICATION,
    ],
  })

  const updateCertificationStatus = async (id: string, status: string) => {
    try {
      const response = await updateCertificationStatusMutation({
        id,
        status,
      })

      return response?.data?.updateCertificationStatus
    }
    catch (error) {
      console.error('Error updating certification status:', error)
      throw error
    }
  }

  const {
    mutate: updateCertifiedDataApprovedChangesMutation,
    loading: loadingUpdateCertifiedDataApprovedChanges,
  } = useMutation(UPDATE_CERTIFIED_DATA_APPROVED_CHANGES, {
    refetchQueries: [
      GET_PARTICIPANT_CERTIFIED_DATA,
      GET_LATEST_PARTICIPANT_CERTIFICATION,
    ],
  })

  const updateCertifiedDataApprovedChanges = async (id: string, approvedChanges: string[], entityType?: string, entityId?: string) => {
    try {
      const response = await updateCertifiedDataApprovedChangesMutation({
        id,
        approvedChanges,
        entityType,
        entityId,
      })

      return response?.data?.updateCertifiedDataApprovedChanges
    }
    catch (error) {
      console.error('Error updating approved changes:', error)
      throw error
    }
  }

  const {
    mutate: updateCertifiedDataRejectedChangesMutation,
    loading: loadingUpdateCertifiedDataRejectedChanges,
  } = useMutation(UPDATE_CERTIFIED_DATA_REJECTED_CHANGES, {
    refetchQueries: [
      GET_PARTICIPANT_CERTIFIED_DATA,
      GET_LATEST_PARTICIPANT_CERTIFICATION,
      GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE,
    ],
  })

  const updateCertifiedDataRejectedChanges = async (id: string, rejectedChanges: string[], entityType?: string, rejectReason?: string, entityId?: string) => {
    try {
      const response = await updateCertifiedDataRejectedChangesMutation({
        id,
        rejectedChanges,
        entityType,
        rejectReason,
        entityId,
      })

      console.log('RejectedResponse::', { response })

      return response?.data?.updateCertifiedDataRejectedChanges
    }
    catch (error) {
      console.error('Error updating rejected changes:', error)
      throw error
    }
  }

  // Preview revert changes query
  const {
    result: previewRevertChangesResult,
    loading: loadingPreviewRevertChanges,
    error: previewRevertChangesError,
    refetch: refetchPreviewRevertChanges,
  } = useQuery(
    PREVIEW_REVERT_CHANGES,
    () => ({ certificationYear: certificationYear.value }),
    { fetchPolicy: 'network-only' },
  )

  const previewRevertChangesData = computed(() =>
    previewRevertChangesResult.value?.previewRevertChanges || null,
  )

  const previewRevertChanges = async (variables: { certificationYear: number }) => {
    try {
      return await refetchPreviewRevertChanges(variables)
    }
    catch (error) {
      console.error('Error previewing revert changes:', error)
      throw error
    }
  }

  // Revert changes mutation
  const {
    mutate: revertApprovedRejectedChangesMutation,
    loading: loadingRevertChanges,
  } = useMutation(REVERT_APPROVED_REJECTED_CHANGES)

  const revertApprovedRejectedChanges = async (variables: { certificationYear: number }) => {
    try {
      return await revertApprovedRejectedChangesMutation(variables)
    }
    catch (error) {
      console.error('Error reverting changes:', error)
      throw error
    }
  }

  // New bulk certification operations
  const {
    mutate: bulkStartCertificationMutation,
    loading: loadingBulkStartCertification,
  } = useMutation(BULK_START_CERTIFICATION)

  const bulkStartCertification = async (participantIds: string[], year: number) => {
    try {
      const response = await bulkStartCertificationMutation({
        participantIds,
        year,
      })

      return response?.data?.bulkStartCertification
    }
    catch (error) {
      console.error('Error bulk starting certification:', error)
      throw error
    }
  }

  const {
    mutate: bulkApproveCertificationMutation,
    loading: loadingBulkApproveCertification,
  } = useMutation(BULK_APPROVE_CERTIFICATION)

  const bulkApproveCertification = async (certificationIds: string[]) => {
    try {
      const response = await bulkApproveCertificationMutation({
        certificationIds,
      })

      return response?.data?.bulkApproveCertification
    }
    catch (error) {
      console.error('Error bulk approving certification:', error)
      throw error
    }
  }

  const {
    mutate: bulkRejectCertificationMutation,
    loading: loadingBulkRejectCertification,
  } = useMutation(BULK_REJECT_CERTIFICATION)

  const bulkRejectCertification = async (certificationIds: string[], reason: string) => {
    try {
      const response = await bulkRejectCertificationMutation({
        certificationIds,
        reason,
      })

      return response?.data?.bulkRejectCertification
    }
    catch (error) {
      console.error('Error bulk rejecting certification:', error)
      throw error
    }
  }

  // New bulk certification queries
  const {
    result: certificationStatsResult,
    loading: loadingCertificationStats,
    error: certificationStatsError,
    refetch: refetchCertificationStats,
  } = useQuery(GET_CERTIFICATION_STATS, { year: certificationYear.value })

  const certificationStats = computed(() =>
    certificationStatsResult.value?.getCertificationStats || null,
  )

  const {
    result: autoApproveEligibleResult,
    loading: loadingAutoApproveEligible,
    error: autoApproveEligibleError,
    refetch: refetchAutoApproveEligible,
  } = useQuery(GET_AUTO_APPROVE_ELIGIBLE, { year: certificationYear.value })

  const autoApproveEligible = computed(() =>
    autoApproveEligibleResult.value?.getAutoApproveEligible || [],
  )

  const {
    result: commonChangePatternsResult,
    loading: loadingCommonChangePatterns,
    error: commonChangePatternsError,
    refetch: refetchCommonChangePatterns,
  } = useQuery(GET_COMMON_CHANGE_PATTERNS, { year: certificationYear.value })

  const commonChangePatterns = computed(() =>
    commonChangePatternsResult.value?.getCommonChangePatterns || [],
  )

  return {
    state: {
      certifiedDataList,
      totalCertifiedData,
      loadingCertifiedData,
      certifiedDataError,
      certifiedDataById: singleCertifiedData,
      loadingCertifiedDataById,
      certifiedDataByIdError,
      participantCertifiedData,
      loadingParticipantCertifiedData,
      participantCertifiedDataError,
      latestCertification,
      loadingLatestCertification,
      latestCertificationError,
      certifiedDataByYearAndYearBefore,
      loadingCertifiedDataByYearAndYearBefore,
      certifiedDataByYearAndYearBeforeError,
      loadingUpdateCertificationStatus,
      loadingUpdateCertifiedDataApprovedChanges,
      loadingUpdateCertifiedDataRejectedChanges,
      loadingPreviewRevertChanges,
      loadingRevertChanges,
      previewRevertChangesData,

      // New bulk certification state
      loadingBulkStartCertification,
      loadingBulkApproveCertification,
      loadingBulkRejectCertification,
      certificationStats,
      loadingCertificationStats,
      certificationStatsError,
      autoApproveEligible,
      loadingAutoApproveEligible,
      autoApproveEligibleError,
      commonChangePatterns,
      loadingCommonChangePatterns,
      commonChangePatternsError,
    },
    actions: {
      refetchCertifiedData,
      refetchParticipantCertifiedData,
      refetchLatestCertification,
      refetchCertifiedDataByYearAndYearBefore,
      updateCertificationStatus,
      updateCertifiedDataApprovedChanges,
      updateCertifiedDataRejectedChanges,
      previewRevertChanges,
      revertApprovedRejectedChanges,

      // New bulk certification actions
      bulkStartCertification,
      bulkApproveCertification,
      bulkRejectCertification,
      refetchCertificationStats,
      refetchAutoApproveEligible,
      refetchCommonChangePatterns,
    },
  }
}
