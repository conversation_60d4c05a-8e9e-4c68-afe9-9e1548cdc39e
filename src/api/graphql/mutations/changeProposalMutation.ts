import gql from 'graphql-tag'

export const CREATE_CHANGE_PROPOSAL = gql`
    mutation CreateChangeProposal($input: CreateChangeProposalInput!, $changeDataInput: CreateChangeDataInput!) {
        createChangeProposal(createChangeProposalInput: $input, createChangeDataInput: $changeDataInput) {
            id
            createdAt
            createdBy {
                id
                firstname
                lastname
            }
            effectiveDate
            entityId
            entityType
            status
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`

export const UPDATE_CHANGE_PROPOSAL = gql`
    mutation UpdateChangeProposal($input: UpdateChangeProposalInput!) {
        updateChangeProposal(updateChangeProposalInput: $input) {
            id
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
            }
            updatedAt
        }
    }
`

export const APPROVE_CHANGE_PROPOSAL = gql`
    mutation ApproveChangeProposal($changeProposalId: String!, $reviewerId: String!,  $changePropagated: Boolean!) {
        approveChangeProposal(changeProposalId: $changeProposalId, reviewerId: $reviewerId, changePropagated: $changePropagated) {
            id
            entityId
            entityType
            participantName
            status
            effectiveDate
            reviewComments
            changes {
                id
                path
                oldValue
                newValue
            }
            createdAt
            updatedAt
        }
    }
`

export const DELETE_CHANGE_PROPOSAL = gql`
    mutation DeleteChangeProposal($id: String!) {
        deleteChangeProposal(id: $id) {
            id
        }
    }
`

export const REJECT_CHANGE_PROPOSAL = gql`
    mutation RejectChangeProposal($changeProposalId: String!, $reviewerId: String!, $reviewerComments: String!) {
        rejectChangeProposal(changeProposalId: $changeProposalId, reviewerId: $reviewerId, reviewerComments: $reviewerComments) {
            id
            status
            reviewComments
            reviewedAt
            entityType
            changes {
                path
            }
            reviewedBy {
                id
                firstname
                lastname
            }
            updatedAt
        }
    }
`

export const GET_LATEST_APPROVED_CHANGE = gql`
  mutation GetLatestApprovedChange($entityType: String!, $path: String!) {
    getLatestApprovedChange(entityType: $entityType, path: $path) {
      id
      entityType
      entityId
      participantName
      isCertificationProposal
      status
      reviewComments
      createdAt
      updatedAt
      effectiveDate
      createdBy {
        id
        firstname
        lastname
        email
        firebaseUid
        lastLogin
      }
      reviewedAt
      reviewedBy {
        id
        firstname
        lastname
        email
        firebaseUid
        lastLogin
      }
      changes {
        id
        path
        newValue
        oldValue
      }
    }
  }
`
