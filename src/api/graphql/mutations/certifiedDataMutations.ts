import gql from 'graphql-tag'

export const UPDATE_CERTIFICATION_STATUS = gql`
  mutation UpdateCertificationStatus($id: String!, $status: String!) {
    updateCertificationStatus(id: $id, status: $status) {
      id
      certificationStatus
      certificationYear
      certifiedAt
      certifiedById
      participantId
    }
  }
`

export const UPDATE_CERTIFIED_DATA_APPROVED_CHANGES = gql`
  mutation UpdateCertifiedDataApprovedChanges($id: String!, $approvedChanges: [String!]!, $entityType: String, $entityId: String) {
    updateCertifiedDataApprovedChanges(id: $id, approvedChanges: $approvedChanges, entityType: $entityType, entityId: $entityId) {
      id
      certificationStatus
      certificationYear
      certifiedAt
      certifiedById
      participantId
    }
  }
`

export const UPDATE_CERTIFIED_DATA_REJECTED_CHANGES = gql`
  mutation UpdateCertifiedDataRejectedChanges($id: String!, $rejectedChanges: [String!]!, $entityType: String, $rejectReason: String, $entityId: String) {
    updateCertifiedDataRejectedChanges(id: $id, rejectedChanges: $rejectedChanges, entityType: $entityType, rejectReason: $rejectReason, entityId: $entityId) {
      id
      certificationStatus
      certificationYear
      certifiedAt
      certifiedById
      participantId
      __typename
    }
  }
`

export const REVERT_APPROVED_REJECTED_CHANGES = gql`
    mutation RevertApprovedRejectedChanges($certificationYear: Int!) {
        revertApprovedRejectedChanges(certificationYear: $certificationYear) {
            success
            certificationYear
            totalCertifiedDataRecords
            totalEntitiesReverted
            totalRejectionReasonsDeleted
            message
        }
    }
`

export const REVERT_SINGLE_FIELD = gql`
    mutation RevertSingleField($input: RevertSingleFieldInput!) {
        revertSingleField(input: $input) {
            success
            entityId
            entityType
            path
            message
        }
    }
`

export const BULK_START_CERTIFICATION = gql`
    mutation BulkStartCertification($participantIds: [String!]!, $year: Int!) {
        bulkStartCertification(input: { participantIds: $participantIds, year: $year }) {
            successful
            failed {
                participantId
                reason
            }
        }
    }
`

export const BULK_APPROVE_CERTIFICATION = gql`
    mutation BulkApproveCertification($certificationIds: [String!]!) {
        bulkApproveCertification(input: { certificationIds: $certificationIds }) {
            successful
            failed {
                certificationId
                reason
            }
        }
    }
`

export const BULK_REJECT_CERTIFICATION = gql`
    mutation BulkRejectCertification($certificationIds: [String!]!, $reason: String!) {
        bulkRejectCertification(input: { certificationIds: $certificationIds, reason: $reason }) {
            successful
            failed {
                certificationId
                reason
            }
        }
    }
`
