import gql from 'graphql-tag'

export const CREATE_PENSION_PARAMETERS = gql`
    mutation CreatePensionParameters($createPensionParametersInput: CreatePensionParametersInput!) {
        createPensionParameters(createPensionParametersInput: $createPensionParametersInput) {
            id
            accrualPercentage
            annualMultiplier
            offsetAmount
            partnersPensionPercentage
            retirementAge
            voluntaryContributionInterestRate
            year
            effectiveDate
            updatedAt
            createdAt
            userId
            pendingChanges
            status
            rejectReason
            updatedBy {
                id
                email
            }
        }
    }
`

export const APPROVE_PENSION_PARAMETERS = gql`
    mutation ApprovePensionParameters($id: ID!) {
        approvePensionParameters(id: $id) {
            id
            accrualPercentage
            annualMultiplier
            offsetAmount
            partnersPensionPercentage
            retirementAge
            voluntaryContributionInterestRate
            year
            effectiveDate
            updatedAt
            createdAt
            userId
            pendingChanges
            status
            rejectReason
            updatedBy {
                id
                email
            }
        }
    }
`

export const REJECT_PENSION_PARAMETERS = gql`
    mutation RejectPensionParameters($id: ID!, $rejectReason: String!, $userId: String!) {
        rejectPensionParameters(id: $id, rejectReason: $rejectReason, userId: $userId) {
            id
            accrualPercentage
            annualMultiplier
            offsetAmount
            partnersPensionPercentage
            retirementAge
            voluntaryContributionInterestRate
            year
            effectiveDate
            updatedAt
            createdAt
            userId
            pendingChanges
            status
            rejectReason
            updatedBy {
                id
                email
            }
        }
    }
`
