import gql from 'graphql-tag'

export const GET_ALL_CHANGE_PROPOSALS = gql`
    query GetAllChangeProposals {
        getAllChangeProposals {
            id
            createdAt
            isCertificationProposal
            createdBy {
                id
                firstname
                lastname
                email
            }
            effectiveDate
            entityId
            entityType
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
                email
            }
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`

export const GET_PENSION_PARAMS_CHANGE_PROPOSALS = gql`
    query GetAllReviewerChangeProposals($reviewerId: String!) {
        getAllPensionParamChangeProposals(reviewerId: $reviewerId) {
            id
            createdAt
            participantName
            createdBy {
                id
                firstname
                lastname
                email
            }
            isCertificationProposal
            effectiveDate
            entityId
            entityType
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
                email
            }
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`
export const GET_PARTICIPANT_CHANGE_PROPOSALS = gql`
    query GetAllReviewerChangeProposals($reviewerId: String!) {
        getAllParticipantChangeProposals(reviewerId: $reviewerId) {
            id
            createdAt
            participantName
            isCertificationProposal
            createdBy {
                id
                firstname
                lastname
                email
            }
            effectiveDate
            entityId
            entityType
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
                email
            }
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`
export const GET_PENSION_PARAMS_CHANGE_PROPOSALS_HISTORY = gql`
    query GetAllReviewerChangeProposalsHistory($reviewerId: String!) {
        getAllPensionParamChangeProposalsHistory(reviewerId: $reviewerId) {
            id
            createdAt
            createdBy {
                id
                firstname
                lastname
                email
            }
            isCertificationProposal
            effectiveDate
            entityId
            entityType
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
                email
            }
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`
export const GET_PARTICIPANT_CHANGE_PROPOSALS_HISTORY = gql`
    query GetAllReviewerChangeProposalsHistory($reviewerId: String!) {
        getAllParticipantChangeProposalsHistory(reviewerId: $reviewerId) {
            id
            createdAt
            createdBy {
                id
                firstname
                lastname
                email
            }
            isCertificationProposal
            effectiveDate
            entityId
            entityType
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
                email
            }
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`

export const GET_CHANGE_PROPOSAL = gql`
    query FindOneChangeProposal($id: String!) {
        findOneChangeProposal(id: $id) {
            id
            createdAt
            createdBy {
                id
                firstname
                lastname
                email
            }
            isCertificationProposal
            effectiveDate
            entityId
            entityType
            status
            reviewComments
            reviewedAt
            reviewedBy {
                id
                firstname
                lastname
                email
            }
            updatedAt
            changes {
                id
                path
                newValue
                oldValue
            }
        }
    }
`
