import gql from 'graphql-tag'

export const GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE = gql`
    query GetCertifiedDataByYearAndYearBefore($certificationYear: Int!) {
        getCertifiedDataByYearAndYearBefore(certificationYear: $certificationYear) {
            data
        }
    }
`

export const PREVIEW_REVERT_CHANGES = gql`
    query PreviewRevertChanges($certificationYear: Int!) {
        previewRevertChanges(certificationYear: $certificationYear) {
            certificationYear
            totalCertifiedDataRecords
            totalEntitiesAffected
            totalRejectionReasons
            estimatedImpact
            affectedEntities {
                certifiedDataId
                participantName
                entityType
                entityId
                approvedChangesCount
                requestedChangesCount
                approvedChanges
                requestedChanges
            }
        }
    }
`

export const GET_ALL_CERTIFIED_DATA = gql`
    query GetAllCertifiedData($findAllCertifiedDataInput: FindAllCertifiedDataInput) {
        getAllCertifiedData(findAllCertifiedDataInput: $findAllCertifiedDataInput) {
            items {
                id
                participantId
                certificationYear
                certifiedAt
                certifiedById
                certificationStatus
                notes
                certifiedEmploymentInfo {
                    id
                    department
                    employeeId
                    havNum
                    position
                    regNum
                    startDate
                    status
                    pendingChanges
                    requestedChanges
                    approvedChanges
                    certifiedSalaryEntries {
                        id
                        year
                        partTimePercentage
                        amount
                        pendingChanges
                        requestedChanges
                        approvedChanges
                    }
                }
                certifiedPensionInfo {
                    id
                    code
                    codeDescription
                    accruedGrossAnnualOldAgePension
                    accruedGrossAnnualPartnersPension
                    accruedGrossAnnualSinglesPension
                    attainableGrossAnnualOldAgePension
                    extraAccruedGrossAnnualOldAgePension
                    extraAccruedGrossAnnualPartnersPension
                    grossAnnualDisabilityPension
                    pensionBase
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
                certifiedPersonalInfo {
                    id
                    firstName
                    lastName
                    birthDay
                    birthMonth
                    birthYear
                    email
                    phone
                    maritalStatus
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
                certifiedIndexationStartOfYear {
                    id
                    accruedGrossAnnualOldAgePension
                    accruedGrossAnnualPartnersPension
                    accruedGrossAnnualSinglesPension
                    extraAccruedGrossAnnualOldAgePension
                    extraAccruedGrossAnnualPartnersPension
                    grossAnnualDisabilityPension
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
                certifiedPensionCorrections {
                    id
                    year
                    correction
                    accruedGrossAnnualOldAgePension
                    accruedGrossAnnualPartnersPension
                    accruedGrossAnnualSinglesPension
                    attainableGrossAnnualOldAgePension
                    grossAnnualDisabilityPension
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
                certifiedPensionParameters {
                    id
                    year
                    effectiveDate
                    retirementAge
                    accrualPercentage
                    offsetAmount
                    partnersPensionPercentage
                    annualMultiplier
                    voluntaryContributionInterestRate
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
                certifiedVoluntaryContributions {
                    id
                    contributions
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
                certifiedPartnerInfo {
                    id
                    firstName
                    lastName
                    dateOfBirth
                    isCurrent
                    startDate
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
            }
            totalCount
        }
    }
`

export const GET_CERTIFIED_DATA_BY_ID = gql`
    query GetCertifiedDataById($findOneCertifiedDataInput: FindOneCertifiedDataInput!) {
        getCertifiedDataById(findOneCertifiedDataInput: $findOneCertifiedDataInput) {
            id
            participantId
            certificationYear
            certifiedAt
            certifiedById
            certificationStatus
            notes
            certifiedEmploymentInfo {
                id
                department
                employeeId
                havNum
                position
                regNum
                startDate
                status
                pendingChanges
                requestedChanges
                approvedChanges
                certifiedSalaryEntries {
                    id
                    year
                    partTimePercentage
                    amount
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
            }
            certifiedPensionInfo {
                id
                code
                codeDescription
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pensionBase
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPersonalInfo {
                id
                firstName
                lastName
                birthDay
                birthMonth
                birthYear
                email
                phone
                maritalStatus
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedIndexationStartOfYear {
                id
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPensionCorrections {
                id
                year
                correction
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                grossAnnualDisabilityPension
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPensionParameters {
                id
                year
                effectiveDate
                retirementAge
                accrualPercentage
                offsetAmount
                partnersPensionPercentage
                annualMultiplier
                voluntaryContributionInterestRate
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedVoluntaryContributions {
                id
                contributions
                pendingChanges
                requestedChanges
                approvedChanges
            }
        }
    }
`

export const GET_PARTICIPANT_CERTIFIED_DATA = gql`
    query GetParticipantCertifiedData($participantId: String!) {
        getParticipantCertifiedData(participantId: $participantId) {
            id
            participantId
            certificationYear
            certifiedAt
            certifiedById
            certificationStatus
            notes
            certifiedEmploymentInfo {
                id
                department
                employeeId
                havNum
                position
                regNum
                startDate
                status
                pendingChanges
                requestedChanges
                approvedChanges
                certifiedSalaryEntries {
                    id
                    year
                    partTimePercentage
                    amount
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
            }
            certifiedPensionInfo {
                id
                code
                codeDescription
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pensionBase
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPersonalInfo {
                id
                firstName
                lastName
                birthDay
                birthMonth
                birthYear
                email
                phone
                maritalStatus
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedIndexationStartOfYear {
                id
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPensionCorrections {
                id
                year
                correction
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                grossAnnualDisabilityPension
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPensionParameters {
                id
                year
                effectiveDate
                retirementAge
                accrualPercentage
                offsetAmount
                partnersPensionPercentage
                annualMultiplier
                voluntaryContributionInterestRate
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedVoluntaryContributions {
                id
                contributions
                pendingChanges
                requestedChanges
                approvedChanges
            }
        }
    }
`

export const GET_LATEST_PARTICIPANT_CERTIFICATION = gql`
    query GetLatestParticipantCertification($participantId: String!) {
        latestParticipantCertification(participantId: $participantId) {
            id
            participantId
            certificationYear
            certifiedAt
            certifiedById
            certificationStatus
            notes
            certifiedEmploymentInfo {
                id
                department
                employeeId
                havNum
                position
                regNum
                startDate
                status
                pendingChanges
                requestedChanges
                approvedChanges
                certifiedSalaryEntries {
                    id
                    year
                    partTimePercentage
                    amount
                    pendingChanges
                    requestedChanges
                    approvedChanges
                }
            }
            certifiedPensionInfo {
                id
                code
                codeDescription
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pensionBase
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPersonalInfo {
                id
                firstName
                lastName
                birthDay
                birthMonth
                birthYear
                email
                phone
                maritalStatus
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedIndexationStartOfYear {
                id
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPensionCorrections {
                id
                year
                correction
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                grossAnnualDisabilityPension
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedPensionParameters {
                id
                year
                effectiveDate
                retirementAge
                accrualPercentage
                offsetAmount
                partnersPensionPercentage
                annualMultiplier
                voluntaryContributionInterestRate
                pendingChanges
                requestedChanges
                approvedChanges
            }
            certifiedVoluntaryContributions {
                id
                contributions
                pendingChanges
                requestedChanges
                approvedChanges
            }
        }
    }
`

export const GET_CERTIFICATION_STATS = gql`
    query GetCertificationStats($year: Int!) {
        getCertificationStats(input: { year: $year }) {
            totalParticipants
            pendingCertifications
            inProgress
            completed
            requiresAttention
        }
    }
`

export const GET_AUTO_APPROVE_ELIGIBLE = gql`
    query GetAutoApproveEligible($year: Int!) {
        getAutoApproveEligible(input: { year: $year }) {
            certificationId
            participantId
            participantName
            changes
        }
    }
`

export const GET_COMMON_CHANGE_PATTERNS = gql`
    query GetCommonChangePatterns($year: Int!) {
        getCommonChangePatterns(input: { year: $year }) {
            field
            count
            certificationIds
        }
    }
`
