import gql from 'graphql-tag'

export const GET_ALL_PARTICIPANTS = gql`
    query GetAllParticipants {
        getAllParticipants {
            id
            lastModified
            approvalStatus
            rejectReason
            personalInfo {
                birthDay
                birthMonth
                birthYear
                email
                firstName
                id
                lastName
                pendingChanges
            }
            pensionInfo{
                id
                code
                codeDescription
            }
            status
        }
    }
`

export const GET_PARTICIPANT_BY_ID = gql`
    query GetParticipant($id: String!) {
        getParticipantById(id: $id) {
            approvalStatus
            certifiedData {
                certificationYear
                certifiedAt
                certifiedById
                certifiedEmploymentInfo {
                    department
                    employeeId
                    havNum
                    id
                    position
                    regNum
                    startDate
                    status
                }
                id
                certifiedIndexationStartOfYear{
                    id
                }
                notes
                participantId
                certifiedPensionCorrections {
                    id
                    year
                }
                certifiedPensionInfo {
                    id
                    code
                    codeDescription
                }
                certifiedPensionParameters {
                    id
                    year
                }
                certifiedPersonalInfo {
                    id
                }
                certifiedVoluntaryContributions {
                    id
                }
            }
            changeProposals {
                createdAt
                effectiveDate
                entityId
                entityType
                id
                reviewComments
                reviewedAt
                status
                updatedAt
            }
            documents {
                exampleField
            }
            employmentInfo {
                department
                employeeId
                havNum
                id
                position
                regNum
                endDate
                startDate
                status
                pendingChanges
                certificationRejectReason {
                    id
                    field
                    reason
                    status
                    createdAt
                    updatedAt
                    submittedAt
                    submittedForReview
                }
                salaryEntries {
                    id
                    year
                    amount
                    partTimePercentage
                    pendingChanges
                    certificationRejectReason {
                        id
                        field
                        reason
                        status
                        createdAt
                        updatedAt
                        submittedAt
                        submittedForReview
                    }
                }
            }
            pensionInfo {
                code
                previousCode
                previousCodeEffectiveDate
                codeEffectiveDate
                codeDescription
                id
                accruedGrossAnnualOldAgePension
                accruedGrossAnnualPartnersPension
                accruedGrossAnnualSinglesPension
                attainableGrossAnnualOldAgePension
                extraAccruedGrossAnnualOldAgePension
                extraAccruedGrossAnnualPartnersPension
                grossAnnualDisabilityPension
                pensionBase
                pendingChanges
                certificationRejectReason {
                    id
                    field
                    reason
                    status
                    createdAt
                    updatedAt
                    submittedAt
                    submittedForReview
                }
            }
            personalInfo {
                birthDay
                birthMonth
                birthYear
                email
                firstName
                id
                lastName
                maritalStatus
                participantId
                phone
                pendingChanges
                certificationRejectReason {
                    id
                    field
                    reason
                    status
                    createdAt
                    updatedAt
                    submittedAt
                    submittedForReview
                    personalInfoId
                }
                partnerInfo {
                    id
                    lastName
                    firstName
                    dateOfBirth
                    isCurrent
                    startDate
                    endDate
                    pendingChanges
                    certificationRejectReason {
                        id
                        field
                        reason
                        status
                        createdAt
                        updatedAt
                        submittedAt
                        submittedForReview
                        personalInfoId
                    }
                }
                children {
                    id
                    firstName
                    lastName
                    dateOfBirth
                    isOrphan
                    isStudying
                    pendingChanges
                    certificationRejectReason {
                        id
                        field
                        reason
                        status
                        createdAt
                        updatedAt
                        submittedAt
                        submittedForReview
                    }
                }
                address{
                    id
                    street
                    houseNumber
                    postalCode
                    city
                    state
                    country
                    pendingChanges
                    certificationRejectReason {
                        id
                        field
                        reason
                        status
                        createdAt
                        updatedAt
                        submittedAt
                        submittedForReview
                    }
                }
            }
            createdAt
            createdBy
            id
            lastModified
            status
            updatedAt
            updatedBy
        }
    }
`
