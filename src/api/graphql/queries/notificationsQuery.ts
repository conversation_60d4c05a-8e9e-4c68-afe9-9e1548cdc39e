import gql from 'graphql-tag'

export const GET_NOTIFICATIONS_BY_RECIPIENT = gql`
    query GetNotificationsByRecipient($recipientId: String!) {
        getNotificationsByRecipient(recipientId: $recipientId) {
            id
            createdAt
            message
            createdBy {
                id
                firstname
                lastname
            }
            read
            readAt
            type
            entityId
            entityType
        }
    }
`
