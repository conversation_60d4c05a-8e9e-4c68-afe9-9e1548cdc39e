import gql from 'graphql-tag'

export const GET_PENSION_PARAMETERS = gql`
  query getPensionParameters {
    getAllPensionParameters {
      id
      accrualPercentage
      annualMultiplier
      offsetAmount
      minimumPensionBase
      partnersPensionPercentage
      retirementAge
      voluntaryContributionInterestRate
      year
      effectiveDate
      updatedAt
      createdAt
      userId 
      pendingChanges
      status
      rejectReason
      updatedBy {
        id
        email
      }
    }
  }
`

export const GET_PENDING_PENSION_PARAMETERS = gql`
  query getPendingPensionParameters {
    getPendingPensionParameters {
      id
      accrualPercentage
      annualMultiplier
      offsetAmount
      minimumPensionBase
      partnersPensionPercentage
      retirementAge
      voluntaryContributionInterestRate
      year
      effectiveDate
      updatedAt
      createdAt
      userId 
      pendingChanges
      status
      rejectReason
      updatedBy {
        id
        email
      }
    }
  }
`
