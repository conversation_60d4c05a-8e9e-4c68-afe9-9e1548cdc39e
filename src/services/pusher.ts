import Pusher from 'pusher-js'

class PusherService {
  private pusher: Pusher | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000 // Start with 1 second

  constructor() {
    // Don't initialize immediately - wait for user authentication
  }

  private initializePusher() {
    if (this.pusher)
      return

    const pusherKey = import.meta.env.VITE_PUSHER_KEY
    const pusherCluster = import.meta.env.VITE_PUSHER_CLUSTER

    if (!pusherKey || !pusherCluster) {
      console.error('Pusher configuration missing')

      return
    }

    const config = {
      cluster: pusherCluster,
      forceTLS: true,
      enabledTransports: ['ws', 'wss'],
      disabledTransports: [],
    }

    console.log('Initializing Pusher with config:', {
      cluster: pusherCluster,
      key: pusherKey,
    })

    this.pusher = new Pusher(pusherKey, config)

    this.pusher.connection.bind('connected', () => {
      this.isConnected = true
      this.reconnectAttempts = 0 // Reset on successful connection
      console.log('Pusher connected successfully')
    })

    this.pusher.connection.bind('disconnected', () => {
      this.isConnected = false
      console.log('Pusher disconnected')
    })

    this.pusher.connection.bind('error', (error: any) => {
      console.error('Pusher connection error:', error)
      this.handleConnectionError(error)
    })

    this.pusher.connection.bind('unavailable', () => {
      console.warn('Pusher connection unavailable')
    })

    this.pusher.connection.bind('failed', () => {
      console.error('Pusher connection failed')
      this.handleConnectionError({ code: 'connection_failed' })
    })
  }

  private handleConnectionError(error: any) {
    if (error.code === 1006 || error.code === 'connection_failed')
      this.attemptReconnect()
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached. Please refresh the page.')

      return
    }

    this.reconnectAttempts++

    const delay = this.reconnectDelay * 2 ** (this.reconnectAttempts - 1) // Exponential backoff

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      if (this.pusher) {
        this.pusher.disconnect()
        this.pusher = null
      }
      this.initializePusher()
    }, delay)
  }

  connect() {
    if (!this.pusher)
      this.initializePusher()
  }

  subscribeToUserChannel(userId: string, callbacks: {
    onNotificationCreated?: (data: any) => void
  }) {
    // Ensure connection is established
    if (!this.pusher)
      this.connect()

    if (!this.pusher || !userId)
      return null

    // Use public channel only - no authentication required
    const channelName = `user-${userId}`

    try {
      console.log(`Subscribing to public channel: ${channelName}`)

      const channel = this.pusher.subscribe(channelName)

      // Handle subscription success
      channel.bind('pusher:subscription_succeeded', () => {
        console.log(`Successfully subscribed to public channel: ${channelName}`)
      })

      // Handle subscription error
      channel.bind('pusher:subscription_error', (error: any) => {
        console.error(`Failed to subscribe to channel ${channelName}:`, error)
      })

      // Handle notification.created event
      if (callbacks.onNotificationCreated)
        channel.bind('notification.created', callbacks.onNotificationCreated)

      return channel
    }
    catch (error) {
      console.error(`Error subscribing to channel ${channelName}:`, error)

      return null
    }
  }

  unsubscribeFromChannel(channelName: string) {
    if (!this.pusher)
      return

    try {
      this.pusher.unsubscribe(channelName)
      console.log(`Unsubscribed from channel: ${channelName}`)
    }
    catch (error) {
      console.error(`Error unsubscribing from channel ${channelName}:`, error)
    }
  }

  disconnect() {
    if (this.pusher) {
      this.pusher.disconnect()
      this.pusher = null
      this.isConnected = false
      this.reconnectAttempts = 0
      console.log('Pusher disconnected and cleaned up')
    }
  }

  getConnectionState() {
    return this.isConnected
  }

  // Get the current connection state from Pusher
  getCurrentConnectionState() {
    return this.pusher?.connection?.state || 'disconnected'
  }

  // Force reconnection
  forceReconnect() {
    console.log('Forcing Pusher reconnection...')
    this.disconnect()
    this.connect()
  }
}

export const pusherService = new PusherService()
