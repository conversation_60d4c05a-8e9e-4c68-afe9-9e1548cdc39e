export default [
  {
    title: 'Dashboard',
    to: { name: 'root' },
    icon: { icon: 'tabler-smart-home' },
    routeName: 'root',
  },
  {
    title: 'Participants',
    to: { name: 'participants' },
    icon: { icon: 'tabler-users-group' },
    routeName: 'participants',
  },
  {
    title: 'Pension Parameters',
    to: { name: 'pension-parameters' },
    icon: { icon: 'tabler-adjustments-horizontal' },
    routeName: 'pension-parameters',
  },
  {
    title: 'Change Proposals',
    icon: { icon: 'tabler-replace' },
    routeName: 'change-proposals',
    children: [
      { title: 'Requested Changes', to: 'change-proposals-requested', routeName: 'change-proposals' },
      { title: 'Change History', to: 'change-proposals-history', routeName: 'change-proposals-history' },
      { title: 'New Participants', to: 'change-proposals-new-participants', routeName: 'change-proposals-new-participants' },
      { title: 'New Pension Parameters', to: 'change-proposals-new-pension-parameters', routeName: 'change-proposals-new-pension-parameters' },
    ],
  },
  {
    title: 'User Management',
    to: { name: 'users' },
    icon: { icon: 'tabler-user-circle' },
    routeName: 'users',
  },
  {
    title: 'Certification Management',
    to: { name: 'certification-management' },
    icon: { icon: 'tabler-checklist' },
    routeName: 'certification-management',
  },
  {
    title: 'Notifications',
    to: { name: 'notifications' },
    icon: { icon: 'tabler-bell' },
    routeName: 'notifications',
  },
]
