export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] }
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> }
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> }
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never }
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never }

/** All built-in and custom scalars, mapped to their actual values */
export interface Scalars {
  ID: { input: string; output: string }
  String: { input: string; output: string }
  Boolean: { input: boolean; output: boolean }
  Int: { input: number; output: number }
  Float: { input: number; output: number }
  DateTime: { input: any; output: any }
  JSON: { input: any; output: any }
  JSONObject: { input: any; output: any }
}

export interface Address {
  __typename?: 'Address'
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  city?: Maybe<Scalars['String']['output']>
  country?: Maybe<Scalars['String']['output']>
  houseNumber?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  personalInfo: PersonalInfo
  postalCode?: Maybe<Scalars['String']['output']>
  state?: Maybe<Scalars['String']['output']>
  street?: Maybe<Scalars['String']['output']>
}

/** The approval status of a participant */
export enum ApprovalStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED',
}

export interface AuditLog {
  __typename?: 'AuditLog'
  action: Scalars['String']['output']
  changes: Scalars['JSON']['output']
  entityId: Scalars['String']['output']
  entityType: Scalars['String']['output']
  id: Scalars['String']['output']
  ipAddress?: Maybe<Scalars['String']['output']>
  proposalId?: Maybe<Scalars['String']['output']>
  timestamp: Scalars['DateTime']['output']
  user?: Maybe<User>
  userAgent?: Maybe<Scalars['String']['output']>
  userId: Scalars['String']['output']
  userRole?: Maybe<Scalars['String']['output']>
}

export interface AutoApproveEligible {
  __typename?: 'AutoApproveEligible'
  certificationId: Scalars['String']['output']
  changes: Scalars['Int']['output']
  participantId: Scalars['String']['output']
  participantName: Scalars['String']['output']
}

export interface BulkApproveCertificationInput {
  certificationIds: Array<Scalars['String']['input']>
}

export interface BulkApproveCertificationResponse {
  __typename?: 'BulkApproveCertificationResponse'
  failed: Array<BulkOperationFailure>
  successful: Array<Scalars['String']['output']>
}

export interface BulkOperationFailure {
  __typename?: 'BulkOperationFailure'
  certificationId: Scalars['String']['output']
  participantId: Scalars['String']['output']
  reason: Scalars['String']['output']
}

export interface BulkRejectCertificationInput {
  certificationIds: Array<Scalars['String']['input']>
  reason: Scalars['String']['input']
}

export interface BulkRejectCertificationResponse {
  __typename?: 'BulkRejectCertificationResponse'
  failed: Array<BulkOperationFailure>
  successful: Array<Scalars['String']['output']>
}

export interface BulkStartCertificationInput {
  participantIds: Array<Scalars['String']['input']>
  year: Scalars['Int']['input']
}

export interface BulkStartCertificationResponse {
  __typename?: 'BulkStartCertificationResponse'
  failed: Array<BulkOperationFailure>
  successful: Array<Scalars['String']['output']>
}

export interface CertificationRejectReason {
  __typename?: 'CertificationRejectReason'
  certifiedDataId?: Maybe<Scalars['String']['output']>
  certifiedEmploymentInfoId?: Maybe<Scalars['String']['output']>
  certifiedIndexationStartOfYearId?: Maybe<Scalars['String']['output']>
  certifiedPensionCorrectionsId?: Maybe<Scalars['String']['output']>
  certifiedPensionInfoId?: Maybe<Scalars['String']['output']>
  certifiedPensionParametersId?: Maybe<Scalars['String']['output']>
  certifiedPersonalInfoId?: Maybe<Scalars['String']['output']>
  certifiedSalaryEntryId?: Maybe<Scalars['String']['output']>
  certifiedVoluntaryContributionsId?: Maybe<Scalars['String']['output']>
  createdAt: Scalars['DateTime']['output']
  field: Scalars['String']['output']
  id: Scalars['ID']['output']
  personalInfoId?: Maybe<Scalars['String']['output']>
  reason: Scalars['String']['output']
  status: CertificationRejectReasonStatus
  submittedAt?: Maybe<Scalars['DateTime']['output']>
  submittedForReview: Scalars['Boolean']['output']
  updatedAt: Scalars['DateTime']['output']
}

/** The status of a certification reject reason */
export enum CertificationRejectReasonStatus {
  Invalid = 'INVALID',
  Valid = 'VALID',
}

export interface CertificationStats {
  __typename?: 'CertificationStats'
  completed: Scalars['Int']['output']
  inProgress: Scalars['Int']['output']
  pendingCertifications: Scalars['Int']['output']
  requiresAttention: Scalars['Int']['output']
  totalParticipants: Scalars['Int']['output']
}

export interface CertifiedAddress {
  __typename?: 'CertifiedAddress'
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  city?: Maybe<Scalars['String']['output']>
  country?: Maybe<Scalars['String']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  houseNumber?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  personalInfo: CertifiedPersonalInfo
  postalCode?: Maybe<Scalars['String']['output']>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
  state?: Maybe<Scalars['String']['output']>
  street?: Maybe<Scalars['String']['output']>
}

export interface CertifiedChild {
  __typename?: 'CertifiedChild'
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  firstName?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  isOrphan: Scalars['Boolean']['output']
  isStudying: Scalars['Boolean']['output']
  lastName?: Maybe<Scalars['String']['output']>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  personalInfo?: Maybe<CertifiedPersonalInfo>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
}

export interface CertifiedData {
  __typename?: 'CertifiedData'
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certificationStatus?: Maybe<Scalars['String']['output']>
  certificationYear: Scalars['Int']['output']
  certifiedAddress?: Maybe<CertifiedAddress>
  certifiedAt: Scalars['DateTime']['output']
  certifiedBy: User
  certifiedById: Scalars['String']['output']
  certifiedChild?: Maybe<Array<CertifiedChild>>
  certifiedEmploymentInfo?: Maybe<CertifiedEmploymentInfo>
  certifiedIndexationStartOfYear?: Maybe<CertifiedIndexationStartOfYear>
  certifiedPartnerInfo?: Maybe<Array<CertifiedPartnerInfo>>
  certifiedPensionCorrections?: Maybe<CertifiedPensionCorrections>
  certifiedPensionInfo?: Maybe<CertifiedPensionInfo>
  certifiedPensionParameters?: Maybe<CertifiedPensionParameters>
  certifiedPersonalInfo?: Maybe<CertifiedPersonalInfo>
  certifiedVoluntaryContributions?: Maybe<CertifiedVoluntaryContributions>
  differences?: Maybe<Array<Scalars['String']['output']>>
  id: Scalars['ID']['output']
  notes?: Maybe<Scalars['String']['output']>
  participant: Participant
  participantId: Scalars['String']['output']
}

export interface CertifiedDataByYearResponse {
  __typename?: 'CertifiedDataByYearResponse'

  /** Certifications grouped by year, with keys being the year strings */
  data: Scalars['JSONObject']['output']
}

export interface CertifiedEmploymentInfo {
  __typename?: 'CertifiedEmploymentInfo'
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  certifiedSalaryEntries?: Maybe<Array<CertifiedSalaryEntry>>
  department?: Maybe<Scalars['String']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  employeeId?: Maybe<Scalars['String']['output']>
  havNum?: Maybe<Scalars['Int']['output']>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  position?: Maybe<Scalars['String']['output']>
  regNum?: Maybe<Scalars['Int']['output']>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
  startDate?: Maybe<Scalars['DateTime']['output']>
  status?: Maybe<Scalars['String']['output']>
}

export interface CertifiedIndexationStartOfYear {
  __typename?: 'CertifiedIndexationStartOfYear'
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  differences?: Maybe<Array<Scalars['String']['output']>>
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
}

export interface CertifiedPartnerInfo {
  __typename?: 'CertifiedPartnerInfo'
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedPersonalInfoId: Scalars['String']['output']
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  firstName?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  isCurrent: Scalars['Boolean']['output']
  isDeceased: Scalars['Boolean']['output']
  lastName?: Maybe<Scalars['String']['output']>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
  startDate?: Maybe<Scalars['DateTime']['output']>
}

export interface CertifiedPensionCorrections {
  __typename?: 'CertifiedPensionCorrections'
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  attainableGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  correction?: Maybe<Scalars['Float']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
  year?: Maybe<Scalars['String']['output']>
}

export interface CertifiedPensionInfo {
  __typename?: 'CertifiedPensionInfo'
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  attainableGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  code?: Maybe<Scalars['Int']['output']>
  codeDescription?: Maybe<Scalars['String']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>
  grossAnnualPension?: Maybe<Scalars['Float']['output']>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  pensionBase?: Maybe<Scalars['Float']['output']>
  previousCode?: Maybe<Scalars['Int']['output']>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
}

export interface CertifiedPensionParameters {
  __typename?: 'CertifiedPensionParameters'
  accrualPercentage?: Maybe<Scalars['Float']['output']>
  annualMultiplier?: Maybe<Scalars['Float']['output']>
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  differences?: Maybe<Array<Scalars['String']['output']>>
  effectiveDate?: Maybe<Scalars['DateTime']['output']>
  id: Scalars['ID']['output']
  offsetAmount?: Maybe<Scalars['Float']['output']>
  partnersPensionPercentage?: Maybe<Scalars['Float']['output']>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
  retirementAge?: Maybe<Scalars['Int']['output']>
  voluntaryContributionInterestRate?: Maybe<Scalars['Float']['output']>
  year?: Maybe<Scalars['String']['output']>
}

export interface CertifiedPersonalInfo {
  __typename?: 'CertifiedPersonalInfo'
  address?: Maybe<Array<CertifiedAddress>>
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  birthDay?: Maybe<Scalars['Int']['output']>
  birthMonth?: Maybe<Scalars['Int']['output']>
  birthYear?: Maybe<Scalars['Int']['output']>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  children?: Maybe<Array<CertifiedChild>>
  differences?: Maybe<Array<Scalars['String']['output']>>
  email?: Maybe<Scalars['String']['output']>
  firstName?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  lastName?: Maybe<Scalars['String']['output']>
  maritalStatus?: Maybe<Scalars['String']['output']>
  partnerInfo?: Maybe<Array<CertifiedPartnerInfo>>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  phone?: Maybe<Scalars['String']['output']>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
}

export interface CertifiedSalaryEntry {
  __typename?: 'CertifiedSalaryEntry'
  amount: Scalars['Float']['output']
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedEmploymentInfo: CertifiedEmploymentInfo
  differences?: Maybe<Array<Scalars['String']['output']>>
  id: Scalars['String']['output']
  partTimePercentage: Scalars['Float']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
  year: Scalars['Float']['output']
}

export interface CertifiedVoluntaryContributions {
  __typename?: 'CertifiedVoluntaryContributions'
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  certifiedData: CertifiedData
  certifiedDataId: Scalars['String']['output']
  contributions?: Maybe<Scalars['JSON']['output']>
  differences?: Maybe<Array<Scalars['String']['output']>>
  id: Scalars['ID']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>
}

export interface ChangeData {
  __typename?: 'ChangeData'
  changeProposal: ChangeProposal
  id: Scalars['ID']['output']
  newValue: Scalars['JSON']['output']
  oldValue?: Maybe<Scalars['JSON']['output']>
  path: Scalars['String']['output']
}

export interface ChangeProposal {
  __typename?: 'ChangeProposal'
  changePropagated: Scalars['Boolean']['output']
  changes: Array<ChangeData>
  createdAt: Scalars['DateTime']['output']
  createdBy: User
  effectiveDate: Scalars['DateTime']['output']
  entityId: Scalars['String']['output']
  entityType: Scalars['String']['output']
  id: Scalars['String']['output']
  isCertificationProposal?: Maybe<Scalars['Boolean']['output']>
  participantName?: Maybe<Scalars['String']['output']>
  reviewComments?: Maybe<Scalars['String']['output']>
  reviewedAt?: Maybe<Scalars['DateTime']['output']>
  reviewedBy?: Maybe<User>
  status: ChangeStatus
  updatedAt: Scalars['DateTime']['output']
}

export enum ChangeStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED',
}

export enum ChangeType {
  CertifiedData = 'CERTIFIED_DATA',
  Parameters = 'PARAMETERS',
  Participant = 'PARTICIPANT',
  Salary = 'SALARY',
}

export interface Child {
  __typename?: 'Child'
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>
  firstName?: Maybe<Scalars['String']['output']>
  id: Scalars['String']['output']
  isOrphan: Scalars['Boolean']['output']
  isStudying: Scalars['Boolean']['output']
  lastName?: Maybe<Scalars['String']['output']>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  personalInfo: PersonalInfo
}

export interface ClaimsResponse {
  __typename?: 'ClaimsResponse'
  claims?: Maybe<FirebaseUserDto>
  error?: Maybe<ErrorType>
}

export interface CommonChangePattern {
  __typename?: 'CommonChangePattern'
  certificationIds: Array<Scalars['String']['output']>
  count: Scalars['Int']['output']
  field: Scalars['String']['output']
}

export interface CreateAddressInput {
  city?: InputMaybe<Scalars['String']['input']>
  country?: InputMaybe<Scalars['String']['input']>
  houseNumber?: InputMaybe<Scalars['String']['input']>
  personalInfoId: Scalars['String']['input']
  postalCode?: InputMaybe<Scalars['String']['input']>
  state?: InputMaybe<Scalars['String']['input']>
  street?: InputMaybe<Scalars['String']['input']>
}

export interface CreateAuditLogInput {
  action: Scalars['String']['input']
  changes: Scalars['JSON']['input']
  entityId: Scalars['String']['input']
  entityType: Scalars['String']['input']
  ipAddress?: InputMaybe<Scalars['String']['input']>
  proposalId?: InputMaybe<Scalars['String']['input']>
  userAgent?: InputMaybe<Scalars['String']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
  userRole?: InputMaybe<Scalars['String']['input']>
}

export interface CreateCertificationRejectReasonInput {
  certifiedDataId?: InputMaybe<Scalars['String']['input']>
  certifiedEmploymentInfoId?: InputMaybe<Scalars['String']['input']>
  certifiedIndexationStartOfYearId?: InputMaybe<Scalars['String']['input']>
  certifiedPensionCorrectionsId?: InputMaybe<Scalars['String']['input']>
  certifiedPensionInfoId?: InputMaybe<Scalars['String']['input']>
  certifiedPensionParametersId?: InputMaybe<Scalars['String']['input']>
  certifiedPersonalInfoId?: InputMaybe<Scalars['String']['input']>
  certifiedSalaryEntryId?: InputMaybe<Scalars['String']['input']>
  certifiedVoluntaryContributionsId?: InputMaybe<Scalars['String']['input']>
  field: Scalars['String']['input']
  personalInfoId?: InputMaybe<Scalars['String']['input']>
  reason: Scalars['String']['input']
  status?: InputMaybe<CertificationRejectReasonStatus>
  submittedAt?: InputMaybe<Scalars['DateTime']['input']>
  submittedForReview?: InputMaybe<Scalars['Boolean']['input']>
}

export interface CreateCertifiedDataInput {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  certificationYear: Scalars['Int']['input']
  certifiedAt: Scalars['DateTime']['input']
  certifiedBy: UserCreateNestedOneWithoutCertifiedDataInput
  certifiedEmploymentInfo?: InputMaybe<CreateCertifiedEmploymentInfoInput>
  certifiedIndexationStartOfYear?: InputMaybe<CreateCertifiedIndexationStartOfYearInput>
  certifiedPensionCorrections?: InputMaybe<CreateCertifiedPensionCorrectionsInput>
  certifiedPensionInfo?: InputMaybe<CreateCertifiedPensionInfoInput>
  certifiedPensionParameters?: InputMaybe<CreateCertifiedPensionParametersInput>
  certifiedPersonalInfo?: InputMaybe<CreateCertifiedPersonalInfoInput>
  certifiedVoluntaryContributions?: InputMaybe<CreateCertifiedVoluntaryContributionsInput>
  id?: InputMaybe<Scalars['String']['input']>
  notes?: InputMaybe<Scalars['String']['input']>
  participant: ParticipantCreateNestedOneWithoutCertifiedDataInput
}

export interface CreateCertifiedEmploymentInfoInput {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  department?: InputMaybe<Scalars['String']['input']>
  employeeId?: InputMaybe<Scalars['String']['input']>
  havNum?: InputMaybe<Scalars['Int']['input']>
  position?: InputMaybe<Scalars['String']['input']>
  regNum?: InputMaybe<Scalars['Int']['input']>
  salaryEntries?: InputMaybe<Scalars['JSON']['input']>
  startDate?: InputMaybe<Scalars['DateTime']['input']>
  status?: InputMaybe<Scalars['String']['input']>
}

export interface CreateCertifiedIndexationStartOfYearInput {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>
}

export interface CreateCertifiedPensionCorrectionsInput {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>
  attainableGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  correction?: InputMaybe<Scalars['Float']['input']>
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>
  year?: InputMaybe<Scalars['String']['input']>
}

export interface CreateCertifiedPensionInfoInput {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>
  attainableGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  code?: InputMaybe<Scalars['Int']['input']>
  codeDescription?: InputMaybe<Scalars['String']['input']>
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>
  pensionBase?: InputMaybe<Scalars['Float']['input']>
}

export interface CreateCertifiedPensionParametersInput {
  accrualPercentage?: InputMaybe<Scalars['Float']['input']>
  annualMultiplier?: InputMaybe<Scalars['Float']['input']>
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>
  offsetAmount?: InputMaybe<Scalars['Float']['input']>
  partnersPensionPercentage?: InputMaybe<Scalars['Float']['input']>
  retirementAge?: InputMaybe<Scalars['Int']['input']>
  voluntaryContributionInterestRate?: InputMaybe<Scalars['Float']['input']>
  year?: InputMaybe<Scalars['String']['input']>
}

export interface CreateCertifiedPersonalInfoInput {
  address?: InputMaybe<Scalars['JSON']['input']>
  birthDay?: InputMaybe<Scalars['Int']['input']>
  birthMonth?: InputMaybe<Scalars['Int']['input']>
  birthYear?: InputMaybe<Scalars['Int']['input']>
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  children?: InputMaybe<Scalars['JSON']['input']>
  email?: InputMaybe<Scalars['String']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
  maritalStatus?: InputMaybe<Scalars['String']['input']>
  partnerInfo?: InputMaybe<Scalars['JSON']['input']>
  phone?: InputMaybe<Scalars['String']['input']>
}

export interface CreateCertifiedVoluntaryContributionsInput {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  contributions?: InputMaybe<Scalars['JSON']['input']>
}

export interface CreateChangeDataInput {
  changeProposalId?: InputMaybe<Scalars['String']['input']>
  newValue: Scalars['String']['input']
  oldValue?: InputMaybe<Scalars['String']['input']>
  path: Scalars['String']['input']
}

export interface CreateChangeProposalInput {
  changePropagated?: InputMaybe<Scalars['Boolean']['input']>
  createdById: Scalars['String']['input']
  effectiveDate: Scalars['DateTime']['input']
  employmentInfoId?: InputMaybe<Scalars['String']['input']>
  entityId: Scalars['String']['input']
  entityType: Scalars['String']['input']
  isCertificationProposal?: InputMaybe<Scalars['Boolean']['input']>
  participantName?: InputMaybe<Scalars['String']['input']>
  reviewComments?: InputMaybe<Scalars['String']['input']>
  reviewedById?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<ChangeStatus>
  type?: InputMaybe<ChangeType>
}

export interface CreateChildInput {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>
  isStudying?: InputMaybe<Scalars['Boolean']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
  personalInfoId: Scalars['String']['input']
}

export interface CreateDocumentInput {
  createdBy?: InputMaybe<Scalars['String']['input']>
  documentId?: InputMaybe<Scalars['String']['input']>
  mimeType?: InputMaybe<Scalars['String']['input']>
  name?: InputMaybe<Scalars['String']['input']>
  participantId: Scalars['String']['input']
  path?: InputMaybe<Scalars['String']['input']>
  size?: InputMaybe<Scalars['Int']['input']>
  type?: InputMaybe<Scalars['String']['input']>
  uploadedAt?: InputMaybe<Scalars['DateTime']['input']>
}

export interface CreateEmploymentInfoInput {
  department?: InputMaybe<Scalars['String']['input']>
  employeeId?: InputMaybe<Scalars['String']['input']>
  endDate?: InputMaybe<Scalars['DateTime']['input']>
  havNum?: InputMaybe<Scalars['Float']['input']>
  participantId: Scalars['String']['input']
  position?: InputMaybe<Scalars['String']['input']>
  regNum?: InputMaybe<Scalars['Float']['input']>
  startDate?: InputMaybe<Scalars['DateTime']['input']>
  status?: InputMaybe<Scalars['String']['input']>
}

export interface CreateNewAddressInput {
  city?: InputMaybe<Scalars['String']['input']>
  country?: InputMaybe<Scalars['String']['input']>
  houseNumber?: InputMaybe<Scalars['String']['input']>
  postalCode?: InputMaybe<Scalars['String']['input']>
  state?: InputMaybe<Scalars['String']['input']>
  street?: InputMaybe<Scalars['String']['input']>
}

export interface CreateNewChildInput {
  dateOfBirth?: InputMaybe<Scalars['String']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>
  isStudying?: InputMaybe<Scalars['Boolean']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
}

export interface CreateNewEmploymentInfoInput {
  department?: InputMaybe<Scalars['String']['input']>
  employeeId?: InputMaybe<Scalars['String']['input']>
  endDate?: InputMaybe<Scalars['String']['input']>
  havNum?: InputMaybe<Scalars['Float']['input']>
  position?: InputMaybe<Scalars['String']['input']>
  regNum?: InputMaybe<Scalars['Float']['input']>
  salaryEntries?: InputMaybe<Array<CreateSalaryEntryInput>>
  startDate?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<Scalars['String']['input']>
}

export interface CreateNewPartnerInfoInput {
  dateOfBirth?: InputMaybe<Scalars['String']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  isCurrent: Scalars['Boolean']['input']
  isDeceased?: InputMaybe<Scalars['Boolean']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
  startDate?: InputMaybe<Scalars['String']['input']>
}

export interface CreateNewPensionDataInput {
  pensionParameters?: InputMaybe<Array<CreatePensionParametersInput>>
  pensionableAmount?: InputMaybe<Scalars['Float']['input']>
  retirementDate?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<Scalars['String']['input']>
  totalContributions?: InputMaybe<Scalars['Float']['input']>
}

export interface CreateNewPensionInfoInput {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>
  attainableGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  code?: InputMaybe<Scalars['Float']['input']>
  codeDescription?: InputMaybe<Scalars['String']['input']>
  codeEffectiveDate?: InputMaybe<Scalars['String']['input']>
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>
  pensionBase?: InputMaybe<Scalars['Float']['input']>
}

export interface CreateNotificationInput {
  entityId: Scalars['String']['input']
  entityType: Scalars['String']['input']
  message: Scalars['String']['input']
  recipientId: Scalars['String']['input']
  type: Scalars['String']['input']
}

export interface CreateParticipantInput {
  approvalStatus?: InputMaybe<ApprovalStatus>
  createdBy: Scalars['String']['input']
  employmentInfo?: InputMaybe<CreateNewEmploymentInfoInput>
  lastModified?: InputMaybe<Scalars['String']['input']>
  pensionData?: InputMaybe<CreateNewPensionDataInput>
  pensionInfo?: InputMaybe<CreateNewPensionInfoInput>
  personalInfo?: InputMaybe<CreatePersonalInfoInput>
  rejectReason?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<Scalars['String']['input']>
  updatedBy: Scalars['String']['input']
}

export interface CreatePartnerInfoInput {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  isCurrent: Scalars['Boolean']['input']
  isDeceased: Scalars['Boolean']['input']
  lastName?: InputMaybe<Scalars['String']['input']>
  personalInfoId: Scalars['String']['input']
  startDate?: InputMaybe<Scalars['DateTime']['input']>
}

export interface CreatePensionCorrectionsInput {
  correction: Scalars['Float']['input']
  createdById: Scalars['ID']['input']
  reviewedAt: Scalars['DateTime']['input']
  reviewedById: Scalars['ID']['input']
  year: Scalars['String']['input']
}

export interface CreatePensionInfoInput {
  code?: InputMaybe<Scalars['Int']['input']>
  codeDescription?: InputMaybe<Scalars['String']['input']>
  codeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>
  codeImpact?: InputMaybe<Scalars['String']['input']>
  participantId: Scalars['ID']['input']
  previousCode?: InputMaybe<Scalars['Int']['input']>
  previousCodeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>
}

export interface CreatePensionParametersInput {
  accrualPercentage: Scalars['Float']['input']
  annualMultiplier: Scalars['Float']['input']
  effectiveDate: Scalars['DateTime']['input']
  offsetAmount: Scalars['Float']['input']
  partnersPensionPercentage: Scalars['Float']['input']
  retirementAge: Scalars['Int']['input']
  userId: Scalars['ID']['input']
  voluntaryContributionInterestRate: Scalars['Float']['input']
  year: Scalars['String']['input']
}

export interface CreatePersonalInfoInput {
  address?: InputMaybe<CreateNewAddressInput>
  birthDay?: InputMaybe<Scalars['Float']['input']>
  birthMonth?: InputMaybe<Scalars['Float']['input']>
  birthYear?: InputMaybe<Scalars['Float']['input']>
  children?: InputMaybe<Array<CreateNewChildInput>>
  email?: InputMaybe<Scalars['String']['input']>
  firstName: Scalars['String']['input']
  lastName: Scalars['String']['input']
  maritalStatus?: InputMaybe<Scalars['String']['input']>
  partnerInfo?: InputMaybe<Array<CreateNewPartnerInfoInput>>
  phone?: InputMaybe<Scalars['String']['input']>
}

export interface CreateRoleInput {
  description?: InputMaybe<Scalars['String']['input']>
  name: Scalars['String']['input']
}

export interface CreateSalaryEntryInput {
  amount: Scalars['Float']['input']
  employmentInfoId?: InputMaybe<Scalars['String']['input']>
  partTimePercentage: Scalars['Float']['input']
  year: Scalars['Float']['input']
}

export interface CreateSystemSettingInput {
  autoApproveChanges?: Scalars['Boolean']['input']
  effectiveDate: Scalars['DateTime']['input']
  passwordExpiryDays?: Scalars['Int']['input']
  requireTwoFactorAuth?: Scalars['Boolean']['input']
  sessionTimeout?: Scalars['Int']['input']
  userId: Scalars['String']['input']
}

export interface CreateUserInput {
  email: Scalars['String']['input']
  firebaseUid?: InputMaybe<Scalars['String']['input']>
  firstname?: InputMaybe<Scalars['String']['input']>
  lastname?: InputMaybe<Scalars['String']['input']>
  roleId: Scalars['String']['input']
}

export interface Document {
  __typename?: 'Document'

  /** Example field (placeholder) */
  exampleField: Scalars['Int']['output']
}

export interface EmploymentInfo {
  __typename?: 'EmploymentInfo'
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  department?: Maybe<Scalars['String']['output']>
  employeeId?: Maybe<Scalars['String']['output']>
  endDate?: Maybe<Scalars['DateTime']['output']>
  havNum?: Maybe<Scalars['Float']['output']>
  id: Scalars['ID']['output']
  participant: Participant
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  position?: Maybe<Scalars['String']['output']>
  regNum?: Maybe<Scalars['Float']['output']>
  salaryEntries: Array<SalaryEntry>
  startDate?: Maybe<Scalars['DateTime']['output']>
  status?: Maybe<Scalars['String']['output']>
}

export interface ErrorType {
  __typename?: 'ErrorType'
  code?: Maybe<Scalars['String']['output']>
  message: Scalars['String']['output']
}

export interface FindAllAuditLogsInput {
  action?: InputMaybe<Scalars['String']['input']>
  endDate?: InputMaybe<Scalars['DateTime']['input']>
  entityId?: InputMaybe<Scalars['String']['input']>
  entityType?: InputMaybe<Scalars['String']['input']>
  proposalId?: InputMaybe<Scalars['String']['input']>
  skip?: InputMaybe<Scalars['Float']['input']>
  sortBy?: InputMaybe<Scalars['String']['input']>
  sortOrder?: InputMaybe<Scalars['String']['input']>
  startDate?: InputMaybe<Scalars['DateTime']['input']>
  take?: InputMaybe<Scalars['Float']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
}

export interface FindAllCertifiedDataInput {
  certificationYear?: InputMaybe<Scalars['Int']['input']>
  participantId?: InputMaybe<Scalars['String']['input']>
  skip?: InputMaybe<Scalars['Int']['input']>
  sortBy?: InputMaybe<Scalars['String']['input']>
  sortOrder?: InputMaybe<Scalars['String']['input']>
  take?: InputMaybe<Scalars['Int']['input']>
}

export interface FindAllDocumentsInput {
  participantId?: InputMaybe<Scalars['String']['input']>
  searchTerm?: InputMaybe<Scalars['String']['input']>
  skip?: InputMaybe<Scalars['Float']['input']>
  take?: InputMaybe<Scalars['Float']['input']>
  type?: InputMaybe<Scalars['String']['input']>
}

export interface FindAllParticipantsInput {
  approvalStatus?: InputMaybe<ApprovalStatus>
  searchTerm?: InputMaybe<Scalars['String']['input']>
  skip?: InputMaybe<Scalars['Float']['input']>
  sortBy?: InputMaybe<Scalars['String']['input']>
  sortOrder?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<Scalars['String']['input']>
  take?: InputMaybe<Scalars['Float']['input']>
}

export interface FindOneCertifiedDataInput {
  certificationYear?: InputMaybe<Scalars['Int']['input']>
  id?: InputMaybe<Scalars['ID']['input']>
  participantId?: InputMaybe<Scalars['String']['input']>
}

export interface FirebaseUserDto {
  __typename?: 'FirebaseUserDto'
  aud?: Maybe<Scalars['String']['output']>
  auth_time?: Maybe<Scalars['Float']['output']>
  email?: Maybe<Scalars['String']['output']>
  email_verified?: Maybe<Scalars['Boolean']['output']>
  exp?: Maybe<Scalars['Float']['output']>
  iat?: Maybe<Scalars['Float']['output']>
  iss?: Maybe<Scalars['String']['output']>
  name?: Maybe<Scalars['String']['output']>
  pensionUserId?: Maybe<Scalars['String']['output']>
  role?: Maybe<Scalars['String']['output']>
  roleId?: Maybe<Scalars['String']['output']>
  sub?: Maybe<Scalars['String']['output']>
  uid?: Maybe<Scalars['String']['output']>
  user_id?: Maybe<Scalars['String']['output']>
}

export interface GetAutoApproveEligibleInput {
  year: Scalars['Int']['input']
}

export interface GetCertificationStatsInput {
  year: Scalars['Int']['input']
}

export interface GetCommonChangePatternsInput {
  year: Scalars['Int']['input']
}

export interface Mutation {
  __typename?: 'Mutation'
  approveChangeProposal: ChangeProposal
  bulkApproveCertification: BulkApproveCertificationResponse
  bulkRejectCertification: BulkRejectCertificationResponse
  bulkStartCertification: BulkStartCertificationResponse
  createAddress: Address
  createAuditLog: AuditLog
  createCertificationRejectReason: CertificationRejectReason
  createCertifiedData: CertifiedData
  createCertifiedEmploymentInfo: CertifiedEmploymentInfo
  createCertifiedIndexationStartOfYear: CertifiedIndexationStartOfYear
  createCertifiedPensionCorrections: CertifiedPensionCorrections
  createCertifiedPensionInfo: CertifiedPensionInfo
  createCertifiedPensionParameters: CertifiedPensionParameters
  createCertifiedPersonalInfo: CertifiedPersonalInfo
  createCertifiedVoluntaryContributions: CertifiedVoluntaryContributions
  createChangeData: ChangeData
  createChangeProposal: ChangeProposal
  createChild: Child
  createDocument: Document
  createEmploymentInfo: EmploymentInfo
  createNotification: Notification
  createParticipant: Participant
  createPartnerInfo: PartnerInfo
  createPensionCorrections: PensionCorrections
  createPensionInfo: PensionInfo
  createPensionParameters: PensionParameters
  createRole: Role
  createSalaryEntry: SalaryEntry
  createSystemSetting: SystemSetting
  createUser: User
  deleteAddress: Address
  deleteChangeData: ChangeData
  deleteChangeProposal: ChangeProposal
  deleteChild: Child
  deleteEmploymentInfo: EmploymentInfo
  deleteSalaryEntry: SalaryEntry
  getLatestApprovedChange?: Maybe<ChangeProposal>
  markAllNotificationsAsRead: Scalars['Boolean']['output']
  markNotificationAsRead: Notification
  rejectChangeProposal: ChangeProposal
  rejectField: RejectFieldResponse
  rejectPersonalInfoField: PersonalInfo
  removeCertificationRejectReason: CertificationRejectReason
  removeDocument: Document
  removeNotification: Notification
  removeParticipant: Participant
  removePartnerInfo: PartnerInfo
  removePensionCorrections: PensionCorrections
  removePensionInfo: PensionInfo
  removePensionParameters: PensionParameters
  removePersonalInfo: PersonalInfo
  removeRole: Role
  removeSystemSetting: SystemSetting
  removeUser: User
  revertApprovedRejectedChanges: RevertChangesResponse
  revertSingleField: RevertSingleFieldResponse
  setUserClaims: ClaimsResponse
  updateAddress: Address
  updateCertificationStatus: CertifiedData
  updateCertifiedChild: CertifiedChild
  updateCertifiedDataApprovedChanges: CertifiedData
  updateCertifiedDataRejectedChanges: CertifiedData
  updateChangeData: ChangeData
  updateChangeProposal: ChangeProposal
  updateChild: Child
  updateDocument: Document
  updateEmploymentInfo: EmploymentInfo
  updateNotification: Notification
  updateParticipant: Participant
  updatePartnerInfo: PartnerInfo
  updatePensionCorrections: PensionCorrections
  updatePensionInfo: PensionInfo
  updatePensionParameters: PensionParameters
  updateRole: Role
  updateSalaryEntry: SalaryEntry
  updateSystemSetting: SystemSetting
  updateUser: User
  updateUserLastLogin: User
}

export interface MutationApproveChangeProposalArgs {
  changePropagated?: InputMaybe<Scalars['Boolean']['input']>
  changeProposalId: Scalars['String']['input']
  reviewerId: Scalars['String']['input']
}

export interface MutationBulkApproveCertificationArgs {
  input: BulkApproveCertificationInput
}

export interface MutationBulkRejectCertificationArgs {
  input: BulkRejectCertificationInput
}

export interface MutationBulkStartCertificationArgs {
  input: BulkStartCertificationInput
}

export interface MutationCreateAddressArgs {
  createAddressInput: CreateAddressInput
}

export interface MutationCreateAuditLogArgs {
  createAuditLogInput: CreateAuditLogInput
}

export interface MutationCreateCertificationRejectReasonArgs {
  createCertificationRejectReasonInput: CreateCertificationRejectReasonInput
}

export interface MutationCreateCertifiedDataArgs {
  certifiedById: Scalars['String']['input']
  createCertifiedDataInput: CreateCertifiedDataInput
  participantId: Scalars['String']['input']
}

export interface MutationCreateCertifiedEmploymentInfoArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedEmploymentInfoInput: CreateCertifiedEmploymentInfoInput
}

export interface MutationCreateCertifiedIndexationStartOfYearArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedIndexationInput: CreateCertifiedIndexationStartOfYearInput
}

export interface MutationCreateCertifiedPensionCorrectionsArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedCorrectionsInput: CreateCertifiedPensionCorrectionsInput
}

export interface MutationCreateCertifiedPensionInfoArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedPensionInfoInput: CreateCertifiedPensionInfoInput
}

export interface MutationCreateCertifiedPensionParametersArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedParametersInput: CreateCertifiedPensionParametersInput
}

export interface MutationCreateCertifiedPersonalInfoArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedPersonalInfoInput: CreateCertifiedPersonalInfoInput
}

export interface MutationCreateCertifiedVoluntaryContributionsArgs {
  certifiedDataId: Scalars['String']['input']
  createCertifiedContributionsInput: CreateCertifiedVoluntaryContributionsInput
}

export interface MutationCreateChangeDataArgs {
  createChangeDataInput: CreateChangeDataInput
}

export interface MutationCreateChangeProposalArgs {
  createChangeDataInput?: InputMaybe<CreateChangeDataInput>
  createChangeProposalInput: CreateChangeProposalInput
}

export interface MutationCreateChildArgs {
  createChildInput: CreateChildInput
}

export interface MutationCreateDocumentArgs {
  createDocumentInput: CreateDocumentInput
}

export interface MutationCreateEmploymentInfoArgs {
  createEmploymentInfoInput: CreateEmploymentInfoInput
}

export interface MutationCreateNotificationArgs {
  createNotificationInput: CreateNotificationInput
}

export interface MutationCreateParticipantArgs {
  createParticipantInput: CreateParticipantInput
}

export interface MutationCreatePartnerInfoArgs {
  createPartnerInfoInput: CreatePartnerInfoInput
}

export interface MutationCreatePensionCorrectionsArgs {
  createPensionCorrectionsInput: CreatePensionCorrectionsInput
}

export interface MutationCreatePensionInfoArgs {
  createPensionInfoInput: CreatePensionInfoInput
}

export interface MutationCreatePensionParametersArgs {
  createPensionParametersInput: CreatePensionParametersInput
}

export interface MutationCreateRoleArgs {
  createRoleInput: CreateRoleInput
}

export interface MutationCreateSalaryEntryArgs {
  createSalaryEntryInput: CreateSalaryEntryInput
}

export interface MutationCreateSystemSettingArgs {
  createSystemSettingInput: CreateSystemSettingInput
}

export interface MutationCreateUserArgs {
  createUserInput: CreateUserInput
}

export interface MutationDeleteAddressArgs {
  id: Scalars['String']['input']
}

export interface MutationDeleteChangeDataArgs {
  id: Scalars['String']['input']
}

export interface MutationDeleteChangeProposalArgs {
  id: Scalars['String']['input']
}

export interface MutationDeleteChildArgs {
  id: Scalars['String']['input']
}

export interface MutationDeleteEmploymentInfoArgs {
  id: Scalars['String']['input']
}

export interface MutationDeleteSalaryEntryArgs {
  id: Scalars['String']['input']
}

export interface MutationGetLatestApprovedChangeArgs {
  entityType: Scalars['String']['input']
  path: Scalars['String']['input']
}

export interface MutationMarkAllNotificationsAsReadArgs {
  recipientId: Scalars['String']['input']
}

export interface MutationMarkNotificationAsReadArgs {
  id: Scalars['String']['input']
}

export interface MutationRejectChangeProposalArgs {
  changeProposalId: Scalars['String']['input']
  reviewerComments: Scalars['String']['input']
  reviewerId: Scalars['String']['input']
}

export interface MutationRejectFieldArgs {
  input: RejectFieldInput
}

export interface MutationRejectPersonalInfoFieldArgs {
  fieldName: Scalars['String']['input']
  id: Scalars['ID']['input']
  rejectReason: Scalars['String']['input']
  userId: Scalars['String']['input']
}

export interface MutationRemoveCertificationRejectReasonArgs {
  id: Scalars['String']['input']
}

export interface MutationRemoveDocumentArgs {
  id: Scalars['String']['input']
}

export interface MutationRemoveNotificationArgs {
  id: Scalars['String']['input']
}

export interface MutationRemoveParticipantArgs {
  id: Scalars['String']['input']
}

export interface MutationRemovePartnerInfoArgs {
  id: Scalars['ID']['input']
}

export interface MutationRemovePensionCorrectionsArgs {
  id: Scalars['ID']['input']
}

export interface MutationRemovePensionInfoArgs {
  id: Scalars['ID']['input']
}

export interface MutationRemovePensionParametersArgs {
  id: Scalars['ID']['input']
}

export interface MutationRemovePersonalInfoArgs {
  id: Scalars['ID']['input']
}

export interface MutationRemoveRoleArgs {
  id: Scalars['ID']['input']
}

export interface MutationRemoveSystemSettingArgs {
  id: Scalars['String']['input']
}

export interface MutationRemoveUserArgs {
  id: Scalars['String']['input']
}

export interface MutationRevertApprovedRejectedChangesArgs {
  certificationYear: Scalars['Int']['input']
}

export interface MutationRevertSingleFieldArgs {
  input: RevertSingleFieldInput
}

export interface MutationSetUserClaimsArgs {
  firebaseUid: Scalars['String']['input']
}

export interface MutationUpdateAddressArgs {
  updateAddressInput: UpdateAddressInput
}

export interface MutationUpdateCertificationStatusArgs {
  input: UpdateCertificationStatusInput
}

export interface MutationUpdateCertifiedChildArgs {
  updateCertifiedChildInput: UpdateCertifiedChildInput
}

export interface MutationUpdateCertifiedDataApprovedChangesArgs {
  approvedChanges: Array<Scalars['String']['input']>
  entityId?: InputMaybe<Scalars['String']['input']>
  entityType?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
}

export interface MutationUpdateCertifiedDataRejectedChangesArgs {
  entityId?: InputMaybe<Scalars['String']['input']>
  entityType?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  rejectReason?: InputMaybe<Scalars['String']['input']>
  rejectedChanges: Array<Scalars['String']['input']>
}

export interface MutationUpdateChangeDataArgs {
  updateChangeDataInput: UpdateChangeDataInput
}

export interface MutationUpdateChangeProposalArgs {
  updateChangeProposalInput: UpdateChangeProposalInput
}

export interface MutationUpdateChildArgs {
  updateChildInput: UpdateChildInput
}

export interface MutationUpdateDocumentArgs {
  updateDocumentInput: UpdateDocumentInput
}

export interface MutationUpdateEmploymentInfoArgs {
  updateEmploymentInfoInput: UpdateEmploymentInfoInput
}

export interface MutationUpdateNotificationArgs {
  updateNotificationInput: UpdateNotificationInput
}

export interface MutationUpdateParticipantArgs {
  updateParticipantInput: UpdateParticipantInput
}

export interface MutationUpdatePartnerInfoArgs {
  updatePartnerInfoInput: UpdatePartnerInfoInput
}

export interface MutationUpdatePensionCorrectionsArgs {
  updatePensionCorrectionsInput: UpdatePensionCorrectionsInput
}

export interface MutationUpdatePensionInfoArgs {
  updatePensionInfoInput: UpdatePensionInfoInput
}

export interface MutationUpdatePensionParametersArgs {
  updatePensionParametersInput: UpdatePensionParametersInput
}

export interface MutationUpdateRoleArgs {
  updateRoleInput: UpdateRoleInput
}

export interface MutationUpdateSalaryEntryArgs {
  updateSalaryEntryInput: UpdateSalaryEntryInput
}

export interface MutationUpdateSystemSettingArgs {
  updateSystemSettingInput: UpdateSystemSettingInput
}

export interface MutationUpdateUserArgs {
  updateUserInput: UpdateUserInput
}

export interface MutationUpdateUserLastLoginArgs {
  id: Scalars['String']['input']
}

export interface NewUserInput {
  email: Scalars['String']['input']
  name?: InputMaybe<Scalars['String']['input']>
  resetLink: Scalars['String']['input']
}

export interface Notification {
  __typename?: 'Notification'
  createdAt: Scalars['DateTime']['output']
  createdBy: User
  createdById: Scalars['String']['output']
  entityId: Scalars['String']['output']
  entityType: Scalars['String']['output']
  id: Scalars['ID']['output']
  message: Scalars['String']['output']
  read: Scalars['Boolean']['output']
  readAt?: Maybe<Scalars['DateTime']['output']>
  recipient: User
  recipientId: Scalars['String']['output']
  type: Scalars['String']['output']
}

export interface PaginatedAuditLogs {
  __typename?: 'PaginatedAuditLogs'
  items: Array<AuditLog>
  totalCount: Scalars['Int']['output']
}

export interface PaginatedCertifiedData {
  __typename?: 'PaginatedCertifiedData'
  items: Array<CertifiedData>
  totalCount: Scalars['Int']['output']
}

export interface PaginatedParticipants {
  __typename?: 'PaginatedParticipants'
  items: Array<Participant>
  totalCount: Scalars['Int']['output']
}

export interface Participant {
  __typename?: 'Participant'
  approvalStatus: ApprovalStatus
  certifiedData?: Maybe<Array<CertifiedData>>
  changeProposals?: Maybe<Array<ChangeProposal>>
  createdAt: Scalars['DateTime']['output']
  createdBy: Scalars['String']['output']
  documents?: Maybe<Array<Document>>
  employmentInfo?: Maybe<EmploymentInfo>
  id: Scalars['ID']['output']
  lastModified?: Maybe<Scalars['DateTime']['output']>
  pensionInfo?: Maybe<PensionInfo>
  personalInfo?: Maybe<PersonalInfo>
  rejectReason?: Maybe<Scalars['String']['output']>
  status: Scalars['String']['output']
  updatedAt: Scalars['DateTime']['output']
  updatedBy: Scalars['String']['output']
}

export interface ParticipantCreateNestedOneWithoutCertifiedDataInput {
  connect: Scalars['String']['input']
}

export interface PartnerInfo {
  __typename?: 'PartnerInfo'
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>
  firstName?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  isCurrent: Scalars['Boolean']['output']
  isDeceased: Scalars['Boolean']['output']
  lastName?: Maybe<Scalars['String']['output']>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  personalInfoId: Scalars['String']['output']
  startDate?: Maybe<Scalars['DateTime']['output']>
}

export interface PensionCorrections {
  __typename?: 'PensionCorrections'
  correction: Scalars['Float']['output']
  createdAt: Scalars['DateTime']['output']
  createdBy: User
  id: Scalars['ID']['output']
  reviewedAt: Scalars['DateTime']['output']
  reviewedBy: User
  updatedAt: Scalars['DateTime']['output']
  year: Scalars['String']['output']
}

export interface PensionInfo {
  __typename?: 'PensionInfo'
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>
  attainableGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  code?: Maybe<Scalars['Int']['output']>
  codeDescription?: Maybe<Scalars['String']['output']>
  codeEffectiveDate?: Maybe<Scalars['DateTime']['output']>
  codeImpact?: Maybe<Scalars['String']['output']>
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>
  grossAnnualPension?: Maybe<Scalars['Float']['output']>
  id: Scalars['ID']['output']
  participant: Participant
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  pensionBase?: Maybe<Scalars['Float']['output']>
  previousCode?: Maybe<Scalars['Int']['output']>
  previousCodeEffectiveDate?: Maybe<Scalars['DateTime']['output']>
}

export interface PensionParameters {
  __typename?: 'PensionParameters'
  accrualPercentage: Scalars['Float']['output']
  annualMultiplier: Scalars['Float']['output']
  createdAt: Scalars['DateTime']['output']
  effectiveDate: Scalars['DateTime']['output']
  id: Scalars['String']['output']
  offsetAmount: Scalars['Float']['output']
  partnersPensionPercentage: Scalars['Float']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  retirementAge: Scalars['Int']['output']
  updatedAt: Scalars['DateTime']['output']
  updatedBy: User
  userId: Scalars['String']['output']
  voluntaryContributionInterestRate: Scalars['Float']['output']
  year: Scalars['String']['output']
}

export interface PersonalInfo {
  __typename?: 'PersonalInfo'
  address?: Maybe<Address>
  birthDay?: Maybe<Scalars['Int']['output']>
  birthMonth?: Maybe<Scalars['Int']['output']>
  birthYear?: Maybe<Scalars['Int']['output']>
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  children?: Maybe<Array<Child>>
  email?: Maybe<Scalars['String']['output']>
  firstName: Scalars['String']['output']
  id: Scalars['ID']['output']
  lastName: Scalars['String']['output']
  maritalStatus?: Maybe<Scalars['String']['output']>
  participantId: Scalars['String']['output']
  partnerInfo?: Maybe<Array<PartnerInfo>>
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  phone?: Maybe<Scalars['String']['output']>
}

export interface Query {
  __typename?: 'Query'
  address: Address
  addresses: Array<Address>
  auditLog: AuditLog
  auditLogs: PaginatedAuditLogs
  certificationRejectReason: CertificationRejectReason
  certificationRejectReasons: Array<CertificationRejectReason>
  certifiedAddress: CertifiedAddress
  certifiedContributionsByCertifiedDataId: CertifiedVoluntaryContributions
  certifiedCorrectionsByCertifiedDataId: CertifiedPensionCorrections
  certifiedEmploymentInfo: CertifiedEmploymentInfo
  certifiedEmploymentInfoByCertifiedDataId: CertifiedEmploymentInfo
  certifiedIndexationByCertifiedDataId: CertifiedIndexationStartOfYear
  certifiedIndexationStartOfYear: CertifiedIndexationStartOfYear
  certifiedParametersByCertifiedDataId: CertifiedPensionParameters
  certifiedPartnerInfo: CertifiedPartnerInfo
  certifiedPensionCorrections: CertifiedPensionCorrections
  certifiedPensionInfo: CertifiedPensionInfo
  certifiedPensionInfoByCertifiedDataId: CertifiedPensionInfo
  certifiedPensionParameters: CertifiedPensionParameters
  certifiedPersonalInfo: CertifiedPersonalInfo
  certifiedPersonalInfoByCertifiedDataId: CertifiedPersonalInfo
  certifiedVoluntaryContributions: CertifiedVoluntaryContributions
  child: Child
  document: Document
  documents: Array<Document>
  employmentInfo: EmploymentInfo
  employmentInfos: Array<EmploymentInfo>
  entityAuditLogs: Array<AuditLog>
  findOneChangeProposal: ChangeProposal
  getAllCertifiedData: PaginatedCertifiedData
  getAllChangeProposals: Array<ChangeProposal>
  getAllNotifications: Array<Notification>
  getAllParticipantChangeProposals: Array<ChangeProposal>
  getAllParticipantChangeProposalsHistory: Array<ChangeProposal>
  getAllParticipants: Array<Participant>
  getAllParticipantsWithFilter: PaginatedParticipants
  getAllPensionParamChangeProposals: Array<ChangeProposal>
  getAllPensionParamChangeProposalsHistory: Array<ChangeProposal>
  getAllPensionParameters: Array<PensionParameters>
  getAllSystemSettings: Array<SystemSetting>
  getAllUsers: Array<User>
  getAutoApproveEligible: Array<AutoApproveEligible>
  getCertificationStats: CertificationStats
  getCertifiedDataById: CertifiedData
  getCertifiedDataByYearAndYearBefore: CertifiedDataByYearResponse
  getCommonChangePatterns: Array<CommonChangePattern>
  getLatestSystemSetting: SystemSetting
  getNotificationsByRecipient: Array<Notification>
  getParticipantById: Participant
  getParticipantCertifiedData: Array<CertifiedData>
  getPensionChildren: Array<Child>
  getPensionParamById: PensionParameters
  getSystemSettingById: SystemSetting
  getUserByEmail: User
  getUserById: User
  latestParticipantCertification?: Maybe<CertifiedData>
  participantDocuments: Array<Document>
  partnerInfo: PartnerInfo
  partnerInfos: Array<PartnerInfo>
  pensionCorrection: PensionCorrections
  pensionCorrections: Array<PensionCorrections>
  pensionInfo: PensionInfo
  pensionInfos: Array<PensionInfo>
  personalInfo: PersonalInfo
  personalInfos: Array<PersonalInfo>
  previewRevertChanges: RevertChangesPreview
  role: Role
  roles: Array<Role>
  salaryEntries: Array<SalaryEntry>
  salaryEntry: SalaryEntry
  sendNewUserEmail: Scalars['String']['output']
  sendRestPasswordEmail: Scalars['String']['output']
  timeRangeAuditLogs: Array<AuditLog>
  totalAuditLogs: Scalars['Int']['output']
  totalParticipants: Scalars['Int']['output']
  unreadNotificationCount: Scalars['Int']['output']
  unreadNotifications: Array<Notification>
  userAuditLogs: Array<AuditLog>
  userByFirebaseUid: User
  yearCertifications: Array<CertifiedData>
}

export interface QueryAddressArgs {
  id: Scalars['String']['input']
}

export interface QueryAuditLogArgs {
  id: Scalars['String']['input']
}

export interface QueryAuditLogsArgs {
  findAllAuditLogsInput?: InputMaybe<FindAllAuditLogsInput>
}

export interface QueryCertificationRejectReasonArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedAddressArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedContributionsByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedCorrectionsByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedEmploymentInfoArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedEmploymentInfoByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedIndexationByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedIndexationStartOfYearArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedParametersByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedPartnerInfoArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedPensionCorrectionsArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedPensionInfoArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedPensionInfoByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedPensionParametersArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedPersonalInfoArgs {
  id: Scalars['String']['input']
}

export interface QueryCertifiedPersonalInfoByCertifiedDataIdArgs {
  certifiedDataId: Scalars['String']['input']
}

export interface QueryCertifiedVoluntaryContributionsArgs {
  id: Scalars['String']['input']
}

export interface QueryChildArgs {
  id: Scalars['String']['input']
}

export interface QueryDocumentArgs {
  id: Scalars['String']['input']
}

export interface QueryDocumentsArgs {
  findAllDocumentsInput?: InputMaybe<FindAllDocumentsInput>
}

export interface QueryEmploymentInfoArgs {
  id: Scalars['String']['input']
}

export interface QueryEntityAuditLogsArgs {
  entityId: Scalars['String']['input']
  entityType: Scalars['String']['input']
}

export interface QueryFindOneChangeProposalArgs {
  id: Scalars['String']['input']
}

export interface QueryGetAllCertifiedDataArgs {
  findAllCertifiedDataInput?: InputMaybe<FindAllCertifiedDataInput>
}

export interface QueryGetAllParticipantChangeProposalsArgs {
  reviewerId: Scalars['String']['input']
}

export interface QueryGetAllParticipantChangeProposalsHistoryArgs {
  reviewerId: Scalars['String']['input']
}

export interface QueryGetAllParticipantsWithFilterArgs {
  findAllParticipantsInput?: InputMaybe<FindAllParticipantsInput>
}

export interface QueryGetAllPensionParamChangeProposalsArgs {
  reviewerId: Scalars['String']['input']
}

export interface QueryGetAllPensionParamChangeProposalsHistoryArgs {
  reviewerId: Scalars['String']['input']
}

export interface QueryGetAutoApproveEligibleArgs {
  input: GetAutoApproveEligibleInput
}

export interface QueryGetCertificationStatsArgs {
  input: GetCertificationStatsInput
}

export interface QueryGetCertifiedDataByIdArgs {
  findOneCertifiedDataInput: FindOneCertifiedDataInput
}

export interface QueryGetCertifiedDataByYearAndYearBeforeArgs {
  certificationYear: Scalars['Int']['input']
}

export interface QueryGetCommonChangePatternsArgs {
  input: GetCommonChangePatternsInput
}

export interface QueryGetNotificationsByRecipientArgs {
  recipientId: Scalars['String']['input']
}

export interface QueryGetParticipantByIdArgs {
  id: Scalars['String']['input']
}

export interface QueryGetParticipantCertifiedDataArgs {
  participantId: Scalars['String']['input']
}

export interface QueryGetPensionParamByIdArgs {
  id: Scalars['ID']['input']
}

export interface QueryGetSystemSettingByIdArgs {
  id: Scalars['String']['input']
}

export interface QueryGetUserByEmailArgs {
  email: Scalars['String']['input']
}

export interface QueryGetUserByIdArgs {
  id: Scalars['String']['input']
}

export interface QueryLatestParticipantCertificationArgs {
  participantId: Scalars['String']['input']
}

export interface QueryParticipantDocumentsArgs {
  participantId: Scalars['String']['input']
}

export interface QueryPartnerInfoArgs {
  id: Scalars['ID']['input']
}

export interface QueryPensionCorrectionArgs {
  id: Scalars['ID']['input']
}

export interface QueryPensionInfoArgs {
  id: Scalars['ID']['input']
}

export interface QueryPersonalInfoArgs {
  id: Scalars['ID']['input']
}

export interface QueryPreviewRevertChangesArgs {
  certificationYear: Scalars['Int']['input']
}

export interface QueryRoleArgs {
  id: Scalars['ID']['input']
}

export interface QuerySalaryEntryArgs {
  id: Scalars['String']['input']
}

export interface QuerySendNewUserEmailArgs {
  data: NewUserInput
}

export interface QuerySendRestPasswordEmailArgs {
  data: ResetPasswordInput
}

export interface QueryTimeRangeAuditLogsArgs {
  endDate: Scalars['DateTime']['input']
  startDate: Scalars['DateTime']['input']
}

export interface QueryTotalAuditLogsArgs {
  entityType?: InputMaybe<Scalars['String']['input']>
}

export interface QueryTotalParticipantsArgs {
  status?: InputMaybe<Scalars['String']['input']>
}

export interface QueryUserAuditLogsArgs {
  userId: Scalars['String']['input']
}

export interface QueryUserByFirebaseUidArgs {
  firebaseUid: Scalars['String']['input']
}

export interface QueryYearCertificationsArgs {
  certificationYear: Scalars['Int']['input']
}

export interface RejectFieldInput {
  entityId: Scalars['ID']['input']
  entityType: Scalars['String']['input']
  fieldName: Scalars['String']['input']
  rejectReason: Scalars['String']['input']
  userId: Scalars['ID']['input']
}

export interface RejectFieldResponse {
  __typename?: 'RejectFieldResponse'
  entityId: Scalars['ID']['output']
  entityType: Scalars['String']['output']
  fieldName: Scalars['String']['output']
  message: Scalars['String']['output']
  success: Scalars['Boolean']['output']
}

export interface ResetPasswordInput {
  email: Scalars['String']['input']
  resetLink: Scalars['String']['input']
}

export interface RevertChangesPreview {
  __typename?: 'RevertChangesPreview'
  affectedEntities: Array<RevertChangesPreviewItem>
  certificationYear: Scalars['Int']['output']
  estimatedImpact: Scalars['String']['output']
  totalCertifiedDataRecords: Scalars['Int']['output']
  totalEntitiesAffected: Scalars['Int']['output']
  totalRejectionReasons: Scalars['Int']['output']
}

export interface RevertChangesPreviewItem {
  __typename?: 'RevertChangesPreviewItem'
  approvedChanges: Array<Scalars['String']['output']>
  approvedChangesCount: Scalars['Int']['output']
  certifiedDataId: Scalars['String']['output']
  entityId: Scalars['String']['output']
  entityType: Scalars['String']['output']
  participantName: Scalars['String']['output']
  requestedChanges: Array<Scalars['String']['output']>
  requestedChangesCount: Scalars['Int']['output']
}

export interface RevertChangesResponse {
  __typename?: 'RevertChangesResponse'
  certificationYear: Scalars['Int']['output']
  message: Scalars['String']['output']
  revertedEntities?: Maybe<Array<RevertedEntityInfo>>
  success: Scalars['Boolean']['output']
  totalCertifiedDataRecords: Scalars['Int']['output']
  totalEntitiesReverted: Scalars['Int']['output']
  totalRejectionReasonsDeleted: Scalars['Int']['output']
}

export interface RevertSingleFieldInput {
  entityId: Scalars['String']['input']
  entityType: Scalars['String']['input']
  path: Scalars['String']['input']
}

export interface RevertSingleFieldResponse {
  __typename?: 'RevertSingleFieldResponse'
  entityId: Scalars['String']['output']
  entityType: Scalars['String']['output']
  message: Scalars['String']['output']
  path: Scalars['String']['output']
  success: Scalars['Boolean']['output']
}

export interface RevertedEntityInfo {
  __typename?: 'RevertedEntityInfo'
  approvedChangesCleared: Array<Scalars['String']['output']>
  certifiedDataId: Scalars['String']['output']
  entityId: Scalars['String']['output']
  entityType: Scalars['String']['output']
  requestedChangesCleared: Array<Scalars['String']['output']>
}

export interface Role {
  __typename?: 'Role'
  description?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  name: Scalars['String']['output']
}

export interface SalaryEntry {
  __typename?: 'SalaryEntry'
  amount: Scalars['Float']['output']
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>
  employmentInfo: EmploymentInfo
  id: Scalars['ID']['output']
  partTimePercentage: Scalars['Float']['output']
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>
  year: Scalars['Float']['output']
}

export interface Subscription {
  __typename?: 'Subscription'
  userNotifications: Notification
}

export interface SystemSetting {
  __typename?: 'SystemSetting'
  autoApproveChanges: Scalars['Boolean']['output']
  createdAt: Scalars['DateTime']['output']
  effectiveDate: Scalars['DateTime']['output']
  id: Scalars['String']['output']
  passwordExpiryDays: Scalars['Int']['output']
  requireTwoFactorAuth: Scalars['Boolean']['output']
  sessionTimeout: Scalars['Int']['output']
  updatedAt: Scalars['DateTime']['output']
  updatedBy: User
  userId: Scalars['String']['output']
}

export interface UpdateAddressInput {
  city?: InputMaybe<Scalars['String']['input']>
  country?: InputMaybe<Scalars['String']['input']>
  houseNumber?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  personalInfoId?: InputMaybe<Scalars['String']['input']>
  postalCode?: InputMaybe<Scalars['String']['input']>
  state?: InputMaybe<Scalars['String']['input']>
  street?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateCertificationStatusInput {
  id: Scalars['String']['input']
  status: Scalars['String']['input']
}

export interface UpdateCertifiedChildInput {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>
  isStudying?: InputMaybe<Scalars['Boolean']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateChangeDataInput {
  changeProposalId?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  newValue?: InputMaybe<Scalars['String']['input']>
  oldValue?: InputMaybe<Scalars['String']['input']>
  path?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateChangeProposalInput {
  changePropagated?: InputMaybe<Scalars['Boolean']['input']>
  createdById?: InputMaybe<Scalars['String']['input']>
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>
  employmentInfoId?: InputMaybe<Scalars['String']['input']>
  entityId?: InputMaybe<Scalars['String']['input']>
  entityType?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  isCertificationProposal?: InputMaybe<Scalars['Boolean']['input']>
  participantName?: InputMaybe<Scalars['String']['input']>
  reviewComments?: InputMaybe<Scalars['String']['input']>
  reviewedById?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<ChangeStatus>
  type?: InputMaybe<ChangeType>
}

export interface UpdateChildInput {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>
  isStudying?: InputMaybe<Scalars['Boolean']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
  personalInfoId?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateDocumentInput {
  createdBy?: InputMaybe<Scalars['String']['input']>
  documentId?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  mimeType?: InputMaybe<Scalars['String']['input']>
  name?: InputMaybe<Scalars['String']['input']>
  participantId?: InputMaybe<Scalars['String']['input']>
  path?: InputMaybe<Scalars['String']['input']>
  size?: InputMaybe<Scalars['Int']['input']>
  type?: InputMaybe<Scalars['String']['input']>
  uploadedAt?: InputMaybe<Scalars['DateTime']['input']>
}

export interface UpdateEmploymentInfoInput {
  department?: InputMaybe<Scalars['String']['input']>
  employeeId?: InputMaybe<Scalars['String']['input']>
  endDate?: InputMaybe<Scalars['DateTime']['input']>
  havNum?: InputMaybe<Scalars['Float']['input']>
  id: Scalars['String']['input']
  participantId?: InputMaybe<Scalars['String']['input']>
  position?: InputMaybe<Scalars['String']['input']>
  regNum?: InputMaybe<Scalars['Float']['input']>
  startDate?: InputMaybe<Scalars['DateTime']['input']>
  status?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateNotificationInput {
  entityId?: InputMaybe<Scalars['String']['input']>
  entityType?: InputMaybe<Scalars['String']['input']>
  id: Scalars['ID']['input']
  message?: InputMaybe<Scalars['String']['input']>
  read?: InputMaybe<Scalars['Boolean']['input']>
  recipientId?: InputMaybe<Scalars['String']['input']>
  type?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateParticipantInput {
  approvalStatus?: InputMaybe<ApprovalStatus>
  createdBy?: InputMaybe<Scalars['String']['input']>
  employmentInfo?: InputMaybe<CreateNewEmploymentInfoInput>
  id: Scalars['String']['input']
  lastModified?: InputMaybe<Scalars['String']['input']>
  pensionData?: InputMaybe<CreateNewPensionDataInput>
  pensionInfo?: InputMaybe<CreateNewPensionInfoInput>
  personalInfo?: InputMaybe<CreatePersonalInfoInput>
  rejectReason?: InputMaybe<Scalars['String']['input']>
  status?: InputMaybe<Scalars['String']['input']>
  updatedBy?: InputMaybe<Scalars['String']['input']>
}

export interface UpdatePartnerInfoInput {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  id: Scalars['ID']['input']
  isCurrent: Scalars['Boolean']['input']
  isDeceased: Scalars['Boolean']['input']
  lastName?: InputMaybe<Scalars['String']['input']>
  personalInfoId: Scalars['String']['input']
  startDate?: InputMaybe<Scalars['DateTime']['input']>
}

export interface UpdatePensionCorrectionsInput {
  correction?: InputMaybe<Scalars['Float']['input']>
  createdById?: InputMaybe<Scalars['ID']['input']>
  id: Scalars['String']['input']
  reviewedAt?: InputMaybe<Scalars['DateTime']['input']>
  reviewedById?: InputMaybe<Scalars['ID']['input']>
  year?: InputMaybe<Scalars['String']['input']>
}

export interface UpdatePensionInfoInput {
  code?: InputMaybe<Scalars['Int']['input']>
  codeDescription?: InputMaybe<Scalars['String']['input']>
  codeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>
  codeImpact?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  participantId?: InputMaybe<Scalars['ID']['input']>
  previousCode?: InputMaybe<Scalars['Int']['input']>
  previousCodeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>
}

export interface UpdatePensionParametersInput {
  accrualPercentage?: InputMaybe<Scalars['Float']['input']>
  annualMultiplier?: InputMaybe<Scalars['Float']['input']>
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>
  id: Scalars['String']['input']
  offsetAmount?: InputMaybe<Scalars['Float']['input']>
  partnersPensionPercentage?: InputMaybe<Scalars['Float']['input']>
  retirementAge?: InputMaybe<Scalars['Int']['input']>
  userId?: InputMaybe<Scalars['ID']['input']>
  voluntaryContributionInterestRate?: InputMaybe<Scalars['Float']['input']>
  year?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateRoleInput {
  description?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  name?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateSalaryEntryInput {
  amount?: InputMaybe<Scalars['Float']['input']>
  employmentInfoId?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  partTimePercentage?: InputMaybe<Scalars['Float']['input']>
  year?: InputMaybe<Scalars['Float']['input']>
}

export interface UpdateSystemSettingInput {
  autoApproveChanges?: InputMaybe<Scalars['Boolean']['input']>
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>
  id: Scalars['String']['input']
  passwordExpiryDays?: InputMaybe<Scalars['Int']['input']>
  requireTwoFactorAuth?: InputMaybe<Scalars['Boolean']['input']>
  sessionTimeout?: InputMaybe<Scalars['Int']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
}

export interface UpdateUserInput {
  email?: InputMaybe<Scalars['String']['input']>
  firebaseUid?: InputMaybe<Scalars['String']['input']>
  firstname?: InputMaybe<Scalars['String']['input']>
  id: Scalars['String']['input']
  lastname?: InputMaybe<Scalars['String']['input']>
  roleId?: InputMaybe<Scalars['String']['input']>
}

export interface User {
  __typename?: 'User'
  email: Scalars['String']['output']
  firebaseUid: Scalars['String']['output']
  firstname?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  lastLogin?: Maybe<Scalars['DateTime']['output']>
  lastname?: Maybe<Scalars['String']['output']>
  role?: Maybe<Role>
}

export interface UserCreateNestedOneWithoutCertifiedDataInput {
  connect: Scalars['String']['input']
}
