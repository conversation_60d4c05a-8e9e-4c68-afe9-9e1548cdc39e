import { defineStore } from 'pinia'

export const usePensionStore = defineStore('pensionStore', {
  state: () => {
    return {
      activeParticipant: null as any | null,
      leftColumnYear: null as number | null,
      middleColumnYear: null as number | null,
      rightColumnYear: null as number | null,
      leftColumnDate: null as string | null,
      middleColumnDate: null as Date | null,
      rightColumnDate: null as Date | null,
      certifiedDataYears: [] as number[],
      normalizedCertifiedData: null as any,
      certifiedData: null as any,
      certifiedDataId: null as string | null,
      onGoingCertification: true as boolean,
      certifiedDataByYearAndYearBefore: null as any,
    }
  },
  actions: {
    setLeftColumnYear(year: number) {
      this.leftColumnYear = year
    },
    setMiddleColumnYear(year: number) {
      this.middleColumnYear = year
    },
    setRightColumnYear(year: number) {
      this.rightColumnYear = year
    },
    setLeftColumnDate(date: Date | string) {
      this.leftColumnDate = date
    },
    setMiddleColumnDate(date: Date) {
      this.middleColumnDate = date
    },
    setRightColumnDate(date: Date) {
      this.rightColumnDate = date
    },
    setCertifiedDataYears(years: number[]) {
      if (years.length <= 0)
        return
      if (this.certifiedDataYears === null || this.certifiedDataYears.length === 0 || this.certifiedDataYears !== years)
        this.certifiedDataYears = years
    },
    setNormalizedCertifiedData(data: any) {
      if (this.normalizedCertifiedData === null)
        this.normalizedCertifiedData = data
    },
    setCertifiedData(data: any) {
      if (this.certifiedData === null)
        this.certifiedData = data
    },
    setActiveParticipant(data: any) {
      this.activeParticipant = data
    },
    setOngoingCertification(status: boolean) {
      this.onGoingCertification = status
    },
    setCertifiedDataByYearAndYearBefore(data: any) {
      this.certifiedDataByYearAndYearBefore = data
    },
    updatePendingChanges(year: number, field: string, entityType: string) {
      const lCEntityType = entityType.charAt(0).toLowerCase() + entityType.slice(1)

      console.log('Updating pending changes for year', year, 'field', field, 'entityType', lCEntityType)

      if (!this.normalizedCertifiedData?.[year]) {
        console.warn(`No certified data found for year ${year}`)

        return
      }

      const certifiedDataEntity = this.normalizedCertifiedData[year][lCEntityType]

      if (!certifiedDataEntity) {
        console.warn(`No entity found for type ${entityType} in year ${year}`)

        return
      }

      try {
        if (!Array.isArray(certifiedDataEntity.pendingChanges))
          certifiedDataEntity.pendingChanges = []

        if (!certifiedDataEntity.pendingChanges.includes(field)) {
          certifiedDataEntity.pendingChanges = [
            ...certifiedDataEntity.pendingChanges,
            field,
          ]
        }
        else {
          certifiedDataEntity.pendingChanges = certifiedDataEntity.pendingChanges.filter((item: string) => item !== field)
        }
      }
      catch (error) {
        console.error('Error updating pending changes:', error)

        try {
          certifiedDataEntity.pendingChanges = [field]
        }
        catch (fallbackError) {
          console.error('Fallback error:', fallbackError)

          this.normalizedCertifiedData[year][entityType] = {
            ...certifiedDataEntity,
            pendingChanges: [field],
          }
        }
      }
    },

    setNewValue(year: number, field: string, newValue: any, entityType: string) {
      const lCEntityType = entityType.charAt(0).toLowerCase() + entityType.slice(1)
      if (!this.normalizedCertifiedData?.[year]) {
        console.warn(`No certified data found for year ${year}`)

        return
      }

      const certifiedDataEntity = this.normalizedCertifiedData[year][lCEntityType]

      if (!certifiedDataEntity) {
        console.warn(`No entity found for type ${entityType} in year ${year}`)

        return
      }

      try {
        certifiedDataEntity[field] = newValue
      }
      catch (error) {
        console.error('Error setting new value:', error)
      }
    },

    resetStore() {
      this.$reset()
    },
  },
  persist: true,
})
