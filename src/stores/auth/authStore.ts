import type { User } from 'firebase/auth'
import {
  confirmPasswordReset,
  createUserWithEmailAndPassword,
  onIdTokenChanged,
  reload,
  sendEmailVerification,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
} from 'firebase/auth'
import type { Unsubscribe } from 'firebase/firestore'
import { watch } from 'vue'
import { useMutation } from '@vue/apollo-composable'
import { gql } from '@apollo/client/core'
import { initStores, resetStores } from '../initStores'
import { auth } from '@/libs/firebase/config'
import { apolloClient } from '@/api/middleware/apolloClient'
import type { Credentials } from '@/types/authTypes'
import { router } from '@/plugins/1.router'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null) as Ref<User | null>
  const userId = ref(null) as Ref<string | null>
  const claims = ref({}) as Ref<Record<string, unknown>>
  const initialized = ref(false)

  const isLoggedIn = computed(() => user.value != null && userId.value != null)

  const authStore = useAuthStore()

  let unsubAuthListener: Unsubscribe | null = null

  function initAuth() {
    // Return a promise that resolves when auth is initialized
    return new Promise(resolve => {
      if (unsubAuthListener)
        unsubAuthListener()

      unsubAuthListener = onIdTokenChanged(auth, async user => {
        if (user != null) {
          const token = await user.getIdTokenResult(true)

          localStorage.setItem('uid', token?.claims?.sub || '')
          claims.value = token.claims
          userId.value = user.uid
          authStore.user = user
        }
        else {
          await resetStores()

          authStore.user = null
          userId.value = null
          claims.value = {}
        }

        initialized.value = true
        resolve(user)
      })
    })
  }

  const registerUser = async (credentials: Credentials): Promise<{ uid: string }> => {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        credentials.email,
        credentials.password,
      )

      await updateProfile(userCredential.user, {
        displayName: credentials.adminName,
      })
      console.log({ userCredential })

      return { uid: userCredential.user.uid }
    }
    catch (error) {
      console.log(error)

      return { uid: '' }
    }
  }

  const signInUser = async (credentials: Credentials) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        credentials.email,
        credentials.password,
      )

      await new Promise(resolve => {
        const stopWatch = watch(authStore.$state, () => {
          if (authStore.user?.uid === userCredential.user.uid) {
            initStores()
            resolve(null)
            stopWatch()
          }
        })
      })
    }
    catch (error) {
      console.log(error)
      throw error
    }
  }

  const handleSignOut = async () => {
    console.log('signing out')
    try {
      await signOut(auth)
      localStorage.clear()

      // Reset auth store state
      user.value = null
      userId.value = null
      claims.value = {}
      initialized.value = false

      await resetStores()
      await apolloClient.resetStore()
      await router.push('/login')
    }
    catch (error) {
      console.log(error)
    }
  }

  const handleSendEmailVerification = async () => {
    const currentUser = auth.currentUser
    if (currentUser) {
      sendEmailVerification(currentUser)
        .then(() => {
          console.log('Email verification sent')
        })
        .catch(error => {
          console.error('Error sending email verification:', error)
        })
    }
  }

  const sendPasswordResetEmail = async (email: string) => {
    const { mutate } = useMutation(gql`
      mutation ResetPassword($email: String!) {
        resetPassword(email: $email)
      }
    `)

    try {
      await mutate({ email })
    }
    catch (error) {
      console.log(error)
    }
  }

  const resetPassword = async (oobCode: string, newPassword: string) => {
    try {
      await confirmPasswordReset(
        auth,
        oobCode,
        newPassword,
      )
    }
    catch (error) {
      console.log(error)
    }
  }

  const refreshUser = async () => {
    if (authStore.user)
      await reload(authStore.user)
  }

  return {
    user,
    userId,
    claims,
    initialized,
    isLoggedIn,
    initAuth,
    registerUser,
    signInUser,
    handleSignOut,
    handleSendEmailVerification,
    sendPasswordResetEmail,
    resetPassword,
    refreshUser,
  }
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    paths: ['userId', 'claims', 'initialized'],
    serializer: {
      serialize: state => {
        // Only persist specific fields, excluding the user object which doesn't serialize well
        const persistedState = {
          userId: state.userId,
          claims: state.claims,
          initialized: state.initialized,
        }

        return JSON.stringify(persistedState)
      },
      deserialize: value => {
        const parsed = JSON.parse(value)

        return {
          user: null, // User will be restored through Firebase auth listener
          userId: parsed.userId || null,
          claims: parsed.claims || {},
          initialized: parsed.initialized || false,
        }
      },
    },
  },
})

export const initAuthStore = () => {
  const authStore = useAuthStore()

  authStore.initAuth()

  return authStore
}
