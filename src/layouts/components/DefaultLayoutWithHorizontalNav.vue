<script lang="ts" setup>
import navItems from '@/navigation/horizontal'

import { themeConfig } from '@themeConfig'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

// Components
import Footer from '@/layouts/components/Footer.vue'
import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
import UserProfile from '@/layouts/components/UserProfile.vue'
import NavBarI18n from '@core/components/I18n.vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
import { HorizontalNavLayout } from '@layouts'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'

const pensionStore = usePensionStore()
const { filterNavigationByRole } = useRoleAccess()

// Filter navigation items based on user role
const filteredNavItems = computed(() => filterNavigationByRole(navItems))

const getParticipantNameForBreadcrumbs = (id: string): string | null => {
  // Try to get from active participant
  const activeParticipant = pensionStore.activeParticipant
  if (activeParticipant?.personalInfo)
    return `${activeParticipant.personalInfo.firstName} ${activeParticipant.personalInfo.lastName}`

  // Fallback to formatted ID
  return 'Participant'
}
</script>

<template>
  <HorizontalNavLayout :nav-items="filteredNavItems">
    <!-- 👉 navbar -->
    <template #navbar>
      <RouterLink
        to="/"
        class="app-logo d-flex align-center gap-x-3"
      >
        <VNodeRenderer :nodes="themeConfig.app.logo" />

        <h1 class="app-title font-weight-bold leading-normal text-xl text-capitalize">
          {{ themeConfig.app.title }}
        </h1>
      </RouterLink>
      <VSpacer />

      <NavBarI18n
        v-if="themeConfig.app.i18n.enable && themeConfig.app.i18n.langConfig?.length"
        :languages="themeConfig.app.i18n.langConfig"
      />

      <NavbarThemeSwitcher class="me-2" />
      <UserProfile />
    </template>

    <!-- 👉 Pages -->
    <div class="page-content-wrapper">
      <!-- 👉 Breadcrumbs -->
      <SimpleBreadcrumbs
        class="ma-6"
        :items="[]"
      />

      <!-- 👉 Page Content -->
      <div class="px-6">
        <slot />
      </div>
    </div>

    <!-- 👉 Footer -->
    <template #footer>
      <Footer />
    </template>

    <!-- 👉 Customizer -->
    <!-- <TheCustomizer /> -->
  </HorizontalNavLayout>
</template>
