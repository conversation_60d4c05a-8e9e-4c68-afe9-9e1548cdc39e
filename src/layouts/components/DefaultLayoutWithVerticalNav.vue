<script lang="ts" setup>
import navItems from '@/navigation/vertical'
import { themeConfig } from '@themeConfig'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

// Components
import Footer from '@/layouts/components/Footer.vue'
import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
import UserProfile from '@/layouts/components/UserProfile.vue'
import NavBarI18n from '@core/components/I18n.vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'

// @layouts plugin
import { VerticalNavLayout } from '@layouts'
import NavBarNotifications from '@/layouts/components/NavBarNotifications.vue'

const pensionStore = usePensionStore()
const { filterNavigationByRole } = useRoleAccess()

// Filter navigation items based on user role
const filteredNavItems = computed(() => filterNavigationByRole(navItems))

// Global participant name provider for breadcrumbs
const getParticipantNameForBreadcrumbs = (id: string): string | null => {
  // Try to get from active participant
  const activeParticipant = pensionStore.activeParticipant
  if (activeParticipant?.personalInfo)
    return `${activeParticipant.personalInfo.firstName} ${activeParticipant.personalInfo.lastName}`

  // Fallback to formatted ID
  return `Participant ${id}`
}
</script>

<template>
  <VerticalNavLayout :nav-items="filteredNavItems">
    <!-- 👉 navbar -->
    <template #navbar="{ toggleVerticalOverlayNavActive }">
      <div class="d-flex h-100 align-center">
        <IconBtn
          id="vertical-nav-toggle-btn"
          class="ms-n3 d-lg-none"
          @click="toggleVerticalOverlayNavActive(true)"
        >
          <VIcon
            size="26"
            icon="tabler-menu-2"
          />
        </IconBtn>

        <NavbarThemeSwitcher />

        <VSpacer />

        <NavBarI18n
          v-if="themeConfig.app.i18n.enable && themeConfig.app.i18n.langConfig?.length"
          :languages="themeConfig.app.i18n.langConfig"
        />
        <NavBarNotifications class="me-1" />
        <UserProfile />
      </div>
    </template>

    <!-- 👉 Pages -->
    <div class="page-content-wrapper">
      <!-- 👉 Breadcrumbs -->
      <SimpleBreadcrumbs :items="[]" />

      <!-- 👉 Page Content -->
      <slot />
    </div>

    <!-- 👉 Footer -->
    <template #footer>
      <Footer />
    </template>

    <!-- 👉 Customizer -->
    <!-- <TheCustomizer /> -->
  </VerticalNavLayout>
</template>
