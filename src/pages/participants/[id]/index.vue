<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { onMounted } from 'vue'
import PensionCalculationBase from '@/components/participants/pensionCalculation/PensionCalculationBase.vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useCertifiedData } from '@/composables/certified-data'
import EmploymentInfo from '@/components/participants/basicInfo/EmploymentInfo.vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const pensionStore = usePensionStore()
const { state: { participantCertifiedData, loadingParticipantCertifiedData } } = useCertifiedData()
const route = useRoute()
const router = useRouter()

const { subscribeToNotifications } = usePusherNotifications()

const { actions: { refetchSingleParticipant } } = useParticipants()
const { canExportData, canEditParticipant } = useRoleAccess()

const participantId = computed(() => route.params.id as string)

const navigateToExport = () => {
  router.push(`/participant-export/${participantId.value}`)
}

const initializePensionMiscData = () => {
  const certifiedDataYears = participantCertifiedData.value.map((entry: any) => entry.certificationYear)

  pensionStore.setCertifiedDataYears(certifiedDataYears)

  const currentYear = new Date().getFullYear()
  const rightColDate = new Date(currentYear, 11, 31)
  const middleColDate = new Date(currentYear - 1, 11, 31)

  const leftColDate = new Date(currentYear - 2, 11, 31).toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric',
  })

  pensionStore.setMiddleColumnYear(currentYear - 1)
  pensionStore.setRightColumnYear(currentYear)
  pensionStore.setLeftColumnYear(currentYear - 2)
  pensionStore.setRightColumnDate(rightColDate)
  pensionStore.setMiddleColumnDate(middleColDate)
  pensionStore.setLeftColumnDate(leftColDate)
}

watch(
  [() => participantCertifiedData.value, () => loadingParticipantCertifiedData.value],
  ([data, loading]) => {
    if (!loading && data.length > 0)
      initializePensionMiscData()
  },
  { immediate: true },
)

onMounted(() => {
  subscribeToNotifications(notification => {
    if (notification.type === 'CHANGE_PROPOSAL_APPROVED' || notification.type === 'CHANGE_PROPOSAL_REJECTED')
      refetchSingleParticipant({ id: participantId.value })
  })
})
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Participants', to: '/participants', icon: 'tabler-users' },
      { title: 'Participant', disabled: true, icon: 'tabler-user' },
    ]"
  />

  <!-- Export Button Header -->
  <VCard class="mb-6">
    <VCardText>
      <div class="d-flex justify-space-between align-center">
        <div>
          <h2 class="text-h5 font-weight-bold mb-1">
            Participant Details
          </h2>
          <p class="text-body-2 text-medium-emphasis mb-0">
            View and manage participant information and export data
          </p>
        </div>
        <VBtn
          v-if="canExportData"
          color="primary"
          variant="elevated"
          prepend-icon="tabler-download"
          @click="navigateToExport"
        >
          Export to Excel
        </VBtn>
      </div>
    </VCardText>
  </VCard>

  <BasicInformation :editable="canEditParticipant" />
  <ParticipantInformation :editable="canEditParticipant" />
  <EmploymentInfo :editable="canEditParticipant" />
  <PensionInfo :editable="canEditParticipant" />
  <SalaryPensionBase :editable="canEditParticipant" />
  <PensionCalculationBase :editable="canEditParticipant" />
</template>
