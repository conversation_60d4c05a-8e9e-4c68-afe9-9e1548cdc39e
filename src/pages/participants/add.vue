<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useParticipants } from '@/composables/participants/useParticipants'
import AddParticipantForm from '@/components/participants/AddParticipantForm.vue'
import { useAppStore } from '@/stores/app/appStore'
import { convertExcelToFormData, exportParticipantTemplate, parseParticipantExcel } from '@/utils/excelUtils'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'
import { useRouteGuard } from '@/composables/auth/useRouteGuard'

const router = useRouter()

const {
  state: { creatingParticipant, error },
  actions: { createParticipant },
} = useParticipants()

const appStore = useAppStore()
const loading = ref(false)
const fileInput = ref<HTMLInputElement>()
const participantFormRef = ref<InstanceType<typeof AddParticipantForm>>()

const { canCreateParticipant, canExportData } = useRoleAccess()
const { checkActionAccess, shouldShowRestrictedPage } = useRouteGuard()

onMounted(() => {
  checkActionAccess('create-participant')
})

// Excel export function
const exportTemplate = () => {
  try {
    exportParticipantTemplate()
    appStore.showSnack('Excel template downloaded successfully')
  }
  catch (error) {
    console.error('Error exporting template:', error)
    appStore.showSnack('Error downloading template')
  }
}

// Trigger file input
const triggerFileUpload = () => {
  fileInput.value?.click()
}

// Handle file upload
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file)
    return

  loading.value = true
  try {
    const participantData = await parseParticipantExcel(file)

    if (participantData.length > 0) {
      // Convert first participant to form data and populate the form
      const formData = convertExcelToFormData(participantData[0])

      // Populate the form with imported data
      if (participantFormRef.value)
        await participantFormRef.value.populateForm(formData)

      const firstParticipant = participantData[0]
      const partnerCount = firstParticipant.personalInfo.partners.length
      const childrenCount = firstParticipant.personalInfo.children.length
      const salaryCount = firstParticipant.employmentInfo.salaryEntries.length

      let message = 'Successfully imported participant data'
      if (partnerCount > 0)
        message += ` with ${partnerCount} partner(s)`
      if (childrenCount > 0)
        message += ` and ${childrenCount} child(ren)`
      if (salaryCount > 1)
        message += ` and ${salaryCount} salary entries`

      appStore.showSnack(message)

      if (participantData.length > 1)
        appStore.showSnack('Note: Only the first participant data was loaded. Please create additional participants separately.')
    }
  }
  catch (error) {
    console.error('Error importing Excel file:', error)
    appStore.showSnack(`Error importing file: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
  finally {
    loading.value = false

    // Reset file input
    if (target)
      target.value = ''
  }
}

const handleSubmit = async (formData: any) => {
  try {
    await createParticipant(formData)
    appStore.showSnack('Participant created successfully')
    router.push('/participants')
  }
  catch (error) {
    console.error('Error creating participant:', error)
    appStore.showSnack('Error creating participant')
  }
}

const handleCancel = () => {
  router.push('/participants')
}
</script>

<template>
  <div class="add-participant-container">
    <PermissionRestricted v-if="shouldShowRestrictedPage" />
    <div v-else>
      <SimpleBreadcrumbs
        :items="[
          { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
          { title: 'Participants', to: '/participants', icon: 'tabler-users' },
          { title: 'Add New Participant', disabled: true, icon: 'tabler-user-plus' },
        ]"
      />

      <div class="header-container">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 font-weight-bold">
              Add New Participant
            </h1>
            <p class="text-subtitle-1 text-grey-darken-1">
              Create a new pension participant
            </p>
          </div>
          <div class="d-flex gap-3">
            <VBtn
              v-if="canExportData"
              color="primary"
              variant="outlined"
              prepend-icon="tabler-download"
              :disabled="loading"
              @click="exportTemplate"
            >
              Export Excel Template
            </VBtn>
            <VBtn
              v-if="canCreateParticipant"
              color="success"
              variant="outlined"
              prepend-icon="tabler-upload"
              :disabled="loading"
              @click="triggerFileUpload"
            >
              Import New Participant
            </VBtn>
            <input
              ref="fileInput"
              type="file"
              accept=".xlsx,.xls"
              style="display: none"
              @change="handleFileUpload"
            >
          </div>
        </div>
      </div>

      <VOverlay
        :model-value="creatingParticipant"
        class="align-center justify-center"
      >
        <VProgressCircular
          color="primary"
          indeterminate
          size="64"
        />
      </VOverlay>

      <AddParticipantForm
        ref="participantFormRef"
        :loading="creatingParticipant"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>

<style scoped>
.add-participant-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header-container {
  margin-bottom: 32px;
}
</style>
