<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth/authStore'
import { useAppStore } from '@/stores/app/appStore'

definePage({
  meta: {
    layout: 'blank',
    public: true,
  },
})

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const password = ref('')
const confirmPassword = ref('')

const handlePasswordReset = async () => {
  if (password.value !== confirmPassword.value) {
    appStore.showSnack('Passwords do not match.')

    return
  }

  const oobCode = route.query.oobCode as string

  try {
    await authStore.resetPassword(oobCode, password.value)
    appStore.showSnack('Password has been reset successfully.')
    router.push({ name: 'login' })
  }
  catch (error) {
    appStore.showSnack('Error resetting password.')
  }
}
</script>

<template>
  <div class="auth-wrapper d-flex align-center justify-center pa-4">
    <VCard
      class="auth-card pa-4 pt-7"
      max-width="448"
    >
      <VCardItem class="justify-center">
        <VCardTitle class="font-weight-semibold text-2xl text-uppercase">
          Reset Password
        </VCardTitle>
      </VCardItem>

      <VCardText class="pt-2">
        <p class="mb-0">
          Enter your new password
        </p>
      </VCardText>

      <VCardText>
        <VForm @submit.prevent="handlePasswordReset">
          <VRow>
            <!-- password -->
            <VCol cols="12">
              <VTextField
                v-model="password"
                label="Password"
                type="password"
              />
            </VCol>

            <!-- confirm password -->
            <VCol cols="12">
              <VTextField
                v-model="confirmPassword"
                label="Confirm Password"
                type="password"
              />
            </VCol>

            <!-- reset password button -->
            <VCol cols="12">
              <VBtn
                block
                type="submit"
              >
                Reset Password
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>
