<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useMutation } from '@vue/apollo-composable'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useParticipantGraph } from '@/api/graphHooks/useParticipantGraph'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { UPDATE_PARTICIPANT } from '@/api/graphql/mutations/participantMutations'
import { useAuthStore } from '@/stores/auth/authStore'
import { useAppStore } from '@/stores/app/appStore'
import { ApprovalStatus } from '@/gql/graphql'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'

const {
  state: { loadingParticipant, currentUserId },
  actions: { refetchSingleParticipant },
} = useParticipants()

const { state: { singleParticipant } } = useParticipantGraph()
const pensionStore = usePensionStore()

const route = useRoute()
const authStore = useAuthStore()
const appStore = useAppStore()

const id = computed(() => route.params.id as string)

// Computed properties for participant data
const participant = computed(() => singleParticipant.value)
const activeParticipant = computed(() => pensionStore.activeParticipant)

const participantName = computed(() => {
  if (!participant.value?.personalInfo)
    return 'Loading...'
  const personalInfo = participant.value.personalInfo

  return personalInfo ? `${personalInfo.firstName} ${personalInfo.lastName}` : 'Unknown Participant'
})

const isApprovalPending = computed(() => participant.value?.approvalStatus === 'PENDING')

// Check for pending changes across all participant data
const pendingChangesCheck = computed(() => {
  const active = activeParticipant.value
  if (!active)
    return { hasPendingChanges: false, pendingFields: [] }

  const pendingFields: string[] = []

  // Check personalInfo pendingChanges
  if (active.personalInfo?.pendingChanges?.length > 0)
    pendingFields.push('Personal Information')

  // Check address pendingChanges
  if (active.personalInfo?.address?.pendingChanges?.length > 0)
    pendingFields.push('Address')

  // Check partnerInfo pendingChanges
  if (active.personalInfo?.partnerInfo?.length > 0) {
    active.personalInfo.partnerInfo.forEach((partner: any, index: number) => {
      if (partner.pendingChanges?.length > 0) {
        const partnerName = `${partner.firstName} ${partner.lastName}` || `Partner ${index + 1}`

        pendingFields.push(`Partner: ${partnerName}`)
      }
    })
  }

  // Check children pendingChanges
  if (active.personalInfo?.children?.length > 0) {
    active.personalInfo.children.forEach((child: any, index: number) => {
      if (child.pendingChanges?.length > 0) {
        const childName = `${child.firstName} ${child.lastName}` || `Child ${index + 1}`

        pendingFields.push(`Child: ${childName}`)
      }
    })
  }

  // Check employmentInfo pendingChanges
  if (active.employmentInfo?.pendingChanges?.length > 0)
    pendingFields.push('Employment Information')

  // Check salaryEntries pendingChanges
  if (active.employmentInfo?.salaryEntries?.length > 0) {
    active.employmentInfo.salaryEntries.forEach((entry: any) => {
      if (entry.pendingChanges?.length > 0)
        pendingFields.push(`Salary Entry (${entry.year})`)
    })
  }

  // Check pensionInfo pendingChanges
  if (active.pensionInfo?.pendingChanges?.length > 0)
    pendingFields.push('Pension Information')

  return {
    hasPendingChanges: pendingFields.length > 0,
    pendingFields,
  }
})

// Loading state computed
const isDataReady = computed(() => !loadingParticipant.value && !!participant.value)

// Can approve computed - checks both approval status and pending changes
const canApprove = computed(() => {
  return isApprovalPending.value && !pendingChangesCheck.value.hasPendingChanges
})

// Approval mutation
const { mutate: updateParticipantMutation, loading: approvingParticipant } = useMutation(UPDATE_PARTICIPANT)

// Approval function
const approveParticipant = async () => {
  if (!participant.value || !currentUserId.value) {
    appStore.showSnack('❌ Error: Missing participant data or user authentication')

    return
  }

  if (pendingChangesCheck.value.hasPendingChanges) {
    appStore.showSnack('❌ Cannot approve participant with pending changes. Please resolve all pending changes first.')

    return
  }

  try {
    appStore.showSnack('Approving participant...')

    await updateParticipantMutation({
      input: {
        id: participant.value.id,
        approvalStatus: ApprovalStatus.Approved,
        updatedBy: currentUserId.value,
      },
    })

    appStore.showSnack(`✅ ${participantName.value} has been approved successfully!`)

    // Refetch to get updated data
    await refetchSingleParticipant()
  }
  catch (error) {
    console.error('Error approving participant:', error)
    appStore.showSnack('❌ Failed to approve participant. Please try again.')
  }
}

// Fetch participant data on mount
onMounted(async () => {
  try {
    await refetchSingleParticipant()
  }
  catch (error) {
    console.error('Error fetching participant data:', error)
    appStore.showSnack('❌ Failed to load participant data. Please refresh the page.')
  }
})

watch(
  () => id.value,
  async newId => {
    if (newId) {
      try {
        await refetchSingleParticipant()
      }
      catch (error) {
        console.error('Error refetching participant data:', error)
        appStore.showSnack('❌ Failed to load participant data.')
      }
    }
  },
  { immediate: false },
)

const showRejectOptions = !isApprovalPending.value
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/' },
      { title: 'Change Proposals', to: '/change-proposals' },
      { title: 'New Participants', to: '/change-proposals/new-participants' },
      { title: `Participant`, disabled: true },
    ]"
  />
  <!-- Loading State -->
  <div
    v-if="loadingParticipant"
    class="loading-container"
  >
    <!-- Header Loading Skeleton -->
    <VSkeletonLoader
      type="card"
      class="mb-6"
      height="120"
    />

    <!-- Content Loading Skeletons -->
    <VSkeletonLoader
      v-for="n in 5"
      :key="n"
      type="card"
      class="mb-4"
      height="200"
    />
  </div>

  <!-- Content when data is ready -->
  <div v-else-if="isDataReady">
    <!-- Pending Changes Warning Alert -->
    <VAlert
      v-if="isApprovalPending && pendingChangesCheck.hasPendingChanges"
      type="warning"
      variant="tonal"
      class="mb-6"
      prominent
    >
      <template #prepend>
        <VIcon
          icon="tabler-alert-triangle"
          size="24"
        />
      </template>

      <div>
        <div class="text-h6 mb-2">
          Pending Changes Detected
        </div>
        <div class="text-body-1 mb-3">
          <strong>{{ participantName }}</strong> cannot be approved because there are pending changes that need to be resolved first.
        </div>

        <div class="text-body-2">
          <strong>Fields with pending changes:</strong>
          <ul class="mt-2 ml-4">
            <li
              v-for="field in pendingChangesCheck.pendingFields"
              :key="field"
              class="mb-1"
            >
              {{ field }}
            </li>
          </ul>
        </div>

        <div class="text-body-2 mt-3 text-warning">
          <VIcon
            icon="tabler-info-circle"
            size="16"
            class="mr-1"
          />
          Please ensure all pending changes are resolved by approving them in change proposals.
        </div>
      </div>
    </VAlert>

    <!-- Participant Approval Alert -->
    <VAlert
      v-else-if="isApprovalPending"
      type="success"
      variant="tonal"
      class="mb-6"
      prominent
    >
      <template #prepend>
        <VIcon
          icon="tabler-user-check"
          size="24"
        />
      </template>

      <div class="d-flex align-center justify-space-between">
        <div class="flex-grow-1">
          <div class="text-h6 mb-2">
            Ready for Approval
          </div>
          <div class="text-body-1">
            <strong>{{ participantName }}</strong> is ready for approval.
            Approving this participant will allow them to proceed in the system.
          </div>
        </div>

        <div class="ml-4">
          <VBtn
            color="success"
            variant="elevated"
            size="large"
            :loading="approvingParticipant"
            :disabled="!canApprove || approvingParticipant"
            @click="approveParticipant"
          >
            <VIcon
              icon="tabler-check"
              class="mr-2"
            />
            Approve Participant
          </VBtn>
        </div>
      </div>
    </VAlert>

    <!-- Participant Already Approved Alert -->
    <VAlert
      v-else-if="!isApprovalPending"
      type="info"
      variant="tonal"
      class="mb-6"
    >
      <template #prepend>
        <VIcon
          icon="tabler-circle-check"
          size="20"
        />
      </template>

      <div class="text-body-1">
        <strong>{{ participantName }}</strong> has been
        <span class="font-weight-bold text-success">{{ participant?.approvalStatus?.toLowerCase() }}</span> and added to the system.
      </div>
    </VAlert>

    <!-- Participant Information Components -->
    <NewBasicInformation
      :show-reject-options="showRejectOptions"
      :editor-view="!isApprovalPending"
    />
    <NewParticipantInformation
      :show-reject-options="showRejectOptions"
      :editor-view="!isApprovalPending"
    />
    <NewEmploymentInfo
      :show-reject-options="showRejectOptions"
      :editor-view="!isApprovalPending"
    />
    <NewPensionInfo
      :show-reject-options="showRejectOptions"
      :editor-view="!isApprovalPending"
    />
    <NewSalaryPensionBase
      :show-reject-options="showRejectOptions"
      :editor-view="!isApprovalPending"
    />
  </div>

  <!-- Error State -->
  <VAlert
    v-else
    type="error"
    variant="tonal"
    class="mb-6"
  >
    <template #prepend>
      <VIcon
        icon="tabler-alert-circle"
        size="20"
      />
    </template>

    <div class="text-body-1">
      Failed to load participant data. Please refresh the page or try again later.
    </div>

    <template #append>
      <VBtn
        variant="outlined"
        size="small"
        @click="refetchSingleParticipant"
      >
        Retry
      </VBtn>
    </template>
  </VAlert>
</template>

<style scoped lang="scss">
.loading-container {
  padding: 16px;
}

.loading-container .v-skeleton-loader {
  border-radius: 8px;
}
</style>
