<script setup lang="ts">
import { useRoute } from 'vue-router'
import { useParticipants } from '@/composables/participants/useParticipants'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
import { useParticipantGraph } from '@/api/graphHooks/useParticipantGraph'
import { usePensionStore } from '@/stores/pension/pensionStore'

const { actions: { refetchSingleParticipant } } = useParticipants()

const route = useRoute()
const id = computed(() => route.params.id as string)


const { state: { singleParticipant }} = useParticipantGraph()
const pensionStore = usePensionStore()

// Computed properties for participant data
const participant = computed(() => singleParticipant.value)
const activeParticipant = computed(() => pensionStore.activeParticipant)

const participantName = computed(() => {
  if (!participant.value?.personalInfo)
    return 'Loading...'
  const personalInfo = participant.value.personalInfo

  return personalInfo ? `${personalInfo.firstName} ${personalInfo.lastName}` : 'Unknown Participant'
})

const isApprovalPending = computed(() => participant.value?.approvalStatus === 'PENDING')

watch(
  () => id.value,
  () => {
    refetchSingleParticipant()
  },
  { immediate: true },
)
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Change Proposals', to: '/change-proposals', icon: 'tabler-file-text' },
      { title: 'New Participants', to: '/change-proposals/new-participants', icon: 'tabler-user-plus' },
      { title: 'Review', disabled: true, icon: 'tabler-eye' },
    ]"
  />

  <VAlert
    type="error"
    variant="tonal"
    class="mb-6"
    v-if="isApprovalPending"
  >
    <template #prepend>
      <VIcon
        icon="tabler-exclamation-circle"
        size="20"
      />
    </template>

    <div class="text-body-1">
      <strong>{{ participantName }}</strong> is pending approval and has NOT been added to the system yet.
    </div>
  </VAlert>

  <NewBasicInformation
    :editor-view="true"
    :show-reject-options="false"
  />
  <NewParticipantInformation
    :show-reject-options="false"
    :editor-view="true"
  />
  <NewEmploymentInfo
    :show-reject-options="false"
    :editor-view="true"
  />
  <NewPensionInfo
    :show-reject-options="false"
    :editor-view="true"
  />
  <NewSalaryPensionBase
    :show-reject-options="false"
    :editor-view="true"
  />
</template>
