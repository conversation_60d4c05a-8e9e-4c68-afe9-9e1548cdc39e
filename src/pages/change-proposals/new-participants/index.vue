<script setup lang="ts">
import NewParticipantsTable from '@/components/change-proposal/requested/NewParticipantsTable.vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Change Proposals', to: '/change-proposals', icon: 'tabler-file-text' },
      { title: 'New Participants', disabled: true, icon: 'tabler-user-plus' },
    ]"
  />

  <VCard>
    <VRow no-gutters>
      <VCol cols="12">
        <VCardText>
          <NewParticipantsTable />
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>
