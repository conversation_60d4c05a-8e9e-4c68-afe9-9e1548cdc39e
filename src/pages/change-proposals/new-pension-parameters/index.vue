<script setup lang="ts">
import NewPensionParametersTable from '@/components/pension-parameters/NewPensionParametersTable.vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Change Proposals', to: '/change-proposals', icon: 'tabler-file-text' },
      { title: 'New Pension Parameters', disabled: true, icon: 'tabler-settings' },
    ]"
  />

  <VCard>
    <VRow no-gutters>
      <VCol cols="12">
        <VCardText>
          <NewPensionParametersTable />
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>