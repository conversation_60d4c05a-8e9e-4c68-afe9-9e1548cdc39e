<script setup lang="ts">
import { onMounted } from 'vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'

const {
  actions: { refetchParticipantChangeProposals, refetchParticipantChangeProposalsHistory, refetchPensionParamsChangeProposalsHistory, refetchPensionParamsChangeProposals },
} = useChangeProposal()

const { subscribeToNotifications } = usePusherNotifications()

const propertyListingSteps = [
  {
    title: 'Participant Changes',
    subtitle: 'Pending changes requested',
    icon: 'tabler-users',
  },
  {
    title: 'Pension Parameter Changes',
    subtitle: 'Pending changes requested',
    icon: 'tabler-refresh',
  },
  {
    title: 'Certfied Participant Changes',
    subtitle: 'Pending certified data changes requested',
    icon: 'tabler-checklist',
  },
]

const currentStep = ref(0)

onMounted(() => {
  refetchParticipantChangeProposals()
  refetchPensionParamsChangeProposals()

  subscribeToNotifications(notification => {
    if (notification.type === 'CHANGE_PROPOSAL_CREATED') {
      if (notification.entityType === 'PensionParameters') {
        refetchPensionParamsChangeProposalsHistory()
        refetchPensionParamsChangeProposals()
      }
      else {
        refetchParticipantChangeProposals()
        refetchParticipantChangeProposalsHistory()
      }
    }
  })
})
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Change Proposals', to: '/change-proposals', icon: 'tabler-file-text' },
      { title: 'Requested', disabled: true, icon: 'tabler-clock' },
    ]"
  />

  <VCard>
    <VRow no-gutters>
      <VCardText>
        <AppStepper
          v-model:current-step="currentStep"
          :items="propertyListingSteps"
          icon-size="22"
          class="stepper-icon-step-bg"
        />
      </VCardText>

      <VCol cols="12">
        <VCardText>
          <VWindow v-model="currentStep">
            <VWindowItem>
              <ParticipantChangeTable />
            </VWindowItem>

            <VWindowItem>
              <PensionParamChangeTable />
            </VWindowItem>
            <VWindowItem>
              <CertifiedParticipantChangeTable />
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>
