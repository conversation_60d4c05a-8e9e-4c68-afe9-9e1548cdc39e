<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
import { ChangeType } from '@/gql/graphql'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'
import CreatePensionParametersDialog from '@/components/pension-parameters/CreatePensionParametersDialog.vue'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'

const appStore = useAppStore()
const editDialog = ref(false)
const createDialog = ref(false)
const currentTab = ref(0)
const { canEditPensionParameters } = useRoleAccess()

const { state: { allPensionParameters: pensionParametersData, loadingPensionParameters }, actions: { refetchPensionParameters } } = usePensionParameters()

const { subscribeToNotifications } = usePusherNotifications()

const selectedField = ref({
  field: '',
  value: '',
  id: '',
  typename: '',
  year: '',
})

const parameterDefinitions: Record<string, {
  title: string
  description: string
  formatter: (value: number | null | undefined) => string | number
}> = {
  accrualPercentage: {
    title: 'Accrual Percentage',
    description: 'The percentage of pensionable salary that is accrued as pension each year',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `${(value * 100).toFixed(2)}%`
    },
  },
  annualMultiplier: {
    title: 'Annual Multiplier',
    description: 'The factor used to calculate annual pensionable salary from monthly salary',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return value
    },
  },
  offsetAmount: {
    title: 'Offset Amount',
    description: 'The amount deducted from the pensionable salary before calculating pension accrual',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `Afl. ${value.toLocaleString()}`
    },
  },
  partnersPensionPercentage: {
    title: 'Partners Pension Percentage',
    description: 'The percentage of the pension that goes to the partner in case of death',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `${(value * 100).toFixed(2)}%`
    },
  },
  retirementAge: {
    title: 'Retirement Age',
    description: 'The age at which the participant is eligible for full pension benefits',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return value
    },
  },
  voluntaryContributionInterestRate: {
    title: 'Voluntary Contribution Interest Rate',
    description: 'The interest rate applied to voluntary contributions',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `${(value * 100).toFixed(2)}%`
    },
  },
  minimumPensionBase: {
    title: 'Minimum Pension Base',
    description: 'If gross fulltime annual salary minus offset is less than minimum pension base, then the pension base should be set to the minimum pension base',
    formatter: (value: number | null | undefined) => {
      if (value == null) return 'N/A'
      return `Afl. ${value.toLocaleString()}`
    },
  },
}

// Create tabs configuration based on parameter definitions
const tabs = Object.keys(parameterDefinitions).map(paramType => ({
  title: parameterDefinitions[paramType].title,
  subtitle: parameterDefinitions[paramType].description,
  icon: 'tabler-settings', // You can customize icons per tab if needed
  paramType,
}))

const parameterTables = computed(() => {
  const tables: Record<string, {
    title: string
    description: string
    items: Array<{
      year: string
      value: string | number
      rawValue: any
      id: string
      editable: boolean
    }>
  }> = {}

  Object.keys(parameterDefinitions).forEach(paramType => {
    const paramInfo = parameterDefinitions[paramType]

    const tableItems: Array<{
      year: string
      value: string | number
      rawValue: any
      id: string
      editable: boolean
    }> = []

    const sortedData = [...(pensionParametersData.value || [])].sort((a: any, b: any) => {
      const yearA = a?.year ? Number.parseInt(a.year) : 0
      const yearB = b?.year ? Number.parseInt(b.year) : 0
      return yearB - yearA
    })

    sortedData.forEach((param: any) => {
      if (!param) return

      const isEditable = param.pendingChanges?.includes(paramType) || false

      tableItems.push({
        year: param.year || 'N/A',
        value: paramInfo.formatter(param[paramType]),
        rawValue: param[paramType],
        id: param.id || '',
        editable: !isEditable,
      })
    })

    tables[paramType] = {
      title: paramInfo.title,
      description: paramInfo.description,
      items: tableItems,
    }
  })

  return tables
})

// Define table headers
const headers = [
  { title: 'YEAR', key: 'year', width: '200px' },
  { title: 'VALUE', key: 'value' },
  { title: '', key: 'actions', sortable: false, width: '100px' },
]

const openEditDialog = (item: any, paramType: string) => {
  console.log({ item, paramType })
  if (!item.editable) {
    appStore.showSnack('Sorry you cannot edit this field')

    return
  }

  selectedField.value = {
    field: paramType,
    value: item.rawValue,
    id: item.id,
    typename: 'PensionParameters',
    year: item.year,
  }

  editDialog.value = true
}

const closeEditDialog = () => {
  editDialog.value = false
}

const updateField = (newValue: any) => {
  closeEditDialog()
}

const openCreateDialog = () => {
  createDialog.value = true
}

const closeCreateDialog = () => {
  createDialog.value = false
}

const handleParametersCreated = async () => {
  await refetchPensionParameters()
  closeCreateDialog()
}

onMounted(async () => {
  await refetchPensionParameters()

  subscribeToNotifications(notification => {
    if (notification.entityType === 'PensionParameters')
      refetchPensionParameters()
  })
})
</script>

<template>
  <VCard>
    <!-- Header with Create Button -->
    <VCardTitle class="d-flex justify-space-between align-center pa-4">
      <span>Pension Parameters</span>
      <VBtn
        v-if="canEditPensionParameters"
        color="primary"
        variant="elevated"
        prepend-icon="tabler-plus"
        @click="openCreateDialog"
      >
        Create Parameters
      </VBtn>
    </VCardTitle>

    <VRow no-gutters>
      <VCol
        cols="12"
        md="6"
      >
        <VCardText>
          <AppStepper
            v-model:current-step="currentTab"
            :items="tabs"
            icon-size="22"
            direction="vertical"
            class="stepper-icon-step-bg"
          />
        </VCardText>
      </VCol>

      <VCol cols="6">
        <VCardText>
          <VWindow
            v-model="currentTab"
            class="disable-tab-transition"
            direction="vertical"
          >
            <VWindowItem
              v-for="tab in tabs"
              :key="tab.paramType"
            >
              <div class="parameter-card">
                <p class="parameter-description">
                  {{ tab.subtitle }}
                </p>
                <div class="parameter-table-section">
                  <VDataTable
                    :headers="headers"
                    :items="parameterTables[tab.paramType].items"
                    hide-default-footer
                    class="elevation-0"
                    density="compact"
                  >
                    <template #item.actions="{ item }">
                      <VBtn
                        v-if="canEditPensionParameters"
                        icon
                        size="small"
                        variant="text"
                        color="primary"
                        @click="openEditDialog(item, tab.paramType)"
                      >
                        <VIcon
                          v-if="!item.editable"
                          size="16"
                          icon="tabler-alert-triangle"
                          class="edit-icon"
                          color="warning"
                        />
                        <VIcon
                          v-else
                          size="16"
                          icon="tabler-edit"
                          class="edit-icon"
                          color="primary"
                        />
                      </VBtn>
                      <span
                        v-else
                        class="text-disabled"
                      >Read Only</span>
                    </template>
                  </VDataTable>
                </div>
              </div>
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>

    <!-- Edit Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :type="ChangeType.Parameters"
      :current-value="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="selectedField.year"
      @close="closeEditDialog"
      @update="updateField"
    />

    <!-- Create Dialog -->
    <CreatePensionParametersDialog
      v-model="createDialog"
      @created="handleParametersCreated"
    />
  </VCard>
</template>

<style scoped>
  .parameter-card {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .parameter-description {
    margin: 0 0 16px 0;
    color: #666;
    font-size: 14px;
  }

  .parameter-table-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>
