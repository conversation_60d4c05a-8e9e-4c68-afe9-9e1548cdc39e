<script setup lang="ts">
import { ref, watch } from 'vue'
import { useQuery } from '@vue/apollo-composable'
import gql from 'graphql-tag'

const auditLogs = ref([])
const totalAuditLogs = ref(0)
const loading = ref(false)
const options = ref({})

const filterEntityType = ref(null)
const filterAction = ref(null)
const filterUserId = ref(null)

const entityTypes = [
  'CERTIFIED_DATA',
  'PARTICIPANT',
  'USER',

  // Add other entity types as needed
]

const actions = [
  'CREATE_CERTIFICATION',
  'UPDATE_CERTIFICATION',
  'DELETE_CERTIFICATION',
  'LOGIN',
  'LOGOUT',

  // Add other actions as needed
]

const headers = [
  { title: 'Timestamp', key: 'timestamp' },
  { title: 'User', key: 'user' },
  { title: 'Action', key: 'action' },
  { title: 'Entity Type', key: 'entityType' },
  { title: 'Entity ID', key: 'entityId' },
  { title: 'Changes', key: 'changes' },
]

const GET_AUDIT_LOGS = gql`
  query GetAuditLogs(
    $skip: Int,
    $take: Int,
    $entityType: String,
    $action: String,
    $userId: String
  ) {
    auditLogs(
      findAllAuditLogsInput: {
        skip: $skip,
        take: $take,
        entityType: $entityType,
        action: $action,
        userId: $userId
      }
    ) {
      items {
        id
        timestamp
        action
        entityType
        entityId
        changes
        user {
          id
          firstname
          lastname
          email
        }
      }
      totalCount
    }
  }
`

const { result, refetch } = useQuery(GET_AUDIT_LOGS, () => ({
  skip: options.value.page ? (options.value.page - 1) * options.value.itemsPerPage : 0,
  take: options.value.itemsPerPage || 10,
  entityType: filterEntityType.value,
  action: filterAction.value,
  userId: filterUserId.value,
}))

watch(result, newValue => {
  if (newValue) {
    auditLogs.value = newValue.auditLogs.items
    totalAuditLogs.value = newValue.auditLogs.totalCount
  }
})

watch([filterEntityType, filterAction, filterUserId], () => {
  refetch()
})

const updateOptions = (newOptions: any) => {
  options.value = newOptions
  refetch()
}
</script>

<template>
  <div>
    <h1>Audit Trail</h1>

    <VRow class="mb-4">
      <VCol
        cols="12"
        md="4"
      >
        <VSelect
          v-model="filterEntityType"
          :items="entityTypes"
          label="Filter by Entity Type"
          clearable
        />
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <VSelect
          v-model="filterAction"
          :items="actions"
          label="Filter by Action"
          clearable
        />
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <VTextField
          v-model="filterUserId"
          label="Filter by User ID"
          clearable
        />
      </VCol>
    </VRow>

    <VDataTable
      v-model:options="options"
      :headers="headers"
      :items="auditLogs"
      item-key="id"
      :loading="loading"
      :server-items-length="totalAuditLogs"
      @update:options="updateOptions"
    >
      <template #item.timestamp="{ item }">
        {{ new Date(item.timestamp).toLocaleString() }}
      </template>
      <template #item.user="{ item }">
        {{ item.user ? `${item.user.firstname} ${item.user.lastname} (${item.user.email})` : 'N/A' }}
      </template>
      <template #item.changes="{ item }">
        <pre>{{ JSON.stringify(item.changes, null, 2) }}</pre>
      </template>
    </VDataTable>
  </div>
</template>
