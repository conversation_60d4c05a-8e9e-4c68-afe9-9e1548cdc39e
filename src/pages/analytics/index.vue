<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useQuery } from '@vue/apollo-composable'
import gql from 'graphql-tag'

const totalParticipants = ref(0)
const completedCertifications = ref(0)
const inProgressCertifications = ref(0)
const pendingCertifications = ref(0)
const averageCompletionTime = ref(0)
const userProductivity = ref([])

const userProductivityHeaders = [
  { title: 'User', key: 'userName' },
  { title: 'Certifications Completed', key: 'completedCount' },
  { title: 'Certifications Started', key: 'startedCount' },
]

const GET_ANALYTICS_DATA = gql`
  query GetAnalyticsData {
    # Query for overall certification progress
    totalParticipants: auditLogs(findAllAuditLogsInput: { action: "CREATE_CERTIFICATION", entityType: "CERTIFIED_DATA" }) {
      totalCount
    }
    completedCertifications: auditLogs(findAllAuditLogsInput: { action: "UPDATE_CERTIFICATION_STATUS", changes: { status: "completed" } }) {
      totalCount
    }
    inProgressCertifications: auditLogs(findAllAuditLogsInput: { action: "UPDATE_CERTIFICATION_STATUS", changes: { status: "started" } }) {
      totalCount
    }
    pendingCertifications: auditLogs(findAllAuditLogsInput: { action: "CREATE_CERTIFICATION", entityType: "CERTIFIED_DATA" }) {
      totalCount
    }

    # Query for user productivity (this will need more sophisticated backend logic)
    # For now, we'll just show a placeholder
  }
`

const { result, loading, error } = useQuery(GET_ANALYTICS_DATA)

onMounted(() => {
  if (result.value) {
    totalParticipants.value = result.value.totalParticipants.totalCount
    completedCertifications.value = result.value.completedCertifications.totalCount
    inProgressCertifications.value = result.value.inProgressCertifications.totalCount
    pendingCertifications.value = result.value.pendingCertifications.totalCount - result.value.completedCertifications.totalCount - result.value.inProgressCertifications.totalCount

    // Placeholder for average completion time and user productivity
    averageCompletionTime.value = 0 // This will require more complex logic to calculate
    userProductivity.value = [
      { userId: 'user1', userName: 'John Doe', completedCount: 5, startedCount: 10 },
      { userId: 'user2', userName: 'Jane Smith', completedCount: 3, startedCount: 7 },
    ]
  }
})
</script>

<template>
  <div>
    <h1>Certification Analytics</h1>

    <VRow>
      <VCol
        cols="12"
        md="6"
      >
        <VCard>
          <VCardTitle>Overall Certification Progress</VCardTitle>
          <VCardText>
            <!-- Chart for overall progress -->
            <p>Total Participants: {{ totalParticipants }}</p>
            <p>Completed: {{ completedCertifications }} ({{ (completedCertifications / totalParticipants * 100).toFixed(2) }}%)</p>
            <p>In Progress: {{ inProgressCertifications }}</p>
            <p>Pending: {{ pendingCertifications }}</p>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard>
          <VCardTitle>Time-to-Complete Certification</VCardTitle>
          <VCardText>
            <!-- Chart for time-to-complete -->
            <p>Average Time: {{ averageCompletionTime }} days</p>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <VRow class="mt-4">
      <VCol cols="12">
        <VCard>
          <VCardTitle>User Productivity</VCardTitle>
          <VCardText>
            <!-- Table or chart for user productivity -->
            <VDataTable
              :headers="userProductivityHeaders"
              :items="userProductivity"
              item-key="userId"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
