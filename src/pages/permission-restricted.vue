<script setup lang="ts">
import { useRoute } from 'vue-router'
import { onMounted } from 'vue'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const route = useRoute()
const { currentRole } = useRoleAccess()

onMounted(() => {
  console.log(`Access denied for role ${currentRole.value} to route: ${route.query.route}`)
})
</script>

<template>
  <div class="permission-restricted-page">
    <PermissionRestrictedNotice />
    <!--    <div>Afia</div> -->
  </div>
</template>

<style scoped>
.permission-restricted-page {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
