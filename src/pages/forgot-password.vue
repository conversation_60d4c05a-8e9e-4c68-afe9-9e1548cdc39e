<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth/authStore'
import { useAppStore } from '@/stores/app/appStore'

const authStore = useAuthStore()
const appStore = useAppStore()

const email = ref('')

const handleResetPassword = async () => {
  try {
    await authStore.sendPasswordResetEmail(email.value)
    appStore.showSnack('Password reset email sent. Please check your inbox.')
  }
  catch (error) {
    appStore.showSnack('Error sending password reset email.')
  }
}
</script>

<template>
  <div class="auth-wrapper d-flex align-center justify-center pa-4">
    <VCard
      class="auth-card pa-4 pt-7"
      max-width="448"
    >
      <VCardItem class="justify-center">
        <VCardTitle class="font-weight-semibold text-2xl text-uppercase">
          Forgot Password
        </VCardTitle>
      </VCardItem>

      <VCardText class="pt-2">
        <p class="mb-0">
          Enter your email and we'll send you instructions to reset your password
        </p>
      </VCardText>

      <VCardText>
        <VForm @submit.prevent="handleResetPassword">
          <VRow>
            <!-- email -->
            <VCol cols="12">
              <VTextField
                v-model="email"
                label="Email"
                type="email"
                autofocus
              />
            </VCol>

            <!-- reset password button -->
            <VCol cols="12">
              <VBtn
                block
                type="submit"
              >
                Reset Password
              </VBtn>
            </VCol>

            <!-- back to login -->
            <VCol cols="12">
              <RouterLink
                class="d-flex align-center justify-center"
                :to="{ name: 'login' }"
              >
                <VIcon
                  icon="bx-chevron-left"
                  class="flip-in-rtl"
                />
                <span>Back to login</span>
              </RouterLink>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>

<!-- <style lang="scss"> -->
<!-- @use "@/styles/pages/auth.scss"; -->
<!-- </style> -->
