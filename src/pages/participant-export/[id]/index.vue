<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import { exportFieldGroups } from './exportFieldGroups'
import { exportTemplates } from './exportTemplates'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useParticipants } from '@/composables/participants/useParticipants'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'
import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
import { usePensionCalculations } from '@/composables/pension-calculations/usePensionCalculations'
import { useCertifiedData } from '@/composables/certified-data'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'
import { useRouteGuard } from '@/composables/auth/useRouteGuard'

const route = useRoute()
const router = useRouter()
const pensionStore = usePensionStore()
const appStore = useAppStore()
const { actions: { refetchSingleParticipant } } = useParticipants()
const { canExportData } = useRoleAccess()
const { checkActionAccess, shouldShowRestrictedPage } = useRouteGuard()

// Add pension calculation composables
const {
  hasCertifiedData,
  shouldShowCertified,
  getColumnYears,
  getStatusClass,
  formatCurrency,
  getColumnData,
} = usePensionBase()

onMounted(() => {
  checkActionAccess('export-data')
})

const { state: { normalizePensionParams } } = usePensionParameters()
const { state: { normalizedParticipantSalaryEntries } } = useParticipants()
const { state: { normalizedParticipantCertifiedData } } = useCertifiedData()
const { actions: { calculateOpTeAccrualToReferenceDate, calculateWpTeAccrualToReferenceDate, calculateOpTeAccrualAfterReferenceDate, calculateAccrualPeriodToReferenceDate, calculateAccrualPeriodAfterReferenceDate } } = usePensionCalculations()

const participantId = computed(() => route.params.id as string)
const activeParticipant = computed(() => pensionStore.activeParticipant)

// Loading state
const loading = ref(false)
const exporting = ref(false)

// Field selection states
const selectedFields = ref<string[]>([])

const groupSelections = ref({
  personalInfo: false,
  address: false,
  partnerInfo: false,
  children: false,
  employmentInfo: false,
  salaryEntries: false,
  pensionInfo: false,
  pensionEndOfPreviousYear: false,
  pensionsAfterCorrections: false,
  indexationBeginning: false,
  accrualPeriodCalculation: false,
  pensionsAccrual: false,
  pensionsAsPerReferenceDate: false,
})

// Field definitions grouped by category - imported from external file
const fieldGroups = ref(exportFieldGroups)

// Template definitions - imported from external file
const templates = ref(exportTemplates)

// Computed properties
const participantName = computed(() => {
  const participant = activeParticipant.value
  if (!participant?.personalInfo)
    return 'Unknown Participant'

  return `${participant.personalInfo.firstName} ${participant.personalInfo.lastName}`
})

const allFields = computed(() => {
  return fieldGroups.value.flatMap(group => group.fields.map(field => field.key))
})

const selectedFieldsCount = computed(() => selectedFields.value.length)
const totalFieldsCount = computed(() => allFields.value.length)

// Groups that automatically include complete table data
const pensionTableGroups = [
  'pensionEndOfPreviousYear',
  'pensionsAfterCorrections',
  'indexationBeginning',
  'accrualPeriodCalculation',
  'pensionsAccrual',
  'pensionsAsPerReferenceDate',
]

// Helper function to check if a field should be disabled
const isFieldDisabled = (groupKey: string) => {
  return pensionTableGroups.includes(groupKey)
}

// Pension calculation table data
const { leftColumnYear, middleColumnYear, rightColumnYear } = getColumnYears()

const pensionEndOfPreviousYearData = computed(() => {
  const getPensionInfoData = (year: number | null) => {
    if (year === null)
      return null
    const computedYear = year - 1

    return getColumnData(computedYear, 'certifiedPensionInfo')
  }

  const leftYear = String(leftColumnYear.value ?? '')
  const middleYear = String(middleColumnYear.value ?? '')
  const rightYear = String(rightColumnYear.value ?? '')

  return [
    {
      Description: 'Code',
      [leftYear]: getPensionInfoData(leftColumnYear.value)?.code ?? 'N/A',
      [middleYear]: getPensionInfoData(middleColumnYear.value)?.code ?? 'N/A',
      [rightYear]: getPensionInfoData(rightColumnYear.value)?.code ?? 'N/A',
    },
    {
      Description: 'OP-TE',
      [leftYear]: formatCurrency(getPensionInfoData(leftColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [middleYear]: formatCurrency(getPensionInfoData(middleColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [rightYear]: formatCurrency(getPensionInfoData(rightColumnYear.value)?.accruedGrossAnnualOldAgePension),
    },
    {
      Description: 'OP-TB',
      [leftYear]: formatCurrency(getPensionInfoData(leftColumnYear.value)?.attainableGrossAnnualOldAgePension),
      [middleYear]: formatCurrency(getPensionInfoData(middleColumnYear.value)?.attainableGrossAnnualOldAgePension),
      [rightYear]: formatCurrency(getPensionInfoData(rightColumnYear.value)?.attainableGrossAnnualOldAgePension),
    },
    {
      Description: 'WP-TE',
      [leftYear]: formatCurrency(getPensionInfoData(leftColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [middleYear]: formatCurrency(getPensionInfoData(middleColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [rightYear]: formatCurrency(getPensionInfoData(rightColumnYear.value)?.accruedGrossAnnualPartnersPension),
    },
    {
      Description: 'ONP-TE',
      [leftYear]: formatCurrency(getPensionInfoData(leftColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [middleYear]: formatCurrency(getPensionInfoData(middleColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [rightYear]: formatCurrency(getPensionInfoData(rightColumnYear.value)?.accruedGrossAnnualSinglesPension),
    },
    {
      Description: 'AOP-TE',
      [leftYear]: formatCurrency(getPensionInfoData(leftColumnYear.value)?.grossAnnualDisabilityPension),
      [middleYear]: formatCurrency(getPensionInfoData(middleColumnYear.value)?.grossAnnualDisabilityPension),
      [rightYear]: formatCurrency(getPensionInfoData(rightColumnYear.value)?.grossAnnualDisabilityPension),
    },
  ]
})

const pensionsAfterCorrectionsData = computed(() => {
  const getColumnDataForCorrections = (year: number | null) => {
    return getColumnData(year, 'certifiedPensionInfo')
  }

  const leftYear = String(leftColumnYear.value ?? '')
  const middleYear = String(middleColumnYear.value ?? '')
  const rightYear = String(rightColumnYear.value ?? '')

  return [
    {
      Description: 'OP-TE',
      [leftYear]: formatCurrency(getColumnDataForCorrections(leftColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [middleYear]: formatCurrency(getColumnDataForCorrections(middleColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [rightYear]: formatCurrency(getColumnDataForCorrections(rightColumnYear.value)?.accruedGrossAnnualOldAgePension),
    },
    {
      Description: 'WP-TE',
      [leftYear]: formatCurrency(getColumnDataForCorrections(leftColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [middleYear]: formatCurrency(getColumnDataForCorrections(middleColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [rightYear]: formatCurrency(getColumnDataForCorrections(rightColumnYear.value)?.accruedGrossAnnualPartnersPension),
    },
    {
      Description: 'ONP-TE',
      [leftYear]: formatCurrency(getColumnDataForCorrections(leftColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [middleYear]: formatCurrency(getColumnDataForCorrections(middleColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [rightYear]: formatCurrency(getColumnDataForCorrections(rightColumnYear.value)?.accruedGrossAnnualSinglesPension),
    },
  ]
})

const indexationBeginningData = computed(() => {
  const getIndexationData = (year: number | null) => {
    return getColumnData(year, 'certifiedIndexationStartOfYear')
  }

  const leftYear = String(leftColumnYear.value ?? '')
  const middleYear = String(middleColumnYear.value ?? '')
  const rightYear = String(rightColumnYear.value ?? '')

  return [
    {
      Description: 'OP-TE',
      [leftYear]: formatCurrency(getIndexationData(leftColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [middleYear]: formatCurrency(getIndexationData(middleColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [rightYear]: formatCurrency(getIndexationData(rightColumnYear.value)?.accruedGrossAnnualOldAgePension),
    },
    {
      Description: 'WP-TE',
      [leftYear]: formatCurrency(getIndexationData(leftColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [middleYear]: formatCurrency(getIndexationData(middleColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [rightYear]: formatCurrency(getIndexationData(rightColumnYear.value)?.accruedGrossAnnualPartnersPension),
    },
    {
      Description: 'ONP-TE',
      [leftYear]: formatCurrency(getIndexationData(leftColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [middleYear]: formatCurrency(getIndexationData(middleColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [rightYear]: formatCurrency(getIndexationData(rightColumnYear.value)?.accruedGrossAnnualSinglesPension),
    },
  ]
})

const accrualPeriodCalculationData = computed(() => {
  const participant = activeParticipant.value
  if (!participant?.personalInfo || !participant?.employmentInfo)
    return []

  const formatBirthDate = (personalInfo: any) => {
    if (!personalInfo)
      return ''
    const month = personalInfo.birthMonth || 0
    const monthName = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][(month - 1) % 12]
    const day = personalInfo.birthDay || 0
    const year = personalInfo.birthYear || 0

    return `${monthName} ${day}, ${year}`
  }

  const formatDate = (date: string) => {
    if (!date)
      return '-'
    const parsedDate = new Date(date)
    const day = parsedDate.getDate()
    const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][parsedDate.getMonth()]
    const year = parsedDate.getFullYear()

    return `${month} ${day}, ${year}`
  }

  const leftYear = String(leftColumnYear.value ?? '')
  const middleYear = String(middleColumnYear.value ?? '')
  const rightYear = String(rightColumnYear.value ?? '')

  return [
    {
      Description: 'Start date of employment',
      [leftYear]: formatDate(participant.employmentInfo.startDate || ''),
      [middleYear]: formatDate(participant.employmentInfo.startDate || ''),
      [rightYear]: formatDate(participant.employmentInfo.startDate || ''),
    },
    {
      Description: 'End date of employment',
      [leftYear]: formatDate(participant.employmentInfo.endDate || '') || '-',
      [middleYear]: formatDate(participant.employmentInfo.endDate || '') || '-',
      [rightYear]: formatDate(participant.employmentInfo.endDate || '') || '-',
    },
    {
      Description: 'Birth date',
      [leftYear]: formatBirthDate(participant.personalInfo),
      [middleYear]: formatBirthDate(participant.personalInfo),
      [rightYear]: formatBirthDate(participant.personalInfo),
    },
    {
      Description: 'AOV age',
      [leftYear]: '65',
      [middleYear]: '65',
      [rightYear]: '65',
    },
  ]
})

const pensionsAccrualData = computed(() => {
  const leftYear = String(leftColumnYear.value ?? '')
  const middleYear = String(middleColumnYear.value ?? '')
  const rightYear = String(rightColumnYear.value ?? '')

  return [
    {
      Description: 'Accrual Percentage',
      [leftYear]: '1.75%',
      [middleYear]: '1.75%',
      [rightYear]: '1.75%',
    },
    {
      Description: 'Pension Base',
      [leftYear]: formatCurrency(0),
      [middleYear]: formatCurrency(0),
      [rightYear]: formatCurrency(0),
    },
  ]
})

const pensionsAsPerReferenceDateData = computed(() => {
  const getPensionReferenceData = (year: number | null) => {
    return getColumnData(year, 'certifiedPensionInfo')
  }

  const leftYear = String(leftColumnYear.value ?? '')
  const middleYear = String(middleColumnYear.value ?? '')
  const rightYear = String(rightColumnYear.value ?? '')

  return [
    {
      Description: 'OP-TE',
      [leftYear]: formatCurrency(getPensionReferenceData(leftColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [middleYear]: formatCurrency(getPensionReferenceData(middleColumnYear.value)?.accruedGrossAnnualOldAgePension),
      [rightYear]: formatCurrency(getPensionReferenceData(rightColumnYear.value)?.accruedGrossAnnualOldAgePension),
    },
    {
      Description: 'WP-TE',
      [leftYear]: formatCurrency(getPensionReferenceData(leftColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [middleYear]: formatCurrency(getPensionReferenceData(middleColumnYear.value)?.accruedGrossAnnualPartnersPension),
      [rightYear]: formatCurrency(getPensionReferenceData(rightColumnYear.value)?.accruedGrossAnnualPartnersPension),
    },
    {
      Description: 'ONP-TE',
      [leftYear]: formatCurrency(getPensionReferenceData(leftColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [middleYear]: formatCurrency(getPensionReferenceData(middleColumnYear.value)?.accruedGrossAnnualSinglesPension),
      [rightYear]: formatCurrency(getPensionReferenceData(rightColumnYear.value)?.accruedGrossAnnualSinglesPension),
    },
    {
      Description: 'AOP-TE',
      [leftYear]: formatCurrency(getPensionReferenceData(leftColumnYear.value)?.grossAnnualDisabilityPension),
      [middleYear]: formatCurrency(getPensionReferenceData(middleColumnYear.value)?.grossAnnualDisabilityPension),
      [rightYear]: formatCurrency(getPensionReferenceData(rightColumnYear.value)?.grossAnnualDisabilityPension),
    },
  ]
})

// Methods
const toggleGroupSelection = (groupKey: string) => {
  const group = fieldGroups.value.find(g => g.key === groupKey)
  if (!group)
    return

  const groupFields = group.fields.map(f => f.key)
  const isGroupSelected = groupSelections.value[groupKey as keyof typeof groupSelections.value]

  if (isGroupSelected) {
    // Deselect all fields in this group
    selectedFields.value = selectedFields.value.filter(field => !groupFields.includes(field))
  }
  else {
    // Select all fields in this group
    groupFields.forEach(field => {
      if (!selectedFields.value.includes(field))
        selectedFields.value.push(field)
    })
  }

  groupSelections.value[groupKey as keyof typeof groupSelections.value] = !isGroupSelected
}

const toggleFieldSelection = (fieldKey: string) => {
  const index = selectedFields.value.indexOf(fieldKey)
  if (index > -1)
    selectedFields.value.splice(index, 1)
  else
    selectedFields.value.push(fieldKey)

  // Update group selection state
  updateGroupSelectionStates()
}

const updateGroupSelectionStates = () => {
  fieldGroups.value.forEach(group => {
    const groupFields = group.fields.map(f => f.key)
    const selectedGroupFields = selectedFields.value.filter(field => groupFields.includes(field))

    groupSelections.value[group.key as keyof typeof groupSelections.value] = selectedGroupFields.length === groupFields.length
  })
}

const selectAllFields = () => {
  selectedFields.value = [...allFields.value]
  Object.keys(groupSelections.value).forEach(key => {
    groupSelections.value[key as keyof typeof groupSelections.value] = true
  })
}

const clearAllSelections = () => {
  selectedFields.value = []
  Object.keys(groupSelections.value).forEach(key => {
    groupSelections.value[key as keyof typeof groupSelections.value] = false
  })
}

const getFieldValue = (participant: any, fieldKey: string): any => {
  const keys = fieldKey.split('.')
  let value = participant

  // Handle special computed fields
  if (fieldKey === 'personalInfo.fullBirthDate') {
    const personalInfo = participant.personalInfo
    if (personalInfo?.birthYear && personalInfo?.birthMonth && personalInfo?.birthDay)
      return `${personalInfo.birthYear}-${String(personalInfo.birthMonth).padStart(2, '0')}-${String(personalInfo.birthDay).padStart(2, '0')}`

    return ''
  }

  // Handle worksheet fields
  if (fieldKey.includes('.worksheet'))
    return 'See separate worksheet'

  // Handle pension calculation row fields
  if (fieldKey.startsWith('pensionEndOfPreviousYear.')) {
    const rowType = fieldKey.split('.')[1]
    const data = pensionEndOfPreviousYearData.value
    if (rowType === 'fullTable')
      return 'See pension calculation table'

    const rowData = data.find(row => {
      switch (rowType) {
        case 'code': return row.Description === 'Code'
        case 'opte': return row.Description === 'OP-TE'
        case 'optb': return row.Description === 'OP-TB'
        case 'wpte': return row.Description === 'WP-TE'
        case 'onpte': return row.Description === 'ONP-TE'
        case 'aopte': return row.Description === 'AOP-TE'
        default: return false
      }
    })

    return rowData ? `${rowData[String(leftColumnYear.value)]} | ${rowData[String(middleColumnYear.value)]} | ${rowData[String(rightColumnYear.value)]}` : ''
  }

  if (fieldKey.startsWith('pensionsAfterCorrections.')) {
    const rowType = fieldKey.split('.')[1]
    const data = pensionsAfterCorrectionsData.value
    if (rowType === 'fullTable')
      return 'See pension calculation table'

    const rowData = data.find(row => {
      switch (rowType) {
        case 'opte': return row.Description === 'OP-TE'
        case 'wpte': return row.Description === 'WP-TE'
        case 'onpte': return row.Description === 'ONP-TE'
        default: return false
      }
    })

    return rowData ? `${rowData[String(leftColumnYear.value)]} | ${rowData[String(middleColumnYear.value)]} | ${rowData[String(rightColumnYear.value)]}` : ''
  }

  if (fieldKey.startsWith('indexationBeginning.')) {
    const rowType = fieldKey.split('.')[1]
    const data = indexationBeginningData.value
    if (rowType === 'fullTable')
      return 'See pension calculation table'

    const rowData = data.find(row => {
      switch (rowType) {
        case 'opte': return row.Description === 'OP-TE'
        case 'wpte': return row.Description === 'WP-TE'
        case 'onpte': return row.Description === 'ONP-TE'
        default: return false
      }
    })

    return rowData ? `${rowData[String(leftColumnYear.value)]} | ${rowData[String(middleColumnYear.value)]} | ${rowData[String(rightColumnYear.value)]}` : ''
  }

  if (fieldKey.startsWith('accrualPeriodCalculation.')) {
    const rowType = fieldKey.split('.')[1]
    const data = accrualPeriodCalculationData.value
    if (rowType === 'fullTable')
      return 'See pension calculation table'

    const rowData = data.find(row => {
      switch (rowType) {
        case 'startDate': return row.Description === 'Start date of employment'
        case 'endDate': return row.Description === 'End date of employment'
        case 'birthDate': return row.Description === 'Birth date'
        case 'aovAge': return row.Description === 'AOV age'
        default: return false
      }
    })

    return rowData ? `${rowData[String(leftColumnYear.value)]} | ${rowData[String(middleColumnYear.value)]} | ${rowData[String(rightColumnYear.value)]}` : ''
  }

  if (fieldKey.startsWith('pensionsAccrual.')) {
    const rowType = fieldKey.split('.')[1]
    const data = pensionsAccrualData.value
    if (rowType === 'fullTable')
      return 'See pension calculation table'

    const rowData = data.find(row => {
      switch (rowType) {
        case 'accrualPercentage': return row.Description === 'Accrual Percentage'
        case 'pensionBase': return row.Description === 'Pension Base'
        default: return false
      }
    })

    return rowData ? `${rowData[String(leftColumnYear.value)]} | ${rowData[String(middleColumnYear.value)]} | ${rowData[String(rightColumnYear.value)]}` : ''
  }

  if (fieldKey.startsWith('pensionsAsPerReferenceDate.')) {
    const rowType = fieldKey.split('.')[1]
    const data = pensionsAsPerReferenceDateData.value
    if (rowType === 'fullTable')
      return 'See pension calculation table'

    const rowData = data.find(row => {
      switch (rowType) {
        case 'opte': return row.Description === 'OP-TE'
        case 'wpte': return row.Description === 'WP-TE'
        case 'onpte': return row.Description === 'ONP-TE'
        case 'aopte': return row.Description === 'AOP-TE'
        default: return false
      }
    })

    return rowData ? `${rowData[String(leftColumnYear.value)]} | ${rowData[String(middleColumnYear.value)]} | ${rowData[String(rightColumnYear.value)]}` : ''
  }

  // Handle legacy array fields (now handled as worksheets)
  if (keys.includes('partners') || keys.includes('children') || keys.includes('salaryEntries'))
    return 'See separate worksheet'

  // Handle address fields
  if (keys[0] === 'address') {
    value = participant.personalInfo?.address
    keys.shift() // Remove 'address' from keys
  }

  // Navigate through object properties
  for (const key of keys) {
    value = value?.[key]
    if (value === undefined || value === null)
      break
  }

  return value ?? ''
}

const formatDate = (dateString: string | Date): string => {
  if (!dateString)
    return ''
  const date = new Date(dateString)

  return date.toISOString().split('T')[0]
}

const exportToExcel = () => {
  if (selectedFields.value.length === 0) {
    appStore.showSnack('❌ Please select at least one field to export')

    return
  }

  if (!activeParticipant.value) {
    appStore.showSnack('❌ No participant data available for export')

    return
  }

  exporting.value = true

  try {
    const participant = activeParticipant.value
    const workbook = XLSX.utils.book_new()

    // Helper to force cell type to string for dates
    const forceDateCellsToString = (worksheet: XLSX.WorkSheet, data: any[], dateHeaders: string[]) => {
      if (!data.length)
        return
      const headerKeys = Object.keys(data[0])
      const dateColumnIndices = dateHeaders.map(h => headerKeys.indexOf(h)).filter(i => i !== -1)

      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
      for (let R = range.s.r + 1; R <= range.e.r; ++R) {
        for (const C of dateColumnIndices) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C })
          const cell = worksheet[cellAddress]
          if (cell) {
            cell.t = 's' // Set type to string
            cell.z = '@' // Set format to text
          }
        }
      }
    }

    // Check for pension calculation groups and automatically include table data
    const selectedPensionGroups = pensionTableGroups.filter(groupKey =>
      groupSelections.value[groupKey as keyof typeof groupSelections.value],
    )

    if (selectedPensionGroups.length > 0) {
      // Create separate worksheets for pension calculation tables
      const tableMapping = {
        pensionEndOfPreviousYear: {
          data: pensionEndOfPreviousYearData.value,
          name: 'Pension End Previous Year',
        },
        pensionsAfterCorrections: {
          data: pensionsAfterCorrectionsData.value,
          name: 'Pensions After Corrections',
        },
        indexationBeginning: {
          data: indexationBeginningData.value,
          name: 'Indexation Beginning',
        },
        accrualPeriodCalculation: {
          data: accrualPeriodCalculationData.value,
          name: 'Accrual Period Calculation',
        },
        pensionsAccrual: {
          data: pensionsAccrualData.value,
          name: 'Pensions Accrual',
        },
        pensionsAsPerReferenceDate: {
          data: pensionsAsPerReferenceDateData.value,
          name: 'Pensions Reference Date',
        },
      }

      // Add each selected group's table as a separate worksheet
      selectedPensionGroups.forEach(groupKey => {
        const tableInfo = tableMapping[groupKey as keyof typeof tableMapping]
        if (tableInfo && tableInfo.data) {
          const worksheet = XLSX.utils.json_to_sheet(tableInfo.data)

          // Apply styling to the worksheet
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')

          // Style header row
          for (let col = range.s.c; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
            if (!worksheet[cellAddress])
              continue

            worksheet[cellAddress].s = {
              font: { bold: true, color: { rgb: 'FFFFFF' } },
              fill: { bgColor: { rgb: '366092' } },
              alignment: { horizontal: 'center' },
            }
          }

          // Set column widths
          const colWidths = tableInfo.data.length > 0
            ? Object.keys(tableInfo.data[0]).map(() => ({ width: 20 }))
            : []

          worksheet['!cols'] = colWidths

          XLSX.utils.book_append_sheet(workbook, worksheet, tableInfo.name)
        }
      })
    }

    // Create worksheets for multi-row data
    // Partners worksheet
    if (selectedFields.value.includes('partners.worksheet')) {
      const partnersData = participant.personalInfo?.partnerInfo || []
      if (partnersData.length > 0) {
        const partnersForExport = partnersData.map((partner: any) => ({
          'First Name': partner.firstName || '',
          'Last Name': partner.lastName || '',
          'Date of Birth': formatDate(partner.dateOfBirth || ''),
          'Start Date': formatDate(partner.startDate || ''),
          'Is Current': partner.isCurrent ? 'Yes' : 'No',
          'Is Deceased': partner.isDeceased ? 'Yes' : 'No',
        }))

        const partnersWorksheet = XLSX.utils.json_to_sheet(partnersForExport)

        forceDateCellsToString(partnersWorksheet, partnersForExport, ['Date of Birth', 'Start Date'])

        // Style partners worksheet
        const range = XLSX.utils.decode_range(partnersWorksheet['!ref'] || 'A1')
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
          if (!partnersWorksheet[cellAddress])
            continue

          partnersWorksheet[cellAddress].s = {
            font: { bold: true, color: { rgb: 'FFFFFF' } },
            fill: { bgColor: { rgb: 'E74C3C' } },
            alignment: { horizontal: 'center' },
          }
        }

        partnersWorksheet['!cols'] = [
          { width: 15 },
          { width: 15 },
          { width: 15 },
          { width: 15 },
          { width: 12 },
          { width: 12 },
        ]

        XLSX.utils.book_append_sheet(workbook, partnersWorksheet, 'Partners')
      }
    }

    // Children worksheet
    if (selectedFields.value.includes('children.worksheet')) {
      const childrenData = participant.personalInfo?.children || []
      if (childrenData.length > 0) {
        const childrenForExport = childrenData.map((child: any) => ({
          'First Name': child.firstName || '',
          'Last Name': child.lastName || '',
          'Date of Birth': formatDate(child.dateOfBirth || ''),
          'Is Orphan': child.isOrphan ? 'Yes' : 'No',
          'Is Studying': child.isStudying ? 'Yes' : 'No',
        }))

        const childrenWorksheet = XLSX.utils.json_to_sheet(childrenForExport)

        forceDateCellsToString(childrenWorksheet, childrenForExport, ['Date of Birth'])

        // Style children worksheet
        const range = XLSX.utils.decode_range(childrenWorksheet['!ref'] || 'A1')
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
          if (!childrenWorksheet[cellAddress])
            continue

          childrenWorksheet[cellAddress].s = {
            font: { bold: true, color: { rgb: 'FFFFFF' } },
            fill: { bgColor: { rgb: 'F39C12' } },
            alignment: { horizontal: 'center' },
          }
        }

        childrenWorksheet['!cols'] = [
          { width: 15 },
          { width: 15 },
          { width: 15 },
          { width: 12 },
          { width: 12 },
        ]

        XLSX.utils.book_append_sheet(workbook, childrenWorksheet, 'Children')
      }
    }

    // Salary Entries worksheet
    if (selectedFields.value.includes('salaryEntries.worksheet')) {
      const salaryData = participant.employmentInfo?.salaryEntries || []
      if (salaryData.length > 0) {
        const salaryForExport = salaryData.map((entry: any) => ({
          'Year': entry.year || '',
          'Amount': entry.amount || 0,
          'Part-time Percentage': `${((entry.partTimePercentage || 1) * 100).toFixed(0)}%`,
        }))

        const salaryWorksheet = XLSX.utils.json_to_sheet(salaryForExport)

        // Style salary worksheet
        const range = XLSX.utils.decode_range(salaryWorksheet['!ref'] || 'A1')
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
          if (!salaryWorksheet[cellAddress])
            continue

          salaryWorksheet[cellAddress].s = {
            font: { bold: true, color: { rgb: 'FFFFFF' } },
            fill: { bgColor: { rgb: '27AE60' } },
            alignment: { horizontal: 'center' },
          }
        }

        salaryWorksheet['!cols'] = [
          { width: 10 }, { width: 15 }, { width: 20 },
        ]

        XLSX.utils.book_append_sheet(workbook, salaryWorksheet, 'Salary Entries')
      }
    }

    // Create main participant data worksheet for non-worksheet fields
    const excludeFields = [
      ...selectedFields.value.filter(field => field.includes('.worksheet')),
    ]

    const mainDataFields = selectedFields.value.filter(field => !excludeFields.includes(field))

    if (mainDataFields.length > 0) {
      const exportData: any[] = []
      const row: any = {}
      const dateHeaders: string[] = []

      mainDataFields.forEach(fieldKey => {
        let value = getFieldValue(participant, fieldKey)

        // Clean field name for header
        const cleanFieldName = fieldKey.split('.').pop() || fieldKey
        const headerName = cleanFieldName.charAt(0).toUpperCase() + cleanFieldName.slice(1).replace(/([A-Z])/g, ' $1')

        // Format dates and identify date columns
        if ((fieldKey.toLowerCase().includes('date') || fieldKey === 'personalInfo.fullBirthDate') && value) {
          value = formatDate(value)
          if (!dateHeaders.includes(headerName))
            dateHeaders.push(headerName)
        }

        row[headerName] = value
      })

      if (Object.keys(row).length > 0) {
        exportData.push(row)

        // Create main participant data worksheet
        const mainWorksheet = XLSX.utils.json_to_sheet(exportData)

        forceDateCellsToString(mainWorksheet, exportData, dateHeaders)

        // Apply styling to main worksheet
        const range = XLSX.utils.decode_range(mainWorksheet['!ref'] || 'A1')

        // Style header row
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
          if (!mainWorksheet[cellAddress])
            continue

          mainWorksheet[cellAddress].s = {
            font: { bold: true, color: { rgb: 'FFFFFF' } },
            fill: { bgColor: { rgb: '4472C4' } },
            alignment: { horizontal: 'center' },
          }
        }

        // Set column widths
        const colWidths = Object.keys(row).map(() => ({ width: 15 }))

        mainWorksheet['!cols'] = colWidths

        XLSX.utils.book_append_sheet(workbook, mainWorksheet, 'Participant Data')
      }
    }

    // Generate filename with participant name and current date
    const today = new Date().toISOString().split('T')[0]
    const safeParticipantName = participantName.value.replace(/[^a-z0-9]/gi, '_')
    const filename = `participant_${safeParticipantName}_${today}.xlsx`

    // Download the file
    XLSX.writeFile(workbook, filename)

    const worksheetCount = workbook.SheetNames.length

    appStore.showSnack(`✅ Excel file exported successfully with ${worksheetCount} worksheet${worksheetCount !== 1 ? 's' : ''}: ${filename}`)
  }
  catch (error) {
    console.error('Export error:', error)
    appStore.showSnack('❌ Failed to export Excel file. Please try again.')
  }
  finally {
    exporting.value = false
  }
}

const goBack = () => {
  router.back()
}

// Add new method for applying templates
const applyTemplate = (template: any) => {
  // Clear current selections
  clearAllSelections()

  if (template.name === 'Complete Report') {
    // Select all fields for complete report
    selectAllFields()
  }
  else {
    // Select specific fields from template
    selectedFields.value = [...template.fields]

    // Update group selections based on template groups
    template.groups.forEach((groupKey: string) => {
      groupSelections.value[groupKey as keyof typeof groupSelections.value] = true
    })

    // Update group selection states for partial selections
    updateGroupSelectionStates()
  }

  appStore.showSnack(`✅ Applied "${template.name}" - ${selectedFields.value.length} fields selected`)
}

// Initialize
onMounted(async () => {
  loading.value = true
  try {
    await refetchSingleParticipant()
  }
  catch (error) {
    console.error('Error fetching participant data:', error)
    appStore.showSnack('❌ Failed to load participant data')
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="participant-export-page">
    <PermissionRestricted v-if="shouldShowRestrictedPage" />
    <div v-else>
      <SimpleBreadcrumbs
        :items="[
          { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
          { title: 'Participants', to: '/participants', icon: 'tabler-users' },
          { title: 'Participant', to: `/participants/${participantId}`, icon: 'tabler-user' },
          { title: 'Export Data', disabled: true, icon: 'tabler-download' },
        ]"
      />
      <!-- Page Header -->
      <VCard class="mb-6">
        <VCardText>
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="d-flex align-center mb-2">
                <VBtn
                  icon="tabler-arrow-left"
                  variant="text"
                  class="mr-3"
                  @click="goBack"
                />
                <div>
                  <h1 class="text-h4 font-weight-bold">
                    Export Participant Data
                  </h1>
                  <p class="text-body-1 text-medium-emphasis mb-0">
                    Select the fields you want to export for <strong>{{ participantName }}</strong>
                  </p>
                </div>
              </div>
            </div>
            <VChip
              v-if="selectedFieldsCount > 0"
              color="primary"
              variant="tonal"
              size="large"
            >
              {{ selectedFieldsCount }} / {{ totalFieldsCount }} fields selected
            </VChip>
          </div>
        </VCardText>
      </VCard>

      <!-- Loading State -->
      <div
        v-if="loading"
        class="text-center py-12"
      >
        <VProgressCircular
          indeterminate
          color="primary"
          size="64"
        />
        <p class="text-body-1 mt-4">
          Loading participant data...
        </p>
      </div>

      <!-- Main Content -->
      <div v-else>
        <!-- Quick Templates and Actions -->
        <VCard class="mb-6">
          <VCardText>
            <div class="mb-4">
              <h3 class="text-h6 font-weight-bold mb-1">
                Quick Templates & Actions
              </h3>
              <p class="text-body-2 text-medium-emphasis">
                Use predefined templates to quickly select common field combinations or manage your selections
              </p>
            </div>

            <div class="d-flex flex-wrap ga-3">
              <!-- Template Chips -->
              <VChip
                v-for="template in templates"
                :key="template.name"
                :color="template.color"
                variant="outlined"
                size="large"
                clickable
                class="template-chip"
                @click="applyTemplate(template)"
              >
                <template #prepend>
                  <VIcon
                    :icon="template.icon"
                    size="18"
                    class="mr-1"
                  />
                </template>

                <div class="d-flex flex-column align-start">
                  <span class="font-weight-bold">{{ template.name }}</span>
                  <span class="text-caption text-medium-emphasis">{{ template.description }}</span>
                </div>
              </VChip>

              <!-- Clear All Action Chip -->
              <VChip
                color="error"
                variant="outlined"
                size="large"
                clickable
                class="template-chip action-chip"
                @click="clearAllSelections"
              >
                <template #prepend>
                  <VIcon
                    icon="tabler-x"
                    size="18"
                    class="mr-1"
                  />
                </template>

                <div class="d-flex flex-column align-start">
                  <span class="font-weight-bold">Clear All</span>
                  <span class="text-caption text-medium-emphasis">Remove all field selections</span>
                </div>
              </VChip>
            </div>

            <VAlert
              type="info"
              variant="tonal"
              density="compact"
              class="mt-4"
            >
              <template #prepend>
                <VIcon
                  icon="tabler-info-circle"
                  size="16"
                />
              </template>
              <span class="text-body-2">
                Click any template to automatically select the relevant fields, or use "Clear All" to start fresh. You can still customize your selection afterwards.
              </span>
            </VAlert>
          </VCardText>
        </VCard>

        <!-- Field Groups -->
        <VRow>
          <VCol
            v-for="group in fieldGroups"
            :key="group.key"
            cols="12"
            md="6"
            lg="4"
          >
            <VCard
              class="field-group-card h-100"
              :class="[
                { selected: groupSelections[group.key as keyof typeof groupSelections] },
              ]"
            >
              <VCardText>
                <!-- Group Header -->
                <div class="d-flex align-center justify-space-between mb-4">
                  <div class="d-flex align-center">
                    <VIcon
                      :icon="group.icon"
                      size="24"
                      class="mr-3"
                      :color="groupSelections[group.key as keyof typeof groupSelections] ? 'primary' : 'default'"
                    />
                    <div>
                      <h4 class="text-h6 font-weight-bold">
                        {{ group.title }}
                      </h4>
                      <p class="text-caption text-medium-emphasis mb-0">
                        {{ group.description }}
                      </p>
                    </div>
                  </div>
                  <VSwitch
                    :model-value="groupSelections[group.key as keyof typeof groupSelections]"
                    color="primary"
                    hide-details
                    @update:model-value="toggleGroupSelection(group.key)"
                  />
                </div>

                <!-- Individual Fields -->
                <div class="fields-list">
                  <VCheckbox
                    v-for="field in group.fields"
                    :key="field.key"
                    :model-value="selectedFields.includes(field.key)"
                    :label="field.label"
                    :disabled="isFieldDisabled(group.key)"
                    color="primary"
                    density="compact"
                    hide-details
                    @update:model-value="toggleFieldSelection(field.key)"
                  />
                </div>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>

        <!-- Export Actions -->
        <VCard class="mt-6">
          <VCardText>
            <div class="d-flex align-center justify-space-between flex-wrap ga-4">
              <div>
                <h3 class="text-h6 font-weight-bold mb-1">
                  Ready to Export?
                </h3>
                <p class="text-body-2 text-medium-emphasis">
                  <span
                    v-if="selectedFieldsCount === 0"
                    class="text-warning"
                  >
                    Please select at least one field to export
                  </span>
                  <span v-else>
                    {{ selectedFieldsCount }} field{{ selectedFieldsCount !== 1 ? 's' : '' }} selected for export
                  </span>
                </p>
              </div>
              <VBtn
                color="success"
                variant="elevated"
                size="large"
                prepend-icon="tabler-download"
                :disabled="selectedFieldsCount === 0"
                :loading="exporting"
                @click="exportToExcel"
              >
                Export to Excel
              </VBtn>
            </div>
          </VCardText>
        </VCard>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .participant-export-page {
    .field-group-card {
      border: 2px solid transparent;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgb(var(--v-theme-primary));
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(var(--v-theme-primary), 0.1);
      }

      &.selected {
        border-color: rgb(var(--v-theme-primary));
        background-color: rgba(var(--v-theme-primary), 0.05);
      }
    }

    .fields-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .template-chip {
      min-height: 60px !important;
      padding: 8px 16px !important;
      border-radius: 12px !important;
      transition: all 0.3s ease;
      cursor: pointer;
      min-width: 200px;
      max-width: 280px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }

      // Special styling for action chips
      &.action-chip {
        &:hover {
          background-color: rgba(var(--v-theme-error), 0.1);
          border-color: rgb(var(--v-theme-error));
        }
      }

      .v-chip__content {
        width: 100%;

        .d-flex {
          width: 100%;
          text-align: left;
          line-height: 1.3;

          .font-weight-bold {
            font-size: 0.875rem;
            margin-bottom: 2px;
          }

          .text-caption {
            font-size: 0.75rem;
            opacity: 0.8;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 160px;
          }
        }
      }

      // Color-specific hover effects
      &.v-chip--variant-outlined {
        &.v-chip--color-success:hover {
          background-color: rgba(var(--v-theme-success), 0.1);
          border-color: rgb(var(--v-theme-success));
        }

        &.v-chip--color-primary:hover {
          background-color: rgba(var(--v-theme-primary), 0.1);
          border-color: rgb(var(--v-theme-primary));
        }

        &.v-chip--color-warning:hover {
          background-color: rgba(var(--v-theme-warning), 0.1);
          border-color: rgb(var(--v-theme-warning));
        }

        &.v-chip--color-info:hover {
          background-color: rgba(var(--v-theme-info), 0.1);
          border-color: rgb(var(--v-theme-info));
        }

        &.v-chip--color-secondary:hover {
          background-color: rgba(var(--v-theme-secondary), 0.1);
          border-color: rgb(var(--v-theme-secondary));
        }

        &.v-chip--color-error:hover {
          background-color: rgba(var(--v-theme-error), 0.1);
          border-color: rgb(var(--v-theme-error));
        }
      }
    }

    // Responsive adjustments
    @media (max-width: 768px) {
      .template-chip {
        min-width: 160px;
        max-width: 100%;

        .v-chip__content .d-flex .text-caption {
          max-width: 120px;
        }
      }
    }
  }
</style>
