export const exportFieldGroups = [
  {
    key: 'personalInfo',
    title: 'Personal Information',
    icon: 'tabler-user',
    description: 'Basic personal details of the participant',
    fields: [
      { key: 'personalInfo.firstName', label: 'First Name', type: 'text' },
      { key: 'personalInfo.lastName', label: 'Last Name', type: 'text' },
      { key: 'personalInfo.email', label: 'Email', type: 'text' },
      { key: 'personalInfo.phone', label: 'Phone', type: 'text' },
      { key: 'personalInfo.maritalStatus', label: 'Marital Status', type: 'text' },
      { key: 'personalInfo.birthDay', label: 'Birth Day', type: 'number' },
      { key: 'personalInfo.birthMonth', label: 'Birth Month', type: 'number' },
      { key: 'personalInfo.birthYear', label: 'Birth Year', type: 'number' },
      { key: 'personalInfo.fullBirthDate', label: 'Full Birth Date', type: 'computed' },
    ],
  },
  {
    key: 'address',
    title: 'Address Information',
    icon: 'tabler-map-pin',
    description: 'Current residential address details',
    fields: [
      { key: 'address.street', label: 'Street', type: 'text' },
      { key: 'address.houseNumber', label: 'House Number', type: 'text' },
      { key: 'address.postalCode', label: 'Postal Code', type: 'text' },
      { key: 'address.city', label: 'City', type: 'text' },
      { key: 'address.state', label: 'State', type: 'text' },
      { key: 'address.country', label: 'Country', type: 'text' },
    ],
  },
  {
    key: 'partnerInfo',
    title: 'Partner Information',
    icon: 'tabler-users',
    description: 'Current and former partner details',
    fields: [
      { key: 'partners.firstName', label: 'Partner First Name', type: 'text' },
      { key: 'partners.lastName', label: 'Partner Last Name', type: 'text' },
      { key: 'partners.dateOfBirth', label: 'Partner Date of Birth', type: 'date' },
      { key: 'partners.startDate', label: 'Partnership Start Date', type: 'date' },
      { key: 'partners.isCurrent', label: 'Is Current Partner', type: 'boolean' },
      { key: 'partners.isDeceased', label: 'Is Deceased', type: 'boolean' },
    ],
  },
  {
    key: 'children',
    title: 'Children Information',
    icon: 'tabler-baby-carriage',
    description: 'Information about participant\'s children',
    fields: [
      { key: 'children.firstName', label: 'Child First Name', type: 'text' },
      { key: 'children.lastName', label: 'Child Last Name', type: 'text' },
      { key: 'children.dateOfBirth', label: 'Child Date of Birth', type: 'date' },
      { key: 'children.isOrphan', label: 'Is Orphan', type: 'boolean' },
      { key: 'children.isStudying', label: 'Is Studying', type: 'boolean' },
    ],
  },
  {
    key: 'employmentInfo',
    title: 'Employment Information',
    icon: 'tabler-briefcase',
    description: 'Current employment and work details',
    fields: [
      { key: 'employmentInfo.employeeId', label: 'Employee ID', type: 'text' },
      { key: 'employmentInfo.department', label: 'Department', type: 'text' },
      { key: 'employmentInfo.position', label: 'Position', type: 'text' },
      { key: 'employmentInfo.regNum', label: 'Registration Number', type: 'number' },
      { key: 'employmentInfo.havNum', label: 'HAV Number', type: 'number' },
      { key: 'employmentInfo.startDate', label: 'Start Date', type: 'date' },
      { key: 'employmentInfo.endDate', label: 'End Date', type: 'date' },
      { key: 'employmentInfo.status', label: 'Employment Status', type: 'text' },
    ],
  },
  {
    key: 'salaryEntries',
    title: 'Salary Information',
    icon: 'tabler-currency-dollar',
    description: 'Historical salary and part-time percentage data',
    fields: [
      { key: 'salaryEntries.year', label: 'Salary Year', type: 'number' },
      { key: 'salaryEntries.amount', label: 'Salary Amount', type: 'number' },
      { key: 'salaryEntries.partTimePercentage', label: 'Part-time Percentage', type: 'number' },
    ],
  },
  {
    key: 'pensionInfo',
    title: 'Pension Information',
    icon: 'tabler-piggy-bank',
    description: 'Pension status, codes, and benefit amounts',
    fields: [
      { key: 'pensionInfo.code', label: 'Pension Code', type: 'number' },
      { key: 'pensionInfo.codeDescription', label: 'Code Description', type: 'text' },
      { key: 'pensionInfo.codeEffectiveDate', label: 'Code Effective Date', type: 'date' },
      { key: 'pensionInfo.previousCode', label: 'Previous Code', type: 'number' },
      { key: 'pensionInfo.previousCodeEffectiveDate', label: 'Previous Code Effective Date', type: 'date' },
      { key: 'pensionInfo.accruedGrossAnnualOldAgePension', label: 'Accrued Gross Annual Old Age Pension', type: 'number' },
      { key: 'pensionInfo.accruedGrossAnnualPartnersPension', label: 'Accrued Gross Annual Partners Pension', type: 'number' },
      { key: 'pensionInfo.accruedGrossAnnualSinglesPension', label: 'Accrued Gross Annual Singles Pension', type: 'number' },
      { key: 'pensionInfo.attainableGrossAnnualOldAgePension', label: 'Attainable Gross Annual Old Age Pension', type: 'number' },
      { key: 'pensionInfo.extraAccruedGrossAnnualOldAgePension', label: 'Extra Accrued Gross Annual Old Age Pension', type: 'number' },
      { key: 'pensionInfo.extraAccruedGrossAnnualPartnersPension', label: 'Extra Accrued Gross Annual Partners Pension', type: 'number' },
      { key: 'pensionInfo.grossAnnualDisabilityPension', label: 'Gross Annual Disability Pension', type: 'number' },
      { key: 'pensionInfo.pensionBase', label: 'Pension Base', type: 'number' },
    ],
  },
  {
    key: 'pensionEndOfPreviousYear',
    title: 'Pensions End of Previous Calendar Year',
    icon: 'tabler-calendar-time',
    description: 'Pension amounts at the end of the previous calendar year (includes complete data table)',
    fields: [
      { key: 'pensionEndOfPreviousYear.code', label: 'Code', type: 'text' },
      { key: 'pensionEndOfPreviousYear.opte', label: 'OP-TE (Old Age Pension)', type: 'currency' },
      { key: 'pensionEndOfPreviousYear.optb', label: 'OP-TB (Attainable Old Age Pension)', type: 'currency' },
      { key: 'pensionEndOfPreviousYear.wpte', label: 'WP-TE (Partners Pension)', type: 'currency' },
      { key: 'pensionEndOfPreviousYear.onpte', label: 'ONP-TE (Singles Pension)', type: 'currency' },
      { key: 'pensionEndOfPreviousYear.aopte', label: 'AOP-TE (Disability Pension)', type: 'currency' },
    ],
  },
  {
    key: 'pensionsAfterCorrections',
    title: 'Pensions After Corrections',
    icon: 'tabler-edit-circle',
    description: 'Pension amounts after applying corrections (includes complete data table)',
    fields: [
      { key: 'pensionsAfterCorrections.opte', label: 'OP-TE (Old Age Pension)', type: 'currency' },
      { key: 'pensionsAfterCorrections.wpte', label: 'WP-TE (Partners Pension)', type: 'currency' },
      { key: 'pensionsAfterCorrections.onpte', label: 'ONP-TE (Singles Pension)', type: 'currency' },
    ],
  },
  {
    key: 'indexationBeginning',
    title: 'Indexation Beginning of Year',
    icon: 'tabler-trending-up',
    description: 'Indexed pension amounts at the beginning of the year (includes complete data table)',
    fields: [
      { key: 'indexationBeginning.opte', label: 'OP-TE (Old Age Pension)', type: 'currency' },
      { key: 'indexationBeginning.wpte', label: 'WP-TE (Partners Pension)', type: 'currency' },
      { key: 'indexationBeginning.onpte', label: 'ONP-TE (Singles Pension)', type: 'currency' },
    ],
  },
  {
    key: 'accrualPeriodCalculation',
    title: 'Accrual Period Calculation',
    icon: 'tabler-calculator',
    description: 'Employment periods and accrual calculations (includes complete data table)',
    fields: [
      { key: 'accrualPeriodCalculation.startDate', label: 'Start Date of Employment', type: 'date' },
      { key: 'accrualPeriodCalculation.endDate', label: 'End Date of Employment', type: 'date' },
      { key: 'accrualPeriodCalculation.birthDate', label: 'Birth Date', type: 'date' },
      { key: 'accrualPeriodCalculation.aovAge', label: 'AOV Age', type: 'number' },
    ],
  },
  {
    key: 'pensionsAccrual',
    title: 'Pensions Accrual',
    icon: 'tabler-chart-line',
    description: 'Pension accrual calculations and projections (includes complete data table)',
    fields: [
      { key: 'pensionsAccrual.accrualPercentage', label: 'Accrual Percentage', type: 'percentage' },
      { key: 'pensionsAccrual.pensionBase', label: 'Pension Base', type: 'currency' },
    ],
  },
  {
    key: 'pensionsAsPerReferenceDate',
    title: 'Pensions as per Reference Date',
    icon: 'tabler-calendar-check',
    description: 'Final pension amounts as of the reference date (includes complete data table)',
    fields: [
      { key: 'pensionsAsPerReferenceDate.opte', label: 'OP-TE (Old Age Pension)', type: 'currency' },
      { key: 'pensionsAsPerReferenceDate.wpte', label: 'WP-TE (Partners Pension)', type: 'currency' },
      { key: 'pensionsAsPerReferenceDate.onpte', label: 'ONP-TE (Singles Pension)', type: 'currency' },
      { key: 'pensionsAsPerReferenceDate.aopte', label: 'AOP-TE (Disability Pension)', type: 'currency' },
    ],
  },

  // {
  //     key: 'partnersWorksheet',
  //     title: 'Partners Information (Worksheet)',
  //     icon: 'tabler-users',
  //     description: 'Complete partner data exported as separate worksheet',
  //     fields: [
  //         { key: 'partners.worksheet', label: 'Partners Worksheet', type: 'worksheet' }
  //     ]
  // },
  // {
  //     key: 'childrenWorksheet',
  //     title: 'Children Information (Worksheet)',
  //     icon: 'tabler-baby-carriage',
  //     description: 'Complete children data exported as separate worksheet',
  //     fields: [
  //         { key: 'children.worksheet', label: 'Children Worksheet', type: 'worksheet' }
  //     ]
  // },
  // {
  //     key: 'salaryEntriesWorksheet',
  //     title: 'Salary Entries (Worksheet)',
  //     icon: 'tabler-currency-dollar',
  //     description: 'Complete salary history exported as separate worksheet',
  //     fields: [
  //         { key: 'salaryEntries.worksheet', label: 'Salary Entries Worksheet', type: 'worksheet' }
  //     ]
  // }
]
