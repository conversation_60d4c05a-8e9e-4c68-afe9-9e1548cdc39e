export const exportTemplates = [
  {
    name: 'Accountant Template',
    color: 'success',
    description: 'Essential information for accounting purposes',
    icon: 'tabler-calculator',
    fields: [
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.email',
      'personalInfo.fullBirthDate',
      'pensionInfo.code',
      'pensionInfo.codeDescription',
      'pensionInfo.accruedGrossAnnualOldAgePension',
      'pensionInfo.accruedGrossAnnualPartnersPension',
      'pensionInfo.accruedGrossAnnualSinglesPension',
      'pensionInfo.pensionBase',
    ],
  },
  {
    name: 'Personal Details',
    color: 'primary',
    description: 'Basic personal and contact information',
    icon: 'tabler-user',
    fields: [
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.email',
      'personalInfo.phone',
      'personalInfo.maritalStatus',
      'personalInfo.fullBirthDate',
      'address.street',
      'address.houseNumber',
      'address.postalCode',
      'address.city',
      'address.country',
    ],
  },
  {
    name: 'HR Template',
    color: 'warning',
    description: 'Human resources and employment data',
    icon: 'tabler-briefcase',
    fields: [
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.email',
      'employmentInfo.employeeId',
      'employmentInfo.department',
      'employmentInfo.position',
      'employmentInfo.regNum',
      'employmentInfo.startDate',
      'employmentInfo.endDate',
      'employmentInfo.status',
      'salaryEntries.worksheet',
    ],
  },
  {
    name: 'Family Report',
    color: 'info',
    description: 'Family structure and dependents',
    icon: 'tabler-users',
    fields: [
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.maritalStatus',
      'personalInfo.fullBirthDate',
      'partners.worksheet',
      'children.worksheet',
    ],
  },
  {
    name: 'Complete Report',
    color: 'secondary',
    description: 'All available participant data',
    icon: 'tabler-database',
    fields: [
      // Personal Information
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.email',
      'personalInfo.phone',
      'personalInfo.maritalStatus',
      'personalInfo.birthDay',
      'personalInfo.birthMonth',
      'personalInfo.birthYear',
      'personalInfo.fullBirthDate',

      // Address Information
      'address.street',
      'address.houseNumber',
      'address.postalCode',
      'address.city',
      'address.state',
      'address.country',

      // Partner Information (individual fields)
      'partners.firstName',
      'partners.lastName',
      'partners.dateOfBirth',
      'partners.startDate',
      'partners.isCurrent',
      'partners.isDeceased',

      // Children Information (individual fields)
      'children.firstName',
      'children.lastName',
      'children.dateOfBirth',
      'children.isOrphan',
      'children.isStudying',

      // Employment Information
      'employmentInfo.employeeId',
      'employmentInfo.department',
      'employmentInfo.position',
      'employmentInfo.regNum',
      'employmentInfo.havNum',
      'employmentInfo.startDate',
      'employmentInfo.endDate',
      'employmentInfo.status',

      // Salary Information (individual fields)
      'salaryEntries.year',
      'salaryEntries.amount',
      'salaryEntries.partTimePercentage',

      // Pension Information
      'pensionInfo.code',
      'pensionInfo.codeDescription',
      'pensionInfo.codeEffectiveDate',
      'pensionInfo.previousCode',
      'pensionInfo.previousCodeEffectiveDate',
      'pensionInfo.accruedGrossAnnualOldAgePension',
      'pensionInfo.accruedGrossAnnualPartnersPension',
      'pensionInfo.accruedGrossAnnualSinglesPension',
      'pensionInfo.attainableGrossAnnualOldAgePension',
      'pensionInfo.extraAccruedGrossAnnualOldAgePension',
      'pensionInfo.extraAccruedGrossAnnualPartnersPension',
      'pensionInfo.grossAnnualDisabilityPension',
      'pensionInfo.pensionBase',

      // Pension Calculation Tables (individual fields)
      'pensionEndOfPreviousYear.code',
      'pensionEndOfPreviousYear.opte',
      'pensionEndOfPreviousYear.optb',
      'pensionEndOfPreviousYear.wpte',
      'pensionEndOfPreviousYear.onpte',
      'pensionEndOfPreviousYear.aopte',
      'pensionsAfterCorrections.opte',
      'pensionsAfterCorrections.wpte',
      'pensionsAfterCorrections.onpte',
      'indexationBeginning.opte',
      'indexationBeginning.wpte',
      'indexationBeginning.onpte',
      'accrualPeriodCalculation.startDate',
      'accrualPeriodCalculation.endDate',
      'accrualPeriodCalculation.birthDate',
      'accrualPeriodCalculation.aovAge',
      'pensionsAccrual.accrualPercentage',
      'pensionsAccrual.pensionBase',
      'pensionsAsPerReferenceDate.opte',
      'pensionsAsPerReferenceDate.wpte',
      'pensionsAsPerReferenceDate.onpte',
      'pensionsAsPerReferenceDate.aopte',

      // Worksheets (for complete data tables)
      'partners.worksheet',
      'children.worksheet',
      'salaryEntries.worksheet',
      'pensionEndOfPreviousYear.fullTable',
      'pensionsAfterCorrections.fullTable',
      'indexationBeginning.fullTable',
      'accrualPeriodCalculation.fullTable',
      'pensionsAccrual.fullTable',
      'pensionsAsPerReferenceDate.fullTable',
    ],
  },
  {
    name: 'Basic Contact',
    color: 'error',
    description: 'Minimal contact information',
    icon: 'tabler-phone',
    fields: [
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.email',
      'personalInfo.phone',
    ],
  },
  {
    name: 'OP-TE Focus',
    color: 'purple',
    description: 'Old Age Pension tracking across all calculation tables',
    icon: 'tabler-chart-line',
    fields: [
      'personalInfo.firstName',
      'personalInfo.lastName',
      'personalInfo.fullBirthDate',
      'pensionEndOfPreviousYear.opte',
      'pensionsAfterCorrections.opte',
      'indexationBeginning.opte',
      'pensionsAsPerReferenceDate.opte',
    ],
  },
]
