// Types
interface Participant {
  id: string
  personalInfo: {
    firstName: string
    lastName: string
  }
  employmentInfo: {
    department: string
  }
  certificationStatus: string
  changes: number
  risk: string
  certificationId?: string | null
}

interface Stats {
  totalParticipants: number
  pendingCertifications: number
  inProgress: number
  completed: number
  requiresAttention: number
}
