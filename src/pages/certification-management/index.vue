<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useParticipantGraph } from '@/api/graphHooks/useParticipantGraph'
import { useCertifiedDataGraph } from '@/api/graphHooks/useCertifiedDataGraph'
import { pusherService } from '@/services/pusher'
import CertificationParticipantsTable from '@/components/certification-management/CertificationParticipantsTable.vue'

const router = useRouter()

// Reactive data
const participants = ref<Participant[]>([])
const selected = ref<Participant[]>([])
const filterStatus = ref('')
const filterRisk = ref('')
const filterDepartment = ref('')
const searchQuery = ref('')
const quickFilter = ref<string | null>(null)
const bulkReviewDialog = ref(false)
const selectedYear = ref(new Date().getFullYear()) // Default to current year or 2023 if earlier

const years = computed(() => {
  const currentYear = new Date().getFullYear()
  const startYear = 2023
  const endYear = 2027
  const yearsArray = []
  for (let year = startYear; year <= endYear; year++)
    yearsArray.push(year)

  return yearsArray
})

let certificationChannel: any = null

// Use existing participant hook
const {
  state: { participantsList, loadingParticipants },
  actions: { refetchParticipants },
} = useParticipantGraph()

// Use certified data hook for bulk operations
const {
  state: {
    certificationStats,
    loadingCertificationStats,
    autoApproveEligible,
    loadingAutoApproveEligible,
    commonChangePatterns,
    loadingCommonChangePatterns,
    loadingBulkStartCertification,
    loadingBulkApproveCertification,
    loadingBulkRejectCertification,
  },
  actions: {
    bulkStartCertification,
    bulkApproveCertification,
    bulkRejectCertification,
    refetchCertificationStats,
    refetchAutoApproveEligible,
    refetchCommonChangePatterns,
  },
} = useCertifiedDataGraph()

// Computed properties
const stats = computed((): Stats => {
  // Use real stats from backend when available, otherwise fall back to computed stats
  if (certificationStats.value) {
    return {
      totalParticipants: certificationStats.value.totalParticipants,
      pendingCertifications: certificationStats.value.pendingCertifications,
      inProgress: certificationStats.value.inProgress,
      completed: certificationStats.value.completed,
      requiresAttention: certificationStats.value.requiresAttention,
    }
  }

  // Fallback to computed stats from participants data
  const total = participants.value.length
  const pending = participants.value.filter(p => p.certificationStatus === 'pending').length
  const inProgress = participants.value.filter(p => p.certificationStatus === 'started').length
  const completed = participants.value.filter(p => p.certificationStatus === 'completed').length
  const requiresAttention = participants.value.filter(p => p.risk === 'high' || p.changes > 5).length

  return {
    totalParticipants: total,
    pendingCertifications: pending,
    inProgress,
    completed,
    requiresAttention,
  }
})

const filteredParticipants = computed(() => {
  let filtered = participants.value

  // Apply filters
  if (filterStatus.value)
    filtered = filtered.filter(p => p.certificationStatus === filterStatus.value)

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()

    filtered = filtered.filter(p =>
      p.personalInfo.firstName.toLowerCase().includes(query)
      || p.personalInfo.lastName.toLowerCase().includes(query),
    )
  }

  return filtered
})

const autoApproveEligibleComputed = computed(() => {
  // Use real data from backend when available
  if (autoApproveEligible.value && autoApproveEligible.value.length > 0)
    return autoApproveEligible.value

  // Fallback to computed data from participants
  return participants.value.filter(p =>
    p.changes === 0
    || (p.changes < 3 && p.risk === 'low'),
  )
})

const commonChanges = computed(() => {
  // Use real data from backend when available
  if (commonChangePatterns.value && commonChangePatterns.value.length > 0) {
    return commonChangePatterns.value.map(pattern => ({
      field: pattern.field,
      count: pattern.count,
      description: `${pattern.count} participants`,
    }))
  }

  // Fallback to mock data
  return [
    { field: 'Salary Increase', count: 38, description: 'avg 3.5%' },
    { field: 'Address Changes', count: 12, description: 'residential' },
    { field: 'Marital Status', count: 5, description: 'status updates' },
  ]
})

// Transform participants data for the table
watch(participantsList, newParticipants => {
  if (newParticipants && newParticipants.length > 0) {
    console.log('Participants data:', newParticipants)
    participants.value = newParticipants.map((item: any) => ({
      id: item.id,
      personalInfo: {
        ...item.personalInfo,
        email: item.personalInfo?.email || `${item.personalInfo?.firstName?.toLowerCase()}.${item.personalInfo?.lastName?.toLowerCase()}@company.com`,
      },
      employmentInfo: item.employmentInfo || { department: 'N/A' },
      certificationStatus: item.certifiedData?.[0]?.certificationStatus || 'pending',
      changes: (item.certifiedData?.[0]?.approvedChanges?.length || 0) + (item.certifiedData?.[0]?.rejectedChanges?.length || 0),
      risk: item.certifiedData?.[0]?.risk || 'none',
      lastActivity: item.certifiedData?.[0]?.updatedAt ? formatLastActivity(item.certifiedData[0].updatedAt) : 'No activity',
      certificationId: item.certifiedData?.[0]?.id || null, // Add certification ID for mutations
    }))
  }
  else {
    console.log('No participants data available')
  }
}, { immediate: true })

// Lifecycle hooks
onMounted(() => {
  // Initialize Pusher for real-time updates using existing service
  pusherService.connect()

  // Subscribe to certification updates channel
  if (pusherService) {
    try {
      certificationChannel = pusherService.pusher?.subscribe('certification-updates')

      if (certificationChannel) {
        certificationChannel.bind('certification-started', (data: any) => {
          console.log('Certification Started:', data)
          refetchParticipants()
        })

        certificationChannel.bind('certification-completed', (data: any) => {
          console.log('Certification Completed:', data)
          refetchParticipants()
        })

        certificationChannel.bind('certification-status-updated', (data: any) => {
          console.log('Certification Status Updated:', data)
          refetchParticipants()
        })
      }
    }
    catch (error) {
      console.error('Error setting up Pusher certification channel:', error)
    }
  }
})

onUnmounted(() => {
  if (certificationChannel)
    pusherService.unsubscribeFromChannel('certification-updates')
})

// Utility functions
const formatLastActivity = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    return `${diffInMinutes} minutes ago`
  }
  else if (diffInHours < 24) {
    return `${diffInHours} hours ago`
  }
  else {
    const diffInDays = Math.floor(diffInHours / 24)

    return `${diffInDays} days ago`
  }
}

// Action methods
const selectAll = () => {
  selected.value = [...filteredParticipants.value]
}

const clearSelection = () => {
  selected.value = []
}

const startBulkCertification = async () => {
  try {
    // route to Start Cert page
    router.push('/certification-management/active')
    clearSelection()
  }
  catch (error) {
    console.error('Error starting bulk certification:', error)
  }
}

const openParticipantReview = (participant: Participant) => {
  // Navigate to individual participant certification page
  window.open(`/certification-management/${participant.id}`, '_blank')
}

const handleViewParticipant = (participant: Participant) => {
  // Navigate to individual participant certification page
  window.open(`/certification-management/${participant.id}`, '_blank')
}

const approveSingle = async (participant: Participant) => {
  try {
    // Check if participant has valid certification data
    if (!participant.certificationStatus || participant.certificationStatus === 'pending') {
      console.error('Cannot approve certification - participant is not started')

      return
    }

    if (!participant.certificationId) {
      console.error('Cannot approve certification - no certification ID found')

      return
    }

    // Use bulk approve mutation with single certification ID
    const result = await bulkApproveCertification([participant.certificationId])

    if (result?.successful?.length > 0) {
      console.log(`Successfully approved certification for participant ${participant.id}`)
      refetchParticipants()
      refetchCertificationStats()
    }

    if (result?.failed?.length > 0)
      console.error(`Failed to approve certification for participant ${participant.id}:`, result.failed[0])
  }
  catch (error) {
    console.error('Error approving single certification:', error)
  }
}
</script>

<template>
  <div class="certification-management pa-6">
    <div class="mb-6">
      <h1 class="text-h4 font-weight-bold mb-4">
        Certification Management
      </h1>
      <VCard class="pa-4 mb-6">
        <div class="d-flex align-center">
          <VSelect
            v-model="selectedYear"
            :items="years"
            label="Certification Year"
            variant="outlined"
            class="me-4"
            style="max-width: 200px;"
          />
          <h2 class="text-h5 font-weight-semibold">
            Certification for {{ selectedYear }}
          </h2>
        </div>
      </VCard>

      <StatsProgress />
    </div>

    <VCard>
      <VCard class="pa-4 mb-6">
        <div class="d-flex flex-wrap align-center gap-4">
          <VBtn
            color="primary"
            variant="elevated"
            :disabled="selected.length === 0"
            :loading="loadingBulkStartCertification"
            @click="startBulkCertification"
          >
            <VIcon start>
              tabler-play
            </VIcon>
            Start Certification ({{ selected.length }})
          </VBtn>

          <VBtn
            color="secondary"
            variant="outlined"
            :disabled="selected.length === 0"
            :loading="loadingBulkApproveCertification"
            @click="bulkReviewDialog = true"
          >
            <VIcon start>
              tabler-checklist
            </VIcon>
            Bulk Review
          </VBtn>

          <VSpacer />

          <VBtn
            color="primary"
            variant="outlined"
            @click="exportData"
          >
            <VIcon start>
              tabler-download
            </VIcon>
            Export
          </VBtn>
        </div>
      </VCard>
      <CertificationParticipantsTable
        v-model:selected="selected"
        :participants="filteredParticipants"
        :loading="loadingParticipants"
        @view-participant="handleViewParticipant"
      />
    </VCard>

    <!-- Bulk Review Dialog -->
    <VDialog
      v-model="bulkReviewDialog"
      max-width="800"
    >
      <VCard>
        <VCardTitle>Bulk Review: {{ selected.length }} Participants Selected</VCardTitle>
        <VCardText>
          <div class="mb-4">
            <div class="text-subtitle-1 font-weight-medium mb-2">
              Common Changes Detected:
            </div>
            <div
              v-for="change in commonChanges"
              :key="change.field"
              class="d-flex align-center mb-2"
            >
              <VIcon
                color="primary"
                size="16"
                class="me-2"
              >
                tabler-users
              </VIcon>
              <span>{{ change.field }}: {{ change.count }} participants ({{ change.description }})</span>
            </div>
          </div>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>

<style scoped>
.certification-management {
  max-width: 1400px;
  margin: 0 auto;
}
</style>
