<script setup lang="ts">
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { computed, onMounted, ref } from 'vue'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'
import RevertChangesDialog from '@/components/dialogs/RevertChangesDialog.vue'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'

const pensionStore = usePensionStore()
const route = useRoute()

const participantId = computed(() => route.params.id as string)
const certificationId = computed(() => route.params.cid as string)
const yearParam = computed(() => route.params.year as string)

const {
  state: {
    certifiedDataByYearAndYearBeforeData,
  },
  actions: {
    fetchCertifiedDataByYearAndYearBefore,
  },
} = useCertifiedDataByYearAndYearBefore()

const setOnGoingCertification = (status: boolean) => {
  pensionStore.setOngoingCertification(status)
}

const startCertifications = computed(() => true)

const leftYear = computed(() => {
  const year = (route.params as { year: string }).year

  return Number.parseInt(year) - 1
})

const rightYear = computed(() => {
  const year = (route.params as { year: string }).year

  return Number.parseInt(year)
})

const leftColSize = computed(() => startCertifications.value ? 6 : 0)
const rightColSize = computed(() => startCertifications.value ? 6 : 12)

// Revert changes dialog
const revertDialog = ref(false)

const openRevertDialog = () => {
  revertDialog.value = true
}

onMounted(async () => {
  await fetchCertifiedDataByYearAndYearBefore(rightYear.value)

  // Set comparison view as default
  setOnGoingCertification(true)
})

onBeforeRouteLeave(() => {
  pensionStore.setOngoingCertification(false)
})
</script>

<template>
  <div class="data-comparison-container">
    <SimpleBreadcrumbs
      :items="[
        { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
        { title: 'Certification Management', to: '/certification-management', icon: 'tabler-certificate' },
        { title: 'Participant', to: `/certification-management/${participantId}`, icon: 'tabler-user' },
        { title: 'Certification', to: `/certification-management/${participantId}/certification/${certificationId}`, icon: 'tabler-certificate-2' },
        { title: `Year ${yearParam}`, to: `/certification-management/${participantId}/certification/${certificationId}/${yearParam}`, icon: 'tabler-calendar' },
        { title: 'Comparison View', disabled: true, icon: 'tabler-layers-intersect' },
      ]"
    />

    <!--    <CertificationActions class="mb-6" /> -->

    <!-- Comparison Header -->
    <VCard
      class="comparison-header mb-6"
      elevation="1"
    >
      <VRow no-gutters>
        <VCol
          v-if="startCertifications"
          :cols="leftColSize"
          class="comparison-header-left"
        >
          <div class="header-content pa-4">
            <div class="d-flex align-center gap-3">
              <VIcon
                icon="tabler-database"
                color="success"
                size="24"
              />
              <div>
                <h3 class="text-h6 font-weight-bold text-success">
                  Certified Data
                </h3>
                <p class="text-body-2 text-medium-emphasis mb-0">
                  Year {{ leftYear }} (Reference)
                </p>
              </div>
            </div>
            <VChip
              color="success"
              variant="tonal"
              size="small"
              prepend-icon="tabler-shield-check"
              class="mt-2"
            >
              Verified & Locked
            </VChip>
          </div>
        </VCol>
        <VDivider vertical />

        <VCol
          :cols="rightColSize"
          class="comparison-header-right"
        >
          <div class="header-content pa-4">
            <div class="d-flex align-center justify-space-between">
              <div class="d-flex align-center gap-3">
                <VIcon
                  icon="tabler-edit"
                  color="warning"
                  size="24"
                />
                <div>
                  <h3 class="text-h6 font-weight-bold text-warning">
                    Pending Certification
                  </h3>
                  <p class="text-body-2 text-medium-emphasis mb-0">
                    Year {{ rightYear }} (Under Review)
                  </p>
                </div>
              </div>
              <div class="d-flex align-center gap-2">
                <VBtn
                  size="small"
                  color="error"
                  variant="outlined"
                  prepend-icon="tabler-refresh-alert"
                  class="ml-2"
                  @click="openRevertDialog"
                >
                  Revert Changes
                </VBtn>
              </div>
            </div>
            <VChip
              color="warning"
              variant="tonal"
              size="small"
              prepend-icon="tabler-clock-pause"
            >
              Draft Mode
            </VChip>
          </div>
        </VCol>
      </VRow>
    </VCard>

    <!-- Section divider -->
    <div class="section-divider">
      <VIcon
        icon="tabler-user-circle"
        size="20"
        class="me-2"
      />
      <span>Basic Information</span>
    </div>

    <!-- Basic Information side by side -->
    <VRow class="data-row">
      <VCol
        v-if="startCertifications"
        :cols="leftColSize"
      >
        <CertifiedBasicInformation
          :year="leftYear"
          :editable="false"
        />
      </VCol>
      <VCol :cols="rightColSize">
        <CertifiedBasicInformation
          :table-view="true"
          :editable="startCertifications"
        />
      </VCol>
    </VRow>

    <!-- Section divider -->
    <div class="section-divider">
      <VIcon
        icon="tabler-id"
        size="20"
        class="me-2"
      />
      <span>Participant Information</span>
    </div>

    <!-- Participant Information side by side -->
    <VRow class="data-row">
      <VCol
        v-if="startCertifications"
        :cols="leftColSize"
      >
        <CertifiedParticipantInformation
          :year="leftYear"
          :editable="false"
        />
      </VCol>
      <VCol :cols="rightColSize">
        <CertifiedParticipantInformation :editable="startCertifications" />
      </VCol>
    </VRow>

    <!-- Section divider -->
    <div class="section-divider">
      <VIcon
        icon="tabler-cash"
        size="20"
        class="me-2"
      />
      <span>Salary Pension Base</span>
    </div>

    <!-- Salary Pension Base side by side -->
    <VRow class="data-row">
      <VCol
        v-if="startCertifications"
        :cols="leftColSize"
      >
        <CertifiedSalaryPensionBase
          :year="leftYear"
          :editable="false"
        />
      </VCol>
      <VCol :cols="rightColSize">
        <CertifiedSalaryPensionBase :year="rightYear" />
      </VCol>
    </VRow>

    <!-- Section divider -->
    <div class="section-divider">
      <VIcon
        icon="tabler-calculator"
        size="20"
        class="me-2"
      />
      <span>Pension Calculation Base</span>
    </div>

    <!-- Pension Calculation Base side by side -->
    <VRow class="data-row">
      <VCol
        v-if="startCertifications"
        :cols="leftColSize"
      >
        <CertifiedPensionCalculationBase
          :year="leftYear"
          :editable="false"
        />
      </VCol>
      <VCol :cols="rightColSize">
        <CertifiedPensionCalculationBase :year="rightYear" />
      </VCol>
    </VRow>

    <!-- Revert Changes Dialog -->
    <RevertChangesDialog
      v-model="revertDialog"
      :certification-year="rightYear"
      @reverted="fetchCertifiedDataByYearAndYearBefore(rightYear)"
    />
  </div>
</template>

<style scoped lang="scss">
  .data-comparison-container {
    padding: 16px;
  }

  .year-header-row {
    margin-bottom: 24px;

    .year-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: rgb(var(--v-theme-surface));
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      height: 100%;

      &.pending {
        border-left: 4px solid rgb(var(--v-theme-primary));
      }

      .year-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background-color: rgba(var(--v-theme-primary), 0.1);
        border-radius: 8px;
        margin-right: 16px;
        color: rgb(var(--v-theme-primary));
      }

      .year-content {
        flex: 1;

        .year-title {
          font-size: 14px;
          font-weight: 500;
          color: rgba(var(--v-theme-on-surface), 0.7);
          margin-bottom: 4px;
        }

        .year-value {
          font-size: 20px;
          font-weight: 600;
          color: rgb(var(--v-theme-on-surface));
        }
      }

      .revert-btn {
        margin-left: auto;
      }
    }
  }

  .section-divider {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 24px 0 16px;
    background-color: rgba(var(--v-theme-primary), 0.05);
    border-radius: 8px;
    font-weight: 600;
    color: rgb(var(--v-theme-primary));

    .v-icon {
      color: rgb(var(--v-theme-primary));
    }
  }

  .data-row {
    margin-bottom: 32px;

    .v-col {
      padding-top: 0;
    }
  }

  @media (max-width: 960px) {
    .year-header-row {
      .year-card {
        flex-direction: column;
        text-align: center;
        padding: 24px 16px;

        .year-icon {
          margin-right: 0;
          margin-bottom: 12px;
        }

        .revert-btn {
          margin-top: 16px;
          margin-left: 0;
          width: 100%;
        }
      }
    }
  }
</style>
