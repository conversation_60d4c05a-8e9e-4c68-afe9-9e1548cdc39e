<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { usePensionBase } from '@/composables/pension-base/usePensionBase'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'

const route = useRoute()

const participantId = computed(() => route.params.id as string)
const certificationId = computed(() => route.params.cid as string)
const yearParam = computed(() => route.params.year as string)

const rightYear = computed(() => {
  return Number.parseInt(route.params.year as string)
})

const { getCertificationStatus } = usePensionBase()

const leftColSize = 12

const certificationStatus = computed(() => {
  return getCertificationStatus(rightYear.value)
})

const isEditable = computed(() => {
  return certificationStatus.value === 'pending'
})
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Certification Management', to: '/certification-management', icon: 'tabler-certificate' },
      { title: 'Participant', to: `/certification-management/${participantId}`, icon: 'tabler-user' },
      { title: 'Certification', to: `/certification-management/${participantId}/certification/${certificationId}`, icon: 'tabler-certificate-2' },
      { title: `Year ${yearParam}`, disabled: true, icon: 'tabler-calendar' },
    ]"
  />

  <CertificationActions class="mb-6" />

  <!-- Basic Information side by side -->
  <VRow>
    <VCol :cols="leftColSize">
      <CertifiedBasicInformation
        :active-certification="true"
        :year="rightYear"
        :editable="isEditable"
      />
    </VCol>
  </VRow>

  <!-- Participant Information side by side -->
  <VRow>
    <VCol :cols="leftColSize">
      <CertifiedParticipantInformation
        :active-certification="true"
        :year="rightYear"
        :editable="isEditable"
      />
    </VCol>
  </VRow>

  <!-- Salary Pension Base side by side -->
  <VRow>
    <VCol :cols="leftColSize">
      <CertifiedSalaryPensionBase
        :year="rightYear"
        :editable="isEditable"
      />
    </VCol>
  </VRow>

  <!-- Pension Calculation Base side by side -->
  <VRow>
    <VCol :cols="leftColSize">
      <CertifiedPensionCalculationBase
        :year="rightYear"
        :editable="isEditable"
      />
    </VCol>
  </VRow>
</template>
