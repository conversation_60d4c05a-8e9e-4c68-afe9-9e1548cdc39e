<script setup lang="ts">
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { usePensionStore } from '@/stores/pension/pensionStore'
import SimpleBreadcrumbs from '@/components/SimpleBreadcrumbs.vue'

const pensionStore = usePensionStore()
const route = useRoute()

const participantId = computed(() => route.params.id as string)
const certificationId = computed(() => route.params.cid as string)
const yearParam = computed(() => route.params.year as string)

const rightYear = computed(() => {
  return Number.parseInt(route.params.year as string)
})

onBeforeRouteLeave(() => {
  pensionStore.setOngoingCertification(false)
})
</script>

<template>
  <SimpleBreadcrumbs
    :items="[
      { title: 'Dashboard', to: '/', icon: 'tabler-dashboard' },
      { title: 'Certification Management', to: '/certification-management', icon: 'tabler-certificate' },
      { title: 'Participant', to: `/certification-management/${participantId}`, icon: 'tabler-user' },
      { title: 'Certification', to: `/certification-management/${participantId}/certification/${certificationId}`, icon: 'tabler-certificate-2' },
      { title: `Year ${yearParam}`, to: `/certification-management/${participantId}/certification/${certificationId}/${yearParam}`, icon: 'tabler-calendar' },
      { title: 'Completed', disabled: true, icon: 'tabler-check' },
    ]"
  />

  <CertificationActions class="mb-6" />
  <VRow class="year-comparison">
    <VCol cols="12">
      <div class="year-label">
        Pending Certification: {{ rightYear }}
      </div>
    </VCol>
  </VRow>

  <!-- Basic Information -->
  <VRow>
    <VCol cols="12">
      <CertifiedBasicInformation :editable="false" />
    </VCol>
  </VRow>

  <!-- Participant Information -->
  <VRow>
    <VCol cols="12">
      <CertifiedParticipantInformation :editable="false" />
    </VCol>
  </VRow>

  <!-- Salary Pension Base -->
  <VRow>
    <VCol cols="12">
      <CertifiedSalaryPensionBase
        :year="rightYear"
        :editable="false"
      />
    </VCol>
  </VRow>

  <!-- Pension Calculation Base -->
  <VRow>
    <VCol cols="12">
      <CertifiedPensionCalculationBase
        :year="rightYear"
        :editable="false"
      />
    </VCol>
  </VRow>
</template>

<style scoped>
  .header-row {
    background-color: #1976d2; /* Primary color */
    color: white;
    padding: 16px;
    margin-bottom: 24px;
    border-radius: 4px;
  }

  .header-title {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .header-subtitle {
    font-size: 16px;
    opacity: 0.9;
  }

  .year-comparison {
    margin-bottom: 24px;
  }

  .year-label {
    font-size: 24px;
    font-weight: 500;
    padding-bottom: 8px;
    border-bottom: 1px solid #1976d2;
  }
</style>
