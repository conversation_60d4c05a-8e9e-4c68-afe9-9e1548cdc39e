<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useMutation, useQuery } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import Pusher from 'pusher-js'

const participants = ref([])
const selected = ref([])
const filterStatus = ref('all')
const filterRisk = ref('all')
const filterName = ref('')

let pusher: Pusher | null = null

const headers = [
  { title: 'Name', key: 'personalInfo.firstName' },
  { title: 'Department', key: 'employmentInfo.department' },
  { title: 'Status', key: 'certifiedData[0].certificationStatus' },
  { title: 'Changes', key: 'changes' },
  { title: 'Risk', key: 'certifiedData[0].risk' },
  { title: 'Actions', key: 'actions' },
]

const GET_PARTICIPANTS = gql`
  query GetAllParticipants {
    findAll {
      items {
        id
        personalInfo {
          firstName
          lastName
        }
        employmentInfo {
          department
        }
        certifiedData {
          id
          certificationStatus
          risk
        }
      }
    }
  }
`

const BULK_START_CERTIFICATION = gql`
  mutation BulkStartCertification($participantIds: [String!]!, $year: Int!) {
    bulkStartCertification(input: { participantIds: $participantIds, year: $year }) {
      successful
      failed {
        id
        reason
      }
    }
  }
`

const BULK_APPROVE_CERTIFICATION = gql`
  mutation BulkApproveCertification($certificationIds: [String!]!) {
    bulkApproveCertification(input: { certificationIds: $certificationIds }) {
      successful
      failed {
        id
        reason
      }
    }
  }
`

const { result, loading, error, refetch } = useQuery(GET_PARTICIPANTS)
const { mutate: bulkStartMutation } = useMutation(BULK_START_CERTIFICATION)
const { mutate: bulkApproveMutation } = useMutation(BULK_APPROVE_CERTIFICATION)

watch(result, newValue => {
  if (newValue)
    participants.value = newValue.findAll.items
})

onMounted(() => {
  pusher = new Pusher('YOUR_PUSHER_APP_KEY', { // Replace with your actual Pusher App Key
    cluster: 'YOUR_PUSHER_CLUSTER', // Replace with your actual Pusher Cluster
    encrypted: true,
  })

  const channel = pusher.subscribe('certification-updates')

  channel.bind('certification-started', (data: any) => {
    console.log('Certification Started:', data)
    refetch()
  })
  channel.bind('certification-completed', (data: any) => {
    console.log('Certification Completed:', data)
    refetch()
  })
})

onUnmounted(() => {
  if (pusher)
    pusher.disconnect()
})

const filteredParticipants = computed(() => {
  return participants.value.filter(participant => {
    const certification = participant.certifiedData && participant.certifiedData.length > 0 ? participant.certifiedData[0] : null

    const matchesStatus = filterStatus.value === 'all'
                          || (certification && certification.certificationStatus.toLowerCase() === filterStatus.value)

    const matchesRisk = filterRisk.value === 'all'
                        || (certification && certification.risk && certification.risk.toLowerCase() === filterRisk.value)

    const matchesName = participant.personalInfo.firstName.toLowerCase().includes(filterName.value.toLowerCase())
                        || participant.personalInfo.lastName.toLowerCase().includes(filterName.value.toLowerCase())

    return matchesStatus && matchesRisk && matchesName
  })
})

const getRiskColor = (risk: string) => {
  if (risk === 'high')
    return 'error'
  else if (risk === 'medium')
    return 'warning'
  else
    return 'success'
}

const startCertification = async () => {
  const participantIds = selected.value.map(p => p.id)
  const year = new Date().getFullYear()

  await bulkStartMutation({ participantIds, year })
}

const approveCertification = async () => {
  const certificationIds = selected.value.map(p => p.certifiedData[0].id).filter(id => id)
  if (certificationIds.length > 0)
    await bulkApproveMutation({ certificationIds })

  // refetch(); // Refetching is now handled by Pusher updates
}
</script>

<template>
  <div>
    <VBtn @click="startCertification">
      Start Certification
    </VBtn>
    <VBtn @click="approveCertification">
      Approve Certification
    </VBtn>
    <VBtn to="/certification-management/templates">
      Manage Templates
    </VBtn>
    <VBtn to="/audit-trail">
      View Audit Trail
    </VBtn>
    <VBtn to="/analytics">
      View Analytics
    </VBtn>

    <VRow class="mt-4">
      <VCol
        cols="12"
        md="4"
      >
        <VSelect
          v-model="filterStatus"
          :items="['all', 'pending', 'started', 'completed']"
          label="Filter by Status"
          clearable
        />
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <VSelect
          v-model="filterRisk"
          :items="['all', 'low', 'medium', 'high']"
          label="Filter by Risk"
          clearable
        />
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <VTextField
          v-model="filterName"
          label="Search by Name"
          clearable
        />
      </VCol>
    </VRow>

    <VDataTable
      v-model="selected"
      :headers="headers"
      :items="filteredParticipants"
      item-key="id"
      show-select
    >
      <template #item.risk="{ item }">
        <VChip :color="getRiskColor(item.risk)">
          {{ item.risk }}
        </VChip>
      </template>
    </VDataTable>
  </div>
</template>
