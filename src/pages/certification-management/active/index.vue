<script setup lang="ts">
import { ref } from 'vue'

interface Participant {
  id: number
  name: string
  department: string
  changes: number
  risk: string
  decision: string
  addressChange?: { previous: string; current: string }
  salaryChange?: { previous: string; current: string }
  phoneChange?: { previous: string; current: string }
  emailChange?: { previous: string; current: string }
}

interface ChangePattern {
  field: string
  count: number
  avgChange: string
  selectedAction: string | null
  participants: Participant[]
}

// Generate dummy participants
const generateParticipants = (): Participant[] => {
  const departments = ['IT', 'HR', 'Finance', 'Marketing', 'Sales', 'Operations', 'Legal']
  const risks = ['low', 'medium', 'high']
  const decisions = ['Approved', 'Rejected', 'Flagged', 'Pending']

  const names = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON> <PERSON>',
    'Kimberly Young',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    'Thomas <PERSON>',
    'Sharon Scott',
    '<PERSON> Green',
  ]

  const addresses = [
    { previous: '123 Main St, City A', current: '456 Oak Ave, City B' },
    { previous: '789 Pine Rd, City C', current: '321 Elm St, City D' },
    { previous: '654 Maple Dr, City E', current: '987 Cedar Ln, City F' },
    { previous: '147 Birch Ave, City G', current: '258 Spruce St, City H' },
    { previous: '369 Walnut Rd, City I', current: '741 Cherry Dr, City J' },
  ]

  const salaries = [
    { previous: '$65,000', current: '$67,600' },
    { previous: '$72,000', current: '$75,600' },
    { previous: '$58,000', current: '$60,900' },
    { previous: '$85,000', current: '$89,250' },
    { previous: '$95,000', current: '$99,750' },
  ]

  const phones = [
    { previous: '(*************', current: '(*************' },
    { previous: '(*************', current: '(*************' },
    { previous: '(*************', current: '(*************' },
    { previous: '(*************', current: '(*************' },
    { previous: '(*************', current: '(*************' },
  ]

  const emails = [
    { previous: '<EMAIL>', current: '<EMAIL>' },
    { previous: '<EMAIL>', current: '<EMAIL>' },
    { previous: '<EMAIL>', current: '<EMAIL>' },
    { previous: '<EMAIL>', current: '<EMAIL>' },
    { previous: '<EMAIL>', current: '<EMAIL>' },
  ]

  return names.map((name, index) => {
    const changesCount = Math.floor(Math.random() * 4) + 1

    const participant: Participant = {
      id: index + 1,
      name,
      department: departments[Math.floor(Math.random() * departments.length)],
      changes: changesCount,
      risk: risks[Math.floor(Math.random() * risks.length)],
      decision: decisions[Math.floor(Math.random() * decisions.length)],
    }

    // Randomly assign changes
    const changeTypes = ['address', 'salary', 'phone', 'email']
    const selectedChanges = changeTypes.sort(() => 0.5 - Math.random()).slice(0, changesCount)

    selectedChanges.forEach(changeType => {
      switch (changeType) {
        case 'address':
          participant.addressChange = addresses[Math.floor(Math.random() * addresses.length)]
        break;
        case 'salary':
          participant.salaryChange = salaries[Math.floor(Math.random() * salaries.length)]
        break;
        case 'phone':
          participant.phoneChange = phones[Math.floor(Math.random() * phones.length)]
        break;
        case 'email':
          participant.emailChange = emails[Math.floor(Math.random() * emails.length)]
        break;
      }
    })

    return participant
  })
}

const participants = ref<Participant[]>(generateParticipants())

const generateChangePatterns = (): ChangePattern[] => {
  const patterns: ChangePattern[] = []

  // Address changes
  const addressChanges = participants.value.filter(p => p.addressChange)
  if (addressChanges.length > 0) {
    patterns.push({
      field: 'Address',
      count: addressChanges.length,
      avgChange: '0.0%',
      selectedAction: null,
      participants: addressChanges,
    })
  }

  // Salary changes
  const salaryChanges = participants.value.filter(p => p.salaryChange)
  if (salaryChanges.length > 0) {
    patterns.push({
      field: 'Salary',
      count: salaryChanges.length,
      avgChange: '4.2%',
      selectedAction: null,
      participants: salaryChanges,
    })
  }

  // Phone changes
  const phoneChanges = participants.value.filter(p => p.phoneChange)
  if (phoneChanges.length > 0) {
    patterns.push({
      field: 'Phone',
      count: phoneChanges.length,
      avgChange: '0.0%',
      selectedAction: null,
      participants: phoneChanges,
    })
  }

  // Email changes
  const emailChanges = participants.value.filter(p => p.emailChange)
  if (emailChanges.length > 0) {
    patterns.push({
      field: 'Email',
      count: emailChanges.length,
      avgChange: '0.0%',
      selectedAction: null,
      participants: emailChanges,
    })
  }

  return patterns
}

const commonChanges = ref<ChangePattern[]>(generateChangePatterns())

const calculateDecisionSummary = () => {
  const approved = participants.value.filter(p => p.decision === 'Approved').length
  const rejected = participants.value.filter(p => p.decision === 'Rejected').length
  const flagged = participants.value.filter(p => p.decision === 'Flagged').length
  const pending = participants.value.filter(p => p.decision === 'Pending').length

  return { approved, rejected, flagged, pending }
}

const decisionSummary = ref(calculateDecisionSummary())

const selectAction = (pattern: ChangePattern, action: string) => {
  pattern.selectedAction = pattern.selectedAction === action ? null : action
}

const quickApproveAll = () => {
  commonChanges.value.forEach(change => {
    change.selectedAction = 'approve'
  })
  participants.value.forEach(p => p.decision = 'Approved')
  decisionSummary.value = calculateDecisionSummary()
}

const quickRejectAll = () => {
  commonChanges.value.forEach(change => {
    change.selectedAction = 'reject'
  })
  participants.value.forEach(p => p.decision = 'Rejected')
  decisionSummary.value = calculateDecisionSummary()
}

const quickFlagAll = () => {
  commonChanges.value.forEach(change => {
    change.selectedAction = 'flag'
  })
  participants.value.forEach(p => p.decision = 'Flagged')
  decisionSummary.value = calculateDecisionSummary()
}

const updateParticipantDecision = (participantId: number, decision: string) => {
  const participant = participants.value.find(p => p.id === participantId)
  if (participant) {
    participant.decision = decision
    decisionSummary.value = calculateDecisionSummary()
  }
}

const applyDecisions = () => {
  console.log('Decisions applied', commonChanges.value)
}

const getChangeValue = (participant: Participant, field: string) => {
  switch (field) {
    case 'Address':
      return participant.addressChange
  case 'Salary':
      return participant.salaryChange
  case 'Phone':
      return participant.phoneChange
  case 'Email':
      return participant.emailChange
  default:
      return null
  }
}

const totalChanges = 0
const noChanges = 0
const highRisk = 0
</script>

<template>
  <VContainer class="bulk-review-page">
    <!-- Header Section -->
    <VRow
      class="mb-4"
      align="center"
    >
      <VCol
        cols="12"
        md="6"
      >
        <div class="d-flex align-center">
          <VIcon
            icon="tabler-checklist"
            size="32"
            class="me-3 text-primary"
          />
          <div>
            <h1 class="text-h4 font-weight-bold">
              Certification Review for {{}}
            </h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Review common patterns and make bulk decisions for {{ participants.length }} participants
            </p>
          </div>
        </div>
      </VCol>
      <VCol
        cols="12"
        md="6"
        class="text-md-right"
      >
        <VBtn
          color="primary"
          prepend-icon="tabler-check"
          @click="applyDecisions"
        >
          Finalize Certification
        </VBtn>
      </VCol>
    </VRow>

    <!-- Stats Cards -->
    <VRow class="mb-6">
      <VCol
        cols="6"
        sm="3"
      >
        <VCard
          variant="flat"
          color="primary-lighten-5"
          class="pa-4"
        >
          <div class="d-flex align-center">
            <VIcon
              icon="tabler-users"
              size="32"
              class="me-3 text-primary"
            />
            <div>
              <div class="text-h4 font-weight-bold">
                {{ participants.length }}
              </div>
              <div class="text-subtitle-2">
                Participants
              </div>
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol
        cols="6"
        sm="3"
      >
        <VCard
          variant="flat"
          color="info-lighten-5"
          class="pa-4"
        >
          <div class="d-flex align-center">
            <VIcon
              icon="tabler-edit"
              size="32"
              class="me-3 text-info"
            />
            <div>
              <div class="text-h4 font-weight-bold">
                {{ totalChanges }}
              </div>
              <div class="text-subtitle-2">
                Total Changes
              </div>
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol
        cols="6"
        sm="3"
      >
        <VCard
          variant="flat"
          color="success-lighten-5"
          class="pa-4"
        >
          <div class="d-flex align-center">
            <VIcon
              icon="tabler-circle-check"
              size="32"
              class="me-3 text-success"
            />
            <div>
              <div class="text-h4 font-weight-bold">
                {{ noChanges }}
              </div>
              <div class="text-subtitle-2">
                No Changes
              </div>
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol
        cols="6"
        sm="3"
      >
        <VCard
          variant="flat"
          color="error-lighten-5"
          class="pa-4"
        >
          <div class="d-flex align-center">
            <VIcon
              icon="tabler-alert-triangle"
              size="32"
              class="me-3 text-error"
            />
            <div>
              <div class="text-h4 font-weight-bold">
                {{ highRisk }}
              </div>
              <div class="text-subtitle-2">
                High Risk
              </div>
            </div>
          </div>
        </VCard>
      </VCol>
    </VRow>

    <!-- Decision Summary -->
    <VCard
      class="mb-6"
      variant="flat"
    >
      <VCardTitle class="d-flex align-center">
        <VIcon
          icon="tabler-clipboard-list"
          size="24"
          class="me-2"
        />
        Certification Summary
      </VCardTitle>
      <VCardText>
        <VTable>
          <thead>
            <tr>
              <th class="text-center">
                Approved
              </th>
              <th class="text-center">
                Rejected
              </th>
              <th class="text-center">
                Flagged
              </th>
              <th class="text-center">
                Pending
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center text-h5 font-weight-bold text-success">
                {{ decisionSummary.approved }}
              </td>
              <td class="text-center text-h5 font-weight-bold text-error">
                {{ decisionSummary.rejected }}
              </td>
              <td class="text-center text-h5 font-weight-bold text-warning">
                {{ decisionSummary.flagged }}
              </td>
              <td class="text-center text-h5 font-weight-bold text-info">
                {{ decisionSummary.pending }}
              </td>
            </tr>
          </tbody>
        </VTable>
      </VCardText>
    </VCard>

    <!-- Common Change Patterns -->
    <VCard
      class="mb-6"
      variant="flat"
    >
      <VCardTitle class="d-flex align-center">
        <VIcon
          icon="tabler-zoom-money"
          size="24"
          class="me-2"
        />
        All Changes
      </VCardTitle>
      <VCardSubtitle>
        Apply bulk decisions to participants with similar changes
      </VCardSubtitle>
      <VCardText>
        <VRow>
          <VCol
            v-for="change in commonChanges"
            :key="change.field"
            cols="12"
            lg="6"
          >
            <VCard
              variant="outlined"
              class="h-100"
            >
              <VCardText>
                <div class="d-flex justify-space-between align-center mb-3">
                  <div>
                    <div class="text-h6 font-weight-bold">
                      {{ change.field }}
                    </div>
                    <div class="text-subtitle-2 text-medium-emphasis">
                      {{ change.count }} participants - Avg change: {{ change.avgChange }}
                    </div>
                  </div>
                  <VChip
                    v-if="change.selectedAction"
                    :color="change.selectedAction === 'approve' ? 'success'
                      : change.selectedAction === 'reject' ? 'error' : 'warning'"
                    size="small"
                  >
                    {{ change.selectedAction === 'approve' ? 'Approved'
                      : change.selectedAction === 'reject' ? 'Rejected' : 'Flagged' }}
                  </VChip>
                </div>

                <!-- Expandable participant list -->
                <VExpansionPanels
                  variant="accordion"
                  class="mt-3"
                >
                  <VExpansionPanel>
                    <VExpansionPanelTitle>
                      <VIcon
                        icon="tabler-users"
                        size="20"
                        class="me-2"
                      />
                      View {{ change.count }} Participants
                    </VExpansionPanelTitle>
                    <VExpansionPanelText>
                      <VList density="compact">
                        <VListItem
                          v-for="participant in change.participants"
                          :key="participant.id"
                          class="participant-item"
                        >
                          <template #prepend>
                            <VAvatar
                              size="32"
                              color="primary"
                            >
                              {{ participant.name.charAt(0) }}
                            </VAvatar>
                          </template>
                          <VListItemTitle>{{ participant.name }}</VListItemTitle>
                          <VListItemSubtitle>
                            <div class="change-details">
                              <div class="change-row">
                                <span class="label">Previous:</span>
                                <span class="value previous">{{ getChangeValue(participant, change.field)?.previous }}</span>
                              </div>
                              <div class="change-row">
                                <span class="label">Current:</span>
                                <span class="value current">{{ getChangeValue(participant, change.field)?.current }}</span>
                              </div>
                            </div>
                          </VListItemSubtitle>
                          <template #append>
                            <div class="d-flex align-center gap-2">
                              <VChip
                                :color="participant.decision === 'Approved' ? 'success'
                                  : participant.decision === 'Rejected' ? 'error'
                                    : participant.decision === 'Flagged' ? 'warning' : 'info'"
                                size="small"
                              >
                                {{ participant.decision }}
                              </VChip>
                              <div class="d-flex gap-1">
                                <VBtn
                                  icon="tabler-check"
                                  variant="text"
                                  color="success"
                                  size="small"
                                  density="comfortable"
                                  @click="updateParticipantDecision(participant.id, 'Approved')"
                                />
                                <VBtn
                                  icon="tabler-flag"
                                  variant="text"
                                  color="warning"
                                  size="small"
                                  density="comfortable"
                                  @click="updateParticipantDecision(participant.id, 'Flagged')"
                                />
                                <VBtn
                                  icon="tabler-x"
                                  variant="text"
                                  color="error"
                                  size="small"
                                  density="comfortable"
                                  @click="updateParticipantDecision(participant.id, 'Rejected')"
                                />
                              </div>
                            </div>
                          </template>
                        </VListItem>
                      </VList>
                    </VExpansionPanelText>
                  </VExpansionPanel>
                </VExpansionPanels>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VContainer>
</template>

<style scoped>
  .bulk-review-page {
    max-width: 1200px;
  }

  .v-table {
    --v-table-header-height: 48px;
  }

  .v-table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
  }

  .v-table td {
    padding: 12px 16px;
  }

  .v-card {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .participant-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .participant-item:last-child {
    border-bottom: none;
  }

  .change-details {
    margin-top: 4px;
  }

  .change-row {
    display: flex;
    gap: 8px;
    margin-bottom: 2px;
  }

  .label {
    font-weight: 500;
    min-width: 60px;
    color: rgba(0, 0, 0, 0.6);
  }

  .value {
    font-family: monospace;
    font-size: 0.875rem;
  }

  .previous {
    color: rgba(0, 0, 0, 0.5);
    text-decoration: line-through;
  }

  .current {
    color: rgba(0, 0, 0, 0.8);
    font-weight: 500;
  }

  .v-expansion-panels {
    box-shadow: none;
  }

  .v-expansion-panel {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }
</style>
