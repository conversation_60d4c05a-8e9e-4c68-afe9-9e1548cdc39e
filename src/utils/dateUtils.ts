/**
 * Utility functions for date handling and conversion
 */

/**
 * Converts a date value to ISO string format
 * @param dateValue - The date value to convert (string, Date, null, or undefined)
 * @returns ISO string or null if invalid/empty
 */
export const toISOString = (dateValue: string | Date | null | undefined): string | null => {
  if (!dateValue)
    return null

  try {
    let date: Date

    // Handle different input types
    if (dateValue instanceof Date) {
      date = dateValue
    }
    else if (typeof dateValue === 'string') {
      // Handle empty strings
      if (dateValue.trim() === '')
        return null

      // Try to parse the string as a date
      date = new Date(dateValue)
    }
    else {
      return null
    }

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date value:', dateValue)

      return null
    }

    return date.toISOString()
  }
  catch (e) {
    console.error('Error converting date to ISO:', dateValue, e)

    return null
  }
}

/**
 * Converts multiple date fields in an object to ISO format
 * @param obj - Object containing date fields
 * @param dateFields - Array of field names that contain dates
 * @returns New object with converted date fields
 */
export const convertDatesToISO = (obj: any, dateFields: string[]): any => {
  if (!obj || typeof obj !== 'object')
    return obj

  const result = { ...obj }

  dateFields.forEach(field => {
    if (result[field])
      result[field] = toISOString(result[field])
  })

  return result
}

/**
 * Safely converts an array of objects with date fields to ISO format
 * @param array - Array of objects
 * @param dateFields - Array of field names that contain dates
 * @returns New array with converted date fields
 */
export const convertArrayDatesToISO = (array: any[], dateFields: string[]): any[] => {
  if (!Array.isArray(array))
    return []

  return array.map(item => convertDatesToISO(item, dateFields))
}

/**
 * Validates if a date string is in valid ISO format
 * @param dateString - The date string to validate
 * @returns true if valid ISO format, false otherwise
 */
export const isValidISODate = (dateString: string): boolean => {
  if (!dateString || typeof dateString !== 'string')
    return false

  try {
    const date = new Date(dateString)

    return !isNaN(date.getTime()) && dateString === date.toISOString()
  }
  catch {
    return false
  }
}
