import * as XLSX from 'xlsx'

export interface ParticipantExcelData {

  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  maritalStatus: string
  birthDate: string

  // Address
  street: string
  houseNumber: string
  postalCode: string
  city: string
  state: string
  country: string

  // Employment Information
  employeeId: string
  department: string
  position: string
  startDate: string
  status: string

  // Salary Entry
  salaryYear: number
  salaryAmount: number
  partTimePercentage: number

  // Pension Information
  pensionCode: number
  codeEffectiveDate: string

  // Partner Information (optional)
  partnerFirstName?: string
  partnerLastName?: string
  partnerDateOfBirth?: string
  partnerStartDate?: string
  partnerIsCurrent?: boolean
  partnerIsDeceased?: boolean

  // Child Information (optional)
  childFirstName?: string
  childLastName?: string
  childDateOfBirth?: string
  childIsOrphan?: boolean
  childIsStudying?: boolean
}

// Pension codes mapping for automatic description lookup
export const pensionCodes = [
  {
    code: 10,
    description: 'Active',
    allowedTransitions: [30, 11, 40, 70],
    impact: 'Active pension accrual and indexation',
  },
  {
    code: 11,
    description: 'Disabled',
    allowedTransitions: [40, 70],
    impact: 'InActive service but continued pension accrual and indexation',
  },
  {
    code: 30,
    description: 'Inactive',
    allowedTransitions: [11, 40, 70],
    impact: 'No further pension accrual, only indexation',
  },
  {
    code: 40,
    description: 'Retired',
    allowedTransitions: [70],
    impact: 'No further pension accrual, only indexation',
  },
  {
    code: 50,
    description: 'Partner entitled to partner\'s pension',
    allowedTransitions: [70],
    impact: 'Partner will be added as new participant when pension starts',
  },
  {
    code: 55,
    description: 'Orphan entitled to orphan\'s pension',
    allowedTransitions: [70],
    impact: 'Orphan will be added as new participant when pension starts',
  },
  {
    code: 70,
    description: 'No longer entitled to pension',
    allowedTransitions: [],
    impact: 'No pension accrual or payments (e.g., due to death)',
  },
]

export const getPensionCodeDescription = (code: number): string => {
  const pensionCode = pensionCodes.find(pc => pc.code === code)

  return pensionCode ? pensionCode.description : ''
}

/**
 * Generates and downloads an Excel template for participant data
 */
export const exportParticipantTemplate = () => {
  // Create template data with multiple examples showing different scenarios
  const templateData = [
    {
      // Personal Information
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+************',
      maritalStatus: 'Married',
      birthDate: '1990-01-15',

      // Address
      street: 'Main Street',
      houseNumber: '123',
      postalCode: '12345',
      city: 'Oranjestad',
      state: 'Aruba',
      country: 'Aruba',

      // Employment Information
      employeeId: 'EMP001',
      department: 'IT Department',
      position: 'Software Developer',
      startDate: '2020-01-01',
      status: 'Active',

      // Salary Entry (First year)
      salaryYear: 2020,
      salaryAmount: 5000,
      partTimePercentage: 100,

      // Pension Information (code description will be auto-filled)
      pensionCode: 10,
      codeEffectiveDate: '2020-01-01',

      // Partner Information (First partner)
      partnerFirstName: 'Jane',
      partnerLastName: 'Doe',
      partnerDateOfBirth: '1992-03-20',
      partnerStartDate: '2015-06-01',
      partnerIsCurrent: true,
      partnerIsDeceased: false,

      // Child Information (First child)
      childFirstName: 'Little',
      childLastName: 'Doe',
      childDateOfBirth: '2018-08-10',
      childIsOrphan: false,
      childIsStudying: true,
    },

    // Example row for additional salary entry (same participant)
    {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+************',
      maritalStatus: 'Married',
      birthDate: '1990-01-15',
      street: 'Main Street',
      houseNumber: '123',
      postalCode: '12345',
      city: 'Oranjestad',
      state: 'Aruba',
      country: 'Aruba',
      employeeId: 'EMP001',
      department: 'IT Department',
      position: 'Software Developer',
      startDate: '2020-01-01',
      status: 'Active',

      // Different salary year
      salaryYear: 2021,
      salaryAmount: 5500,
      partTimePercentage: 100,

      pensionCode: 10,
      codeEffectiveDate: '2020-01-01',

      // Leave partner and child fields empty for additional salary entries
      partnerFirstName: '',
      partnerLastName: '',
      partnerDateOfBirth: '',
      partnerStartDate: '',
      partnerIsCurrent: '',
      partnerIsDeceased: '',
      childFirstName: '',
      childLastName: '',
      childDateOfBirth: '',
      childIsOrphan: '',
      childIsStudying: '',
    },

    // Example row for additional partner (same participant)
    {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+************',
      maritalStatus: 'Married',
      birthDate: '1990-01-15',
      street: 'Main Street',
      houseNumber: '123',
      postalCode: '12345',
      city: 'Oranjestad',
      state: 'Aruba',
      country: 'Aruba',
      employeeId: 'EMP001',
      department: 'IT Department',
      position: 'Software Developer',
      startDate: '2020-01-01',
      status: 'Active',

      // Leave salary fields empty for additional partner entries
      salaryYear: '',
      salaryAmount: '',
      partTimePercentage: '',

      pensionCode: 10,
      codeEffectiveDate: '2020-01-01',

      // Second partner (ex-partner)
      partnerFirstName: 'Mary',
      partnerLastName: 'Smith',
      partnerDateOfBirth: '1988-12-15',
      partnerStartDate: '2010-01-01',
      partnerIsCurrent: false,
      partnerIsDeceased: false,

      // Leave child fields empty
      childFirstName: '',
      childLastName: '',
      childDateOfBirth: '',
      childIsOrphan: '',
      childIsStudying: '',
    },
  ]

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(templateData)

  // Add instructions sheet
  const instructions = [
    { Field: 'Instructions', Value: 'Please fill in the participant data according to the template below' },
    { Field: '', Value: '' },
    { Field: 'IMPORTANT', Value: 'Multiple rows can be used for the same participant to add multiple partners, children, or salary entries' },
    { Field: '', Value: '' },
    { Field: 'Required Fields', Value: 'firstName, lastName, email, employeeId, department, position, startDate, pensionCode, codeEffectiveDate' },
    { Field: '', Value: '' },
    { Field: 'Multi-Row Format', Value: 'Use multiple rows for the same participant (same firstName, lastName, email, employeeId) to add:' },
    { Field: '- Multiple Salary Entries', Value: 'Fill salaryYear, salaryAmount, partTimePercentage. Leave partner/child fields empty.' },
    { Field: '- Multiple Partners', Value: 'Fill partner fields. Leave salary and child fields empty.' },
    { Field: '- Multiple Children', Value: 'Fill child fields. Leave salary and partner fields empty.' },
    { Field: '', Value: '' },
    { Field: 'Date Format', Value: 'YYYY-MM-DD (e.g., 2024-01-15)' },
    { Field: 'Marital Status Options', Value: 'Single, Married, Divorced, Widowed, Separated' },
    { Field: 'Employment Status Options', Value: 'Active, Inactive, Retired' },
    { Field: 'Boolean Fields', Value: 'Use TRUE or FALSE for partnerIsCurrent, partnerIsDeceased, childIsOrphan, childIsStudying' },
    { Field: '', Value: '' },
    { Field: 'Pension Codes', Value: '10=Active, 11=Disabled, 30=Inactive, 40=Retired, 50=Partner Pension, 55=Orphan Pension, 70=No Pension' },
    { Field: 'Code Description', Value: 'DO NOT fill codeDescription - it will be automatically determined from the pension code' },
    { Field: '', Value: '' },
    { Field: 'Example Structure', Value: 'Row 1: Basic info + first salary + first partner + first child' },
    { Field: '', Value: 'Row 2: Same basic info + second salary (empty partner/child fields)' },
    { Field: '', Value: 'Row 3: Same basic info + second partner (empty salary/child fields)' },
    { Field: '', Value: 'Row 4: Same basic info + second child (empty salary/partner fields)' },
  ]

  const instructionsSheet = XLSX.utils.json_to_sheet(instructions)

  // Add pension codes reference sheet
  const pensionCodesData = pensionCodes.map(pc => ({
    'Code': pc.code,
    'Description': pc.description,
    'Impact': pc.impact,
    'Allowed Transitions': pc.allowedTransitions.join(', '),
  }))

  const pensionCodesSheet = XLSX.utils.json_to_sheet(pensionCodesData)

  // Add sheets to workbook
  XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions')
  XLSX.utils.book_append_sheet(workbook, pensionCodesSheet, 'Pension Codes')
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Participant Template')

  // Generate filename with current date
  const today = new Date().toISOString().split('T')[0]
  const filename = `participant_template_${today}.xlsx`

  // Download the file
  XLSX.writeFile(workbook, filename)
}

/**
 * Parses an uploaded Excel file and returns grouped participant data
 */
export const parseParticipantExcel = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // Get the participant template sheet
        const sheetName = workbook.SheetNames.find(name =>
          name.toLowerCase().includes('template')
          || name.toLowerCase().includes('participant'),
        ) || workbook.SheetNames[workbook.SheetNames.length - 1] // Last sheet if no template found

        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as ParticipantExcelData[]

        // Filter out empty rows and validate required fields
        const validData = jsonData.filter(row =>
          row.firstName
          && row.lastName
          && row.email
          && row.employeeId,
        )

        if (validData.length === 0)
          throw new Error('No valid participant data found in the Excel file')

        // Group rows by participant (using employeeId as unique identifier)
        const groupedData = groupParticipantRows(validData)

        resolve(groupedData)
      }
      catch (error) {
        reject(new Error(`Error parsing Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`))
      }
    }

    reader.onerror = () => {
      reject(new Error('Error reading file'))
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * Groups multiple rows for the same participant into a single participant object
 */
const groupParticipantRows = (rows: ParticipantExcelData[]) => {
  const participantMap = new Map<string, any>()

  rows.forEach(row => {
    const participantKey = `${row.employeeId}-${row.firstName}-${row.lastName}`

    if (!participantMap.has(participantKey)) {
      // Initialize participant with basic info
      participantMap.set(participantKey, {
        personalInfo: {
          firstName: row.firstName,
          lastName: row.lastName,
          email: row.email,
          phone: row.phone || '',
          maritalStatus: row.maritalStatus || '',
          birthDate: row.birthDate || '',
          partners: [],
          children: [],
          address: {
            street: row.street || '',
            houseNumber: row.houseNumber || '',
            postalCode: row.postalCode || '',
            city: row.city || '',
            state: row.state || '',
            country: row.country || '',
          },
        },
        employmentInfo: {
          employeeId: row.employeeId,
          department: row.department || '',
          position: row.position || '',
          startDate: row.startDate || '',
          status: row.status || 'Active',
          salaryEntries: [],
        },
        pensionInfo: {
          code: row.pensionCode || null,
          codeDescription: getPensionCodeDescription(row.pensionCode),
          codeEffectiveDate: row.codeEffectiveDate || '',
          pensionBase: null,
        },
      })
    }

    const participant = participantMap.get(participantKey)

    // Add salary entry if present
    if (row.salaryYear && row.salaryAmount) {
      const existingSalary = participant.employmentInfo.salaryEntries.find(
        (entry: any) => entry.year === row.salaryYear,
      )

      if (!existingSalary) {
        participant.employmentInfo.salaryEntries.push({
          year: row.salaryYear,
          amount: row.salaryAmount,
          partTimePercentage: row.partTimePercentage || 100,
        })
      }
    }

    // Add partner if present
    if (row.partnerFirstName && row.partnerLastName) {
      const existingPartner = participant.personalInfo.partners.find(
        (partner: any) =>
          partner.firstName === row.partnerFirstName
          && partner.lastName === row.partnerLastName
          && partner.dateOfBirth === row.partnerDateOfBirth,
      )

      if (!existingPartner) {
        participant.personalInfo.partners.push({
          firstName: row.partnerFirstName,
          lastName: row.partnerLastName,
          dateOfBirth: row.partnerDateOfBirth || '',
          startDate: row.partnerStartDate || '',
          isCurrent: row.partnerIsCurrent || false,
          isDeceased: row.partnerIsDeceased || false,
        })
      }
    }

    // Add child if present
    if (row.childFirstName && row.childLastName) {
      const existingChild = participant.personalInfo.children.find(
        (child: any) =>
          child.firstName === row.childFirstName
          && child.lastName === row.childLastName
          && child.dateOfBirth === row.childDateOfBirth,
      )

      if (!existingChild) {
        participant.personalInfo.children.push({
          firstName: row.childFirstName,
          lastName: row.childLastName,
          dateOfBirth: row.childDateOfBirth || '',
          isOrphan: row.childIsOrphan || false,
          isStudying: row.childIsStudying || false,
        })
      }
    }
  })

  return Array.from(participantMap.values())
}

/**
 * Converts grouped Excel data to form data format
 * The data is already in the correct format from groupParticipantRows
 */
export const convertExcelToFormData = (groupedData: any) => {
  // Ensure at least one salary entry exists
  if (groupedData.employmentInfo.salaryEntries.length === 0) {
    groupedData.employmentInfo.salaryEntries.push({
      year: new Date().getFullYear(),
      amount: null,
      partTimePercentage: 100,
    })
  }

  // Ensure pension code description is set
  if (groupedData.pensionInfo.code && !groupedData.pensionInfo.codeDescription)
    groupedData.pensionInfo.codeDescription = getPensionCodeDescription(groupedData.pensionInfo.code)

  return groupedData
}
