import { useRouter } from 'vue-router'
import { computed, ref } from 'vue'
import { useRoleAccess } from './useRoleAccess'

export const useRouteGuard = () => {
  const { canAccessRoute, canPerformAction, currentRole } = useRoleAccess()
  const router = useRouter()

  const isPermissionRestricted = ref(false)
  const restrictedReason = ref('')

  const checkRouteAccess = (routeName: string): boolean => {
    const hasAccess = canAccessRoute(routeName)

    if (!hasAccess) {
      isPermissionRestricted.value = true
      restrictedReason.value = `Access to ${routeName} is restricted for your role (${currentRole.value})`
    }

    return hasAccess
  }

  const checkActionAccess = (action: string): boolean => {
    const hasAccess = canPerformAction(action)

    if (!hasAccess) {
      isPermissionRestricted.value = true
      restrictedReason.value = `Action ${action} is restricted for your role (${currentRole.value})`
    }

    return hasAccess
  }

  const redirectToRestricted = () => {
    isPermissionRestricted.value = true
  }

  const redirectToDashboard = () => {
    router.push({ name: 'root' })
  }

  const resetRestriction = () => {
    isPermissionRestricted.value = false
    restrictedReason.value = ''
  }

  const shouldShowRestrictedPage = computed(() => isPermissionRestricted.value)

  return {
    checkRouteAccess,
    checkActionAccess,
    redirectToRestricted,
    redirectToDashboard,
    resetRestriction,
    shouldShowRestrictedPage,
    restrictedReason,
    isPermissionRestricted,
  }
}
