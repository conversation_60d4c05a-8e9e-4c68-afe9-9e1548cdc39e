# Role-Based Access Control (RBAC)

This directory contains the role-based access control system for the pension admin application.

## Usage

### Basic Setup

```typescript
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

const {
  currentRole,
  isAdmin,
  is<PERSON><PERSON><PERSON><PERSON>,
  isAccountant,
  isEditor,
  canAccessParticipants,
  canCreateParticipant,
  canApproveChanges
} = useRoleAccess()
```

### Available Roles

- **`admin`** - Full system access including user management and system configuration
- **`reviewer`** - Can review and approve/reject change proposals and manage certifications
- **`accountant`** - Manages pension parameters, financial calculations, and generates reports
- **`editor`** - Can create and edit participant data, create change proposals

### Route Protection

```vue
<template>
  <!-- Only show if user can access participants -->
  <VBtn v-if="canAccessParticipants" to="/participants">
    Participants
  </VBtn>
  
  <!-- Only show for admins -->
  <VBtn v-if="isAdmin" to="/users">
    User Management
  </VBtn>
  
  <!-- Only show for accountants/admins -->
  <VBtn v-if="isAccountant || isAdmin" to="/pension-parameters">
    Pension Parameters
  </VBtn>
</template>
```

### Action Permissions

```vue
<template>
  <!-- Only show create button if user can create participants -->
  <VBtn 
    v-if="canCreateParticipant"
    @click="createParticipant"
    color="primary"
  >
    Create Participant
  </VBtn>
  
  <!-- Only show approve/reject buttons for reviewers/admins -->
  <VBtn v-if="canApproveChanges" @click="approve">Approve</VBtn>
  <VBtn v-if="canRejectChanges" @click="reject">Reject</VBtn>
</template>
```

### Navigation Filtering

Navigation is automatically filtered in the layout components based on user roles.

### Manual Permission Checks

```typescript
// Check specific route access
if (canAccessRoute('pension-parameters')) {
  // User can access pension parameters
}

// Check specific action permission
if (canPerformAction('edit-participant')) {
  // User can edit participants
}
```

## Adding New Permissions

### 1. Add New Routes

Edit the `ROLE_PERMISSIONS` object in `useRoleAccess.ts`:

```typescript
const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
  admin: {
    routes: [
      // ... existing routes
      'new-route-name'
    ],
    // ...
  }
}
```

### 2. Add New Actions

```typescript
const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
  admin: {
    actions: [
      // ... existing actions
      'new-action-name'
    ],
    // ...
  }
}
```

### 3. Add Computed Properties (Optional)

For frequently used permissions, add computed properties:

```typescript
const canAccessNewFeature = computed(() => canAccessRoute('new-feature'))
const canPerformNewAction = computed(() => canPerformAction('new-action'))

return {
  // ... existing returns
  canAccessNewFeature,
  canPerformNewAction
}
```

## Role Permission Matrix

| Feature | Admin | Reviewer | Accountant | Editor |
|---------|-------|----------|------------|--------|
| Dashboard | ✅ | ✅ | ✅ | ✅ |
| View Participants | ✅ | ✅ | ✅ | ✅ |
| Create Participants | ✅ | ❌ | ❌ | ✅ |
| Edit Participants | ✅ | ❌ | ❌ | ✅ |
| Delete Participants | ✅ | ❌ | ❌ | ❌ |
| Export Data | ✅ | ❌ | ✅ | ✅ |
| View Pension Parameters | ✅ | ❌ | ✅ | ❌ |
| Edit Pension Parameters | ✅ | ❌ | ✅ | ❌ |
| Change Proposals | ✅ | ✅ | ✅ | ✅ |
| Create Change Proposals | ✅ | ❌ | ✅ | ✅ |
| Approve/Reject Changes | ✅ | ✅ | ❌ | ❌ |
| User Management | ✅ | ❌ | ❌ | ❌ |
| Certification Management | ✅ | ✅ | ❌ | ❌ |
| Financial Calculations | ✅ | ❌ | ✅ | ❌ |
| Generate Reports | ✅ | ❌ | ✅ | ❌ |

## Security Notes

- The composable defaults to `editor` role if no role is found in claims
- Always use the composable for both UI hiding AND backend validation
- Route guards should also be implemented for additional security
- Never trust frontend-only role checks for sensitive operations