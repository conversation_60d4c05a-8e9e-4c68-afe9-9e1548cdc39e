import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth/authStore'

export type UserRole = 'admin' | 'reviewer' | 'accountant' | 'editor'

interface RolePermissions {
  routes: string[]
  actions: string[]
}

const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
  admin: {
    routes: [
      'root', // Dashboard
      'participants',
      'participant-export',
      'pension-parameters',
      'change-proposals',
      'change-proposals-requested',
      'change-proposals-history',
      'change-proposals-new-participants',
      'change-proposals-new-pension-parameters',
      'users',
      'certification-management',
      'notifications',
    ],
    actions: [
      'create-participant',
      'edit-participant',
      'delete-participant',
      'export-data',
      'edit-pension-parameters',
      'approve-changes',
      'reject-changes',
      'create-change-proposal',
      'manage-users',
      'manage-certifications',
      'view-all-data',
      'system-configuration',
    ],
  },

  reviewer: {
    routes: [
      'root', // Dashboard
      'participants',
      'participant-export',
      'pension-parameters',
      'change-proposals',
      'change-proposals-requested',
      'change-proposals-history',
      'change-proposals-new-participants',
      'certification-management',
      'notifications',
    ],
    actions: [
      'export-data',
      'approve-changes',
      'reject-changes',
      'view-all-data',
    ],
  },

  accountant: {
    routes: [
      'root', // Dashboard
      'participants',
      'participant-export',
      'pension-parameters',
      'certification-management',
      'notifications',
    ],
    actions: [
      'export-data',
      'view-all-data',
    ],
  },

  editor: {
    routes: [
      'root', // Dashboard
      'participants',
      'participant-export',
      'pension-parameters',
      'change-proposals',
      'change-proposals-requested',
      'change-proposals-history',
      'change-proposals-new-participants',
      'certification-management',
      'notifications',
    ],
    actions: [
      'create-participant',
      'edit-participant',
      'view-participants',
      'export-data',
      'create-change-proposal',
      'view-change-proposals',
      'edit-participant-data',
      'edit-pension-parameters',
      'data-entry',
    ],
  },
}

export const useRoleAccess = () => {
  const authStore = useAuthStore()

  const currentRole = computed(() => {
    const role = authStore.claims.role as UserRole

    return role || 'reviewer'
  })

  // Get current user permissions
  const currentPermissions = computed(() => {
    return ROLE_PERMISSIONS[currentRole.value] || ROLE_PERMISSIONS.editor
  })

  // Route access helpers
  const canAccessRoute = (routeName: string): boolean => {
    return currentPermissions.value.routes.includes(routeName)
  }

  const canPerformAction = (action: string): boolean => {
    return currentPermissions.value.actions.includes(action)
  }

  // Specific page access computed properties
  const canAccessDashboard = computed(() => canAccessRoute('root'))
  const canAccessParticipants = computed(() => canAccessRoute('participants'))
  const canAccessParticipantExport = computed(() => canAccessRoute('participant-export'))
  const canAccessPensionParameters = computed(() => canAccessRoute('pension-parameters'))
  const canAccessChangeProposals = computed(() => canAccessRoute('change-proposals'))
  const canAccessUserManagement = computed(() => canAccessRoute('users'))
  const canAccessCertificationManagement = computed(() => canAccessRoute('certification-management'))
  const canAccessNotifications = computed(() => canAccessRoute('notifications'))

  // Specific action permissions
  const canCreateParticipant = computed(() => canPerformAction('create-participant'))
  const canEditParticipant = computed(() => canPerformAction('edit-participant'))
  const canDeleteParticipant = computed(() => canPerformAction('delete-participant'))
  const canExportData = computed(() => canPerformAction('export-data'))
  const canEditPensionParameters = computed(() => canPerformAction('edit-pension-parameters'))
  const canApproveChanges = computed(() => canPerformAction('approve-changes'))
  const canRejectChanges = computed(() => canPerformAction('reject-changes'))
  const canReviewPensionParameters = computed(() => canPerformAction('approve-changes') || canPerformAction('reject-changes'))
  const canCreateChangeProposal = computed(() => canPerformAction('create-change-proposal'))
  const canManageUsers = computed(() => canPerformAction('manage-users'))
  const canManageCertifications = computed(() => canPerformAction('manage-certifications'))
  const canViewAllData = computed(() => canPerformAction('view-all-data'))

  const isAdmin = computed(() => currentRole.value === 'admin')
  const isReviewer = computed(() => currentRole.value === 'reviewer')
  const isAccountant = computed(() => currentRole.value === 'accountant')
  const isEditor = computed(() => currentRole.value === 'editor')

  // Navigation filtering helper
  const filterNavigationByRole = (navItems: any[]) => {
    return navItems.filter(item => {
      // Handle nested navigation items
      if (item.children) {
        const filteredChildren = item.children.filter((child: any) => {
          const routeName = child.routeName || (typeof child.to === 'string' ? child.to : child.to?.name)

          return routeName ? canAccessRoute(routeName) : false
        })

        // Only show parent if it has accessible children
        if (filteredChildren.length > 0)
          return { ...item, children: filteredChildren }

        return false
      }

      const routeName = item.routeName || (typeof item.to === 'string' ? item.to : item.to?.name)

      return routeName ? canAccessRoute(routeName) : false
    }).filter(Boolean)
  }

  return {
    // Current role info
    currentRole,
    currentPermissions,

    // Role checks
    isAdmin,
    isReviewer,
    isAccountant,
    isEditor,

    // Route access
    canAccessRoute,
    canAccessDashboard,
    canAccessParticipants,
    canAccessParticipantExport,
    canAccessPensionParameters,
    canAccessChangeProposals,
    canAccessUserManagement,
    canAccessCertificationManagement,
    canAccessNotifications,

    // Action permissions
    canPerformAction,
    canCreateParticipant,
    canEditParticipant,
    canDeleteParticipant,
    canExportData,
    canEditPensionParameters,
    canApproveChanges,
    canRejectChanges,
    canReviewPensionParameters,
    canCreateChangeProposal,
    canManageUsers,
    canManageCertifications,
    canViewAllData,

    // Utilities
    filterNavigationByRole,

    // For manual role permission checks
    ROLE_PERMISSIONS,
  }
}
