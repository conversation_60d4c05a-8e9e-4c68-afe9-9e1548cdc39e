import { transformObjectToArray } from '@/utils/transformers'
import { usePensionParameterGraph } from '@/api/graphHooks/usePensionParameterGraph'
import { useAuthStore } from '@/stores/auth/authStore'

export const usePensionParameters = () => {
  const authStore = useAuthStore()
  
  const {
    state: {
      loadingPensionParameters,
      loadingPendingPensionParameters,
      loadingApprovePensionParameters,
      loadingRejectPensionParameters,
      allPensionParameters,
      pendingPensionParameters,
    },
    actions: {
      refetchPensionParameters,
      refetchPendingPensionParameters,
      approvePensionParameters: approvePensionParametersGraph,
      rejectPensionParameters: rejectPensionParametersGraph,
    },
  } = usePensionParameterGraph()

  const pensionParamsTransformed = computed(() => {
    return allPensionParameters.value.map((param: any) => {
      return transformObjectToArray(param)
    })
  })

  const normalizePensionParams = computed(() => {
    return allPensionParameters.value.reduce((acc: Record<number, any>, param: any) => {
      const year = Number.parseInt(param.year)

      acc[year] = param

      return acc
    }, {})
  })

  const availableStatuses = ['Active', 'Pending', 'Inactive']

  const approvePensionParameters = async (id: string) => {
    return await approvePensionParametersGraph({ id })
  }

  const rejectPensionParameters = async (id: string, rejectReason: string) => {
    const userId = authStore.user?.uid
    if (!userId) {
      throw new Error('User not authenticated')
    }
    return await rejectPensionParametersGraph({ id, rejectReason, userId })
  }

  return {
    state: {
      pensionParamsTransformed,
      allPensionParameters,
      pendingPensionParameters,
      availableStatuses,
      normalizePensionParams,
      loadingPensionParameters,
      loadingPendingPensionParameters,
      loadingApprovePensionParameters,
      loadingRejectPensionParameters,
    },
    actions: {
      refetchPensionParameters,
      refetchPendingPensionParameters,
      approvePensionParameters,
      rejectPensionParameters,
    },
  }
}
