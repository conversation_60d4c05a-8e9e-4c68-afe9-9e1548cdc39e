import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useMutation, useQuery } from '@vue/apollo-composable'
import { useCertifiedDataGraph } from '@/api/graphHooks/useCertifiedDataGraph'
import { usePensionStore } from '@/stores/pension/pensionStore'
import {
  REVERT_SINGLE_FIELD,
  UPDATE_CERTIFIED_DATA_APPROVED_CHANGES,
  UPDATE_CERTIFIED_DATA_REJECTED_CHANGES,
} from '@/api/graphql/mutations/certifiedDataMutations'
import { GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE } from '@/api/graphql/queries/certifiedDataQueries'

export const useCertifiedDataByYearAndYearBefore = () => {
  const pensionStore = usePensionStore()
  const route = useRoute()

  const certificationYear = computed(() => {
    return Number.parseInt(route.params.year as string)
  })

  const {
    state: {
      certifiedDataByYearAndYearBefore,
      loadingCertifiedDataByYearAndYearBefore,
      loadingUpdateCertifiedDataApprovedChanges,
      loadingUpdateCertifiedDataRejectedChanges,
      loadingPreviewRevertChanges,
      loadingRevertChanges,
      previewRevertChangesData,
    },
    actions: {
      refetchCertifiedDataByYearAndYearBefore,
      updateCertifiedDataApprovedChanges,
      updateCertifiedDataRejectedChanges,
      previewRevertChanges,
      revertApprovedRejectedChanges,
    },
  } = useCertifiedDataGraph()

  const certifiedDataByYearAndYearBeforeData = ref<any>(null)

  const { mutate: approveCertifiedField } = useMutation(
    UPDATE_CERTIFIED_DATA_APPROVED_CHANGES,
  )

  const { mutate: rejectCertifiedField } = useMutation(
    UPDATE_CERTIFIED_DATA_REJECTED_CHANGES,
  )

  const { mutate: revertSingleField } = useMutation(REVERT_SINGLE_FIELD)

  // Store the data in the pension store when it changes
  watch(certifiedDataByYearAndYearBefore, newData => {
    if (newData)
      pensionStore.setCertifiedDataByYearAndYearBefore(newData)
  }, { immediate: true })

  // Get the data from the pension store or from the query result
  const certifiedDataByYearAndYearBeforeDataComputed = computed(() => {
    return pensionStore.certifiedDataByYearAndYearBefore || certifiedDataByYearAndYearBefore.value
  })

  // Function to fetch data for a specific year
  const fetchCertifiedDataByYearAndYearBefore = async (year: number) => {
    const { data } = useQuery(GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE, {
      certificationYear: year,
    }, { enabled: !!year })

    certifiedDataByYearAndYearBeforeData.value = data.value

    return data.value
  }

  const handleApproveCertifiedField = async (id: string, fieldName: string, entityType?: string, entityId?: string) => {
    try {
      const { data } = await approveCertifiedField({
        id,
        approvedChanges: [fieldName],
        entityType,
        entityId,
      })

      await fetchCertifiedDataByYearAndYearBefore(certificationYear.value)

      return data
    }
    catch (error) {
      console.error('Error approving certified field:', error)
      throw error
    }
  }

  const handleRejectCertifiedField = async (id: string, fieldName: string, entityType?: string, rejectReason?: string, entityId?: string) => {
    try {
      const { data } = await rejectCertifiedField({
        id,
        rejectedChanges: [fieldName],
        entityType,
        rejectReason,
        entityId,
      })

      await fetchCertifiedDataByYearAndYearBefore(certificationYear.value)

      return data
    }
    catch (error) {
      console.error('Error rejecting certified field:', error)
      throw error
    }
  }

  // Function to preview revert changes
  const handlePreviewRevertChanges = async () => {
    try {
      const result = await previewRevertChanges({
        certificationYear: certificationYear.value,
      })

      return result?.data?.previewRevertChanges
    }
    catch (error) {
      console.error('Error previewing revert changes:', error)
      throw error
    }
  }

  // Function to revert all changes
  const handleRevertAllChanges = async () => {
    try {
      const result = await revertApprovedRejectedChanges({
        certificationYear: certificationYear.value,
      })

      if (result?.data?.revertApprovedRejectedChanges.success)
        await fetchCertifiedDataByYearAndYearBefore(certificationYear.value)

      return result?.data?.revertApprovedRejectedChanges
    }
    catch (error) {
      console.error('Error reverting all changes:', error)
      throw error
    }
  }

  const handleRevertSingleField = async (input: {
    entityId: string
    entityType: string
    path: string
  }) => {
    try {
      const { data } = await revertSingleField({
        input,
      })

      return data
    }
    catch (error) {
      console.error('Error reverting field:', error)
      throw error
    }
  }

  return {
    state: {
      certifiedDataByYearAndYearBeforeData: certifiedDataByYearAndYearBeforeDataComputed,
      loadingCertifiedDataByYearAndYearBefore,
      loadingUpdateCertifiedDataApprovedChanges,
      loadingUpdateCertifiedDataRejectedChanges,
      loadingPreviewRevertChanges,
      loadingRevertChanges,
      previewRevertChangesData,
    },
    actions: {
      fetchCertifiedDataByYearAndYearBefore,
      handleApproveCertifiedField,
      handleRejectCertifiedField,
      handlePreviewRevertChanges,
      handleRevertAllChanges,
      handleRevertSingleField,
    },
  }
}
