import { readonly, ref } from 'vue'
import type { Channel } from 'pusher-js'
import { pusherService } from '@/services/pusher'
import { useAuthStore } from '@/stores/auth/authStore'

export function usePusherNotifications() {
  const authStore = useAuthStore()
  const channel = ref<Channel | null>(null)
  const isConnected = ref(false)
  const connectionState = ref('disconnected')

  const subscribeToNotifications = (onNotificationReceived: (notification: any) => void) => {
    const userId = authStore.claims.pensionUserId

    if (!userId) {
      console.warn('No user ID available for Pusher subscription')

      return
    }

    try {
      // Subscribe to user-specific public channel (no authentication required)
      channel.value = pusherService.subscribeToUserChannel(userId, {
        onNotificationCreated: data => {
          onNotificationReceived(data)
        },
      })

      if (channel.value) {
        isConnected.value = true
        console.log(`Subscribed to public user channel: user-${userId}`)
      }
    }
    catch (error) {
      console.error('Error subscribing to notifications:', error)
    }
  }

  // Watch for connection state changes
  const watchConnectionState = () => {
    const interval = setInterval(() => {
      const currentState = pusherService.getCurrentConnectionState()

      connectionState.value = currentState

      if (currentState === 'connected' && !isConnected.value)
        isConnected.value = true
      else if (currentState !== 'connected' && isConnected.value)
        isConnected.value = false
    }, 1000)

    return () => clearInterval(interval)
  }

  const unsubscribe = () => {
    if (channel.value) {
      const userId = authStore.claims.pensionUserId
      if (userId)
        pusherService.unsubscribeFromChannel(`user-${userId}`)

      channel.value = null
      isConnected.value = false
    }
  }

  const forceReconnect = () => {
    console.log('Forcing Pusher reconnection...')
    unsubscribe()
    pusherService.forceReconnect()
  }

  // Start watching connection state
  const stopWatching = watchConnectionState()

  //
  // // Clean up on unmount
  // onUnmounted(() => {
  //   unsubscribe()
  //   stopWatching()
  // })

  return {
    subscribeToNotifications,
    unsubscribe,
    forceReconnect,
    isConnected: readonly(isConnected),
    connectionState: readonly(connectionState),
  }
}
