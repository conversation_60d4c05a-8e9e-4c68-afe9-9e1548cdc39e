<script lang="ts" setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { formatDistance } from 'date-fns'
import { useAppStore } from '@/stores/app/appStore'
import { useNotifications } from '@/composables/notifications/useNotifications'
import { usePusherNotifications } from '@/composables/notifications/usePusherNotifications'
import { useSound } from '@/composables/useSound'

const props = withDefaults(defineProps<Props>(), {
  location: 'bottom end',
  badgeProps: undefined,
})

const router = useRouter()
const appStore = useAppStore()

interface Props {
  badgeProps?: object
  location?: any
}

const { play } = useSound()

// Get notifications functionality from our composable
const {
  state: {
    formattedNotifications,
    unreadCount,
    loadingUserNotifications,
    loadingOperations,
  },
  actions: {
    handleMarkAsRead,
    handleMarkAllAsRead,
    getNotificationIcon,
    getNotificationColor,
    refreshNotifications,
  },
} = useNotifications()

// Get Pusher functionality
const { subscribeToNotifications } = usePusherNotifications()

// Check if there are any unread notifications
const isAllMarkRead = computed(() => {
  return unreadCount.value > 0
})

// Mark all notifications as read or unread
const markAllReadOrUnread = async () => {
  if (isAllMarkRead.value) {
    try {
      await handleMarkAllAsRead()
      appStore.showSnack('All notifications marked as read')
    }
    catch (error) {
      console.error('Error marking all as read:', error)
      appStore.showSnack('Error updating notifications')
    }
  }
}

// Toggle notification read status
const toggleReadUnread = async (isRead: boolean, id: string) => {
  if (!isRead) {
    try {
      await handleMarkAsRead(id)
    }
    catch (error) {
      console.error('Error marking notification as read:', error)
      appStore.showSnack('Error updating notification')
    }
  }
}

// Format date for display
const formatNotificationDate = (date: string) => {
  return formatDistance(new Date(date), new Date(), { addSuffix: true })
}

// Handle notification click
const handleNotificationClick = async (notification: any) => {
  console.log('Notification clicked:', notification)

  if (notification.entityId && notification.entityType) {
    switch (notification.entityType) {
      case 'CHANGE_PROPOSAL':
        router.push(
        `/change-proposals/requested?id=${notification.entityId}`,
      )
        break
      case 'PARTICIPANT':
        router.push(`/participants/${notification.entityId}`)
        break
      default:
        router.push('/notifications')
        break
    }
  }
  else {
    router.push('/notifications')
  }
}

// Setup real-time notifications on mount
onMounted(() => {
  // Initial load of notifications
  refreshNotifications()

  // Subscribe to real-time notifications via Pusher
  subscribeToNotifications(notification => {
    // Refresh notifications when a new one is received
    refreshNotifications()

    // Show a snackbar notification
    appStore.showSnack(`New notification: ${notification.message}`)
    play('notification')
  })
})
</script>

<template>
  <IconBtn id="notification-btn">
    <VBadge
      v-bind="props.badgeProps"
      :model-value="unreadCount > 0"
      color="error"
      dot
      offset-x="2"
      offset-y="3"
    >
      <VIcon icon="tabler-bell" />
    </VBadge>

    <VMenu
      activator="parent"
      width="380px"
      :location="props.location"
      offset="12px"
      :close-on-content-click="false"
    >
      <VCard class="d-flex flex-column">
        <!-- 👉 Header -->
        <VCardItem class="notification-section">
          <VCardTitle class="text-h6">
            Notifications
          </VCardTitle>

          <template #append>
            <VChip
              v-show="unreadCount > 0"
              size="small"
              color="primary"
              class="me-2"
            >
              {{ unreadCount }} New
            </VChip>
            <IconBtn
              v-show="formattedNotifications.length"
              size="34"
              :disabled="loadingOperations || !isAllMarkRead"
              @click="markAllReadOrUnread"
            >
              <VIcon
                size="20"
                color="high-emphasis"
                icon="tabler-mail-opened"
              />

              <VTooltip
                activator="parent"
                location="start"
              >
                Mark all as read
              </VTooltip>
            </IconBtn>
          </template>
        </VCardItem>

        <VDivider />

        <!-- 👉 Notifications list -->
        <PerfectScrollbar
          :options="{ wheelPropagation: false }"
          style="max-block-size: 23.75rem;"
        >
          <VList class="notification-list rounded-0 py-0">
            <VListItem
              v-if="loadingUserNotifications"
              class="text-center text-medium-emphasis"
              style="block-size: 56px;"
            >
              <div class="d-flex justify-center align-center">
                <VProgressCircular
                  indeterminate
                  color="primary"
                  size="24"
                />
              </div>
            </VListItem>

            <template
              v-for="(notification, index) in formattedNotifications"
              v-else-if="formattedNotifications.length > 0"
              :key="notification.id"
            >
              <VDivider v-if="index > 0" />
              <VListItem
                link
                lines="one"
                min-height="66px"
                class="list-item-hover-class"
                @click="handleNotificationClick(notification)"
              >
                <!-- Slot: Prepend -->
                <!-- Handles Avatar: Image, Icon, Text -->
                <div class="d-flex align-start gap-3">
                  <VAvatar
                    :color="getNotificationColor(notification.type)"
                    variant="tonal"
                  >
                    <VIcon :icon="getNotificationIcon(notification.type)" />
                  </VAvatar>

                  <div>
                    <p class="text-sm font-weight-medium mb-1">
                      {{ notification.typeDisplayName }}
                    </p>
                    <p
                      class="text-body-2 mb-2"
                      style="letter-spacing: 0.4px !important; line-height: 18px;"
                    >
                      {{ notification.message }}
                    </p>
                    <p
                      class="text-sm text-disabled mb-0"
                      style="letter-spacing: 0.4px !important; line-height: 18px;"
                    >
                      {{ formatNotificationDate(notification.createdAt) }}
                    </p>
                  </div>
                  <VSpacer />

                  <div class="d-flex flex-column align-end">
                    <VIcon
                      size="10"
                      icon="tabler-circle-filled"
                      :color="!notification.read ? 'primary' : '#a8aaae'"
                      :class="`${notification.read ? 'visible-in-hover' : ''}`"
                      class="mb-2"
                      @click.stop="toggleReadUnread(notification.read, notification.id)"
                    />
                  </div>
                </div>
              </VListItem>
            </template>

            <VListItem
              v-else
              class="text-center text-medium-emphasis"
              style="block-size: 56px;"
            >
              <VListItemTitle>No Notifications Found!</VListItemTitle>
            </VListItem>
          </VList>
        </PerfectScrollbar>

        <VDivider />

        <!-- 👉 Footer -->
        <VCardText
          v-show="formattedNotifications.length"
          class="pa-4"
        >
          <VBtn
            block
            size="small"
            @click="router.push('/notifications')"
          >
            View All Notifications
          </VBtn>
        </VCardText>
      </VCard>
    </VMenu>
  </IconBtn>
</template>

<style lang="scss">
  .notification-section {
    padding-block: 0.75rem;
    padding-inline: 1rem;
  }

  .list-item-hover-class {
    .visible-in-hover {
      display: none;
    }

    &:hover {
      .visible-in-hover {
        display: block;
      }
    }
  }

  .notification-list.v-list {
    .v-list-item {
      border-radius: 0 !important;
      margin: 0 !important;
      padding-block: 0.75rem !important;
    }
  }

  // Badge Style Override for Notification Badge
  .notification-badge {
    .v-badge__badge {
      /* stylelint-disable-next-line liberty/use-logical-spec */
      min-width: 18px;
      padding: 0;
      block-size: 18px;
    }
  }
</style>
