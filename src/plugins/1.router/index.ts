import { setupLayouts } from 'virtual:generated-layouts'
import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router/auto'
import { createRouter, createWebHistory } from 'vue-router/auto'
import { useAuthStore } from '@/stores/auth/authStore'
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

function recursiveLayouts(route: RouteRecordRaw): RouteRecordRaw {
  if (route.children) {
    for (let i = 0; i < route.children.length; i++)
      route.children[i] = recursiveLayouts(route.children[i])

    return route
  }

  return setupLayouts([route])[0]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to: any) {
    if (to.hash)
      return { el: to.hash, behavior: 'smooth', top: 60 }

    return { top: 0 }
  },
  extendRoutes: (pages: any) => [
    ...[...pages].map(route => recursiveLayouts(route)),
  ],
})

router.beforeEach((to: any, from: any, next: any) => {
  const authStore = useAuthStore()
  const authUid = localStorage.getItem('uid')
  const isUserLoggedIn = authStore.user != null && authStore.user.uid != null || !!authUid

  const publicRoutes = ['/login', '/register', '/forgot-password', '/reset-password', '/permission-restricted'] // Add all your public routes
  const isPublicRoute = publicRoutes.includes(to.path) || to.path.startsWith('/public/')

  if (!isUserLoggedIn && !isPublicRoute) {
    next({ path: '/login', query: { redirect: to.fullPath } })

    return
  }

  // If user is logged in, check role-based access control
  // Skip permission check for permission-restricted page to avoid infinite loop
  if (isUserLoggedIn && !isPublicRoute && to.path !== '/permission-restricted') {
    const { canAccessRoute } = useRoleAccess()

    // Map routes to permission names
    const routePermissionMap: Record<string, string> = {
      '/': 'root',
      '/participants': 'participants',
      '/participant-export': 'participant-export',
      '/pension-parameters': 'pension-parameters',
      '/change-proposals': 'change-proposals',
      '/change-proposals/requested': 'change-proposals',
      '/change-proposals/history': 'change-proposals-history',
      '/change-proposals/new-participants': 'change-proposals-new-participants',
      '/users': 'users',
      '/certification-management': 'certification-management',
      '/notifications': 'notifications',
    }

    // Get the base route path for permission checking
    const baseRoutePath = getBaseRoutePath(to.path)
    const requiredPermission = routePermissionMap[baseRoutePath]

    if (requiredPermission && !canAccessRoute(requiredPermission)) {
      next({ path: '/permission-restricted', query: { route: to.path }, replace: true })

      return
    }
  }

  next()
})

// Helper function to get base route path
function getBaseRoutePath(path: string): string {
  // Handle dynamic routes by getting the base path
  if (path.startsWith('/participants/') && path !== '/participants')
    return '/participants'

  if (path.startsWith('/participant-export/'))
    return '/participant-export'

  if (path.startsWith('/change-proposals/')) {
    if (path.includes('/history'))
      return '/change-proposals/history'
    if (path.includes('/new-participants'))
      return '/change-proposals/new-participants'

    return '/change-proposals'
  }
  if (path.startsWith('/certification-management/'))
    return '/certification-management'

  if (path.startsWith('/users/'))
    return '/users'

  return path
}

export { router }

export default function (app: App) {
  app.use(router)
}
