<script setup lang="ts">
import { useRoleAccess } from '@/composables/auth/useRoleAccess'

// Use the role access composable
const {
  currentRole,
  isAdmin,
  isReviewer,
  isAccountant,
  isEditor,
  canAccessParticipants,
  canCreateParticipant,
  canEditPensionParameters,
  canApproveChanges,
  canExportData,
} = useRoleAccess()
</script>

<template>
  <div class="role-access-example">
    <VCard class="mb-4">
      <VCardTitle>Current User Role: {{ currentRole }}</VCardTitle>
      <VCardText>
        <VChip
          :color="isAdmin ? 'success' : isReviewer ? 'warning' : isAccountant ? 'primary' : 'info'"
          class="mb-2"
        >
          {{ currentRole }}
        </VChip>
      </VCardText>
    </VCard>

    <!-- Navigation example -->
    <VCard class="mb-4">
      <VCardTitle>Available Navigation</VCardTitle>
      <VCardText>
        <VBtn
          v-if="canAccessParticipants"
          to="/participants"
          class="me-2 mb-2"
          color="primary"
        >
          Participants
        </VBtn>

        <VBtn
          v-if="canEditPensionParameters"
          to="/pension-parameters"
          class="me-2 mb-2"
          color="secondary"
        >
          Pension Parameters
        </VBtn>
      </VCardText>
    </VCard>

    <!-- Action buttons example -->
    <VCard class="mb-4">
      <VCardTitle>Available Actions</VCardTitle>
      <VCardText>
        <VBtn
          v-if="canCreateParticipant"
          class="me-2 mb-2"
          color="success"
          prepend-icon="tabler-plus"
        >
          Create Participant
        </VBtn>

        <VBtn
          v-if="canExportData"
          class="me-2 mb-2"
          color="info"
          prepend-icon="tabler-download"
        >
          Export Data
        </VBtn>

        <VBtn
          v-if="canApproveChanges"
          class="me-2 mb-2"
          color="success"
          prepend-icon="tabler-check"
        >
          Approve Changes
        </VBtn>
      </VCardText>
    </VCard>

    <!-- Role-specific content -->
    <VCard v-if="isAdmin">
      <VCardTitle>Admin Panel</VCardTitle>
      <VCardText>
        <p>You have full access to all features.</p>
      </VCardText>
    </VCard>

    <VCard v-else-if="isReviewer">
      <VCardTitle>Reviewer Panel</VCardTitle>
      <VCardText>
        <p>You can review and approve/reject change proposals.</p>
      </VCardText>
    </VCard>
  </div>
</template>
