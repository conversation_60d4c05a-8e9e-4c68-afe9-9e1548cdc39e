# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Address {
    certificationRejectReason: [CertificationRejectReason!]
    city: String
    country: String
    houseNumber: String
    id: ID!
    pendingChanges: [String!]
    personalInfo: PersonalInfo!
    postalCode: String
    state: String
    street: String
}

"""The approval status of a participant"""
enum ApprovalStatus {
    APPROVED
    PENDING
    REJECTED
}

type AuditLog {
    action: String!
    changes: JSON!
    entityId: String!
    entityType: String!
    id: String!
    ipAddress: String
    proposalId: String
    timestamp: DateTime!
    user: User
    userAgent: String
    userId: String!
    userRole: String
}

type AutoApproveEligible {
    certificationId: String!
    changes: Int!
    participantId: String!
    participantName: String!
}

input BulkApproveCertificationInput {
    certificationIds: [String!]!
}

type BulkApproveCertificationResponse {
    failed: [BulkOperationFailure!]!
    successful: [String!]!
}

type BulkOperationFailure {
    certificationId: String!
    participantId: String!
    reason: String!
}

input BulkRejectCertificationInput {
    certificationIds: [String!]!
    reason: String!
}

type BulkRejectCertificationResponse {
    failed: [BulkOperationFailure!]!
    successful: [String!]!
}

input BulkStartCertificationInput {
    participantIds: [String!]!
    year: Int!
}

type BulkStartCertificationResponse {
    failed: [BulkOperationFailure!]!
    successful: [String!]!
}

type CertificationRejectReason {
    certifiedDataId: String
    certifiedEmploymentInfoId: String
    certifiedIndexationStartOfYearId: String
    certifiedPensionCorrectionsId: String
    certifiedPensionInfoId: String
    certifiedPensionParametersId: String
    certifiedPersonalInfoId: String
    certifiedSalaryEntryId: String
    certifiedVoluntaryContributionsId: String
    createdAt: DateTime!
    field: String!
    id: ID!
    personalInfoId: String
    reason: String!
    status: CertificationRejectReasonStatus!
    submittedAt: DateTime
    submittedForReview: Boolean!
    updatedAt: DateTime!
}

"""The status of a certification reject reason"""
enum CertificationRejectReasonStatus {
    INVALID
    VALID
}

type CertificationStats {
    completed: Int!
    inProgress: Int!
    pendingCertifications: Int!
    requiresAttention: Int!
    totalParticipants: Int!
}

type CertifiedAddress {
    approvedChanges: [String!]
    city: String
    country: String
    differences: [String!]
    houseNumber: String
    id: ID!
    pendingChanges: [String!]
    personalInfo: CertifiedPersonalInfo!
    postalCode: String
    requestedChanges: [String!]
    state: String
    street: String
}

type CertifiedChild {
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    dateOfBirth: DateTime
    differences: [String!]
    firstName: String
    id: ID!
    isOrphan: Boolean!
    isStudying: Boolean!
    lastName: String
    pendingChanges: [String!]
    personalInfo: CertifiedPersonalInfo
    requestedChanges: [String!]
}

type CertifiedData {
    certificationRejectReason: [CertificationRejectReason!]
    certificationStatus: String
    certificationYear: Int!
    certifiedAddress: CertifiedAddress
    certifiedAt: DateTime!
    certifiedBy: User!
    certifiedById: String!
    certifiedChild: [CertifiedChild!]
    certifiedEmploymentInfo: CertifiedEmploymentInfo
    certifiedIndexationStartOfYear: CertifiedIndexationStartOfYear
    certifiedPartnerInfo: [CertifiedPartnerInfo!]
    certifiedPensionCorrections: CertifiedPensionCorrections
    certifiedPensionInfo: CertifiedPensionInfo
    certifiedPensionParameters: CertifiedPensionParameters
    certifiedPersonalInfo: CertifiedPersonalInfo
    certifiedVoluntaryContributions: CertifiedVoluntaryContributions
    differences: [String!]
    id: ID!
    notes: String
    participant: Participant!
    participantId: String!
}

type CertifiedDataByYearResponse {
    """Certifications grouped by year, with keys being the year strings"""
    data: JSONObject!
}

type CertifiedEmploymentInfo {
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    certifiedSalaryEntries: [CertifiedSalaryEntry!]
    department: String
    differences: [String!]
    employeeId: String
    havNum: Int
    id: ID!
    pendingChanges: [String!]
    position: String
    regNum: Int
    requestedChanges: [String!]
    startDate: DateTime
    status: String
}

type CertifiedIndexationStartOfYear {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    differences: [String!]
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    id: ID!
    pendingChanges: [String!]
    requestedChanges: [String!]
}

type CertifiedPartnerInfo {
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedPersonalInfoId: String!
    dateOfBirth: DateTime
    differences: [String!]
    firstName: String
    id: ID!
    isCurrent: Boolean!
    isDeceased: Boolean!
    lastName: String
    pendingChanges: [String!]
    requestedChanges: [String!]
    startDate: DateTime
}

type CertifiedPensionCorrections {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    approvedChanges: [String!]
    attainableGrossAnnualOldAgePension: Float
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    correction: Float
    differences: [String!]
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    id: ID!
    pendingChanges: [String!]
    requestedChanges: [String!]
    year: String
}

type CertifiedPensionInfo {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    approvedChanges: [String!]
    attainableGrossAnnualOldAgePension: Float
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    code: Int
    codeDescription: String
    differences: [String!]
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    grossAnnualPension: Float
    id: ID!
    pendingChanges: [String!]
    pensionBase: Float
    previousCode: Int
    requestedChanges: [String!]
}

type CertifiedPensionParameters {
    accrualPercentage: Float
    annualMultiplier: Float
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    differences: [String!]
    effectiveDate: DateTime
    id: ID!
    offsetAmount: Float
    partnersPensionPercentage: Float
    pendingChanges: [String!]
    requestedChanges: [String!]
    retirementAge: Int
    voluntaryContributionInterestRate: Float
    year: String
}

type CertifiedPersonalInfo {
    address: [CertifiedAddress!]
    approvedChanges: [String!]
    birthDay: Int
    birthMonth: Int
    birthYear: Int
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    children: [CertifiedChild!]
    differences: [String!]
    email: String
    firstName: String
    id: ID!
    lastName: String
    maritalStatus: String
    partnerInfo: [CertifiedPartnerInfo!]
    pendingChanges: [String!]
    phone: String
    requestedChanges: [String!]
}

type CertifiedSalaryEntry {
    amount: Float!
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedEmploymentInfo: CertifiedEmploymentInfo!
    differences: [String!]
    id: String!
    partTimePercentage: Float!
    pendingChanges: [String!]
    requestedChanges: [String!]
    year: Float!
}

type CertifiedVoluntaryContributions {
    approvedChanges: [String!]
    certificationRejectReason: [CertificationRejectReason!]
    certifiedData: CertifiedData!
    certifiedDataId: String!
    contributions: JSON
    differences: [String!]
    id: ID!
    pendingChanges: [String!]
    requestedChanges: [String!]
}

type ChangeData {
    changeProposal: ChangeProposal!
    id: ID!
    newValue: JSON!
    oldValue: JSON
    path: String!
}

type ChangeProposal {
    changePropagated: Boolean!
    changes: [ChangeData!]!
    createdAt: DateTime!
    createdBy: User!
    effectiveDate: DateTime!
    entityId: String!
    entityType: String!
    id: String!
    isCertificationProposal: Boolean
    participantName: String
    reviewComments: String
    reviewedAt: DateTime
    reviewedBy: User
    status: ChangeStatus!
    updatedAt: DateTime!
}

enum ChangeStatus {
    APPROVED
    PENDING
    REJECTED
}

enum ChangeType {
    CERTIFIED_DATA
    PARAMETERS
    PARTICIPANT
    SALARY
}

type Child {
    certificationRejectReason: [CertificationRejectReason!]
    dateOfBirth: DateTime
    firstName: String
    id: String!
    isOrphan: Boolean!
    isStudying: Boolean!
    lastName: String
    pendingChanges: [String!]
    personalInfo: PersonalInfo!
}

type ClaimsResponse {
    claims: FirebaseUserDto
    error: ErrorType
}

type CommonChangePattern {
    certificationIds: [String!]!
    count: Int!
    field: String!
}

input CreateAddressInput {
    city: String
    country: String
    houseNumber: String
    personalInfoId: String!
    postalCode: String
    state: String
    street: String
}

input CreateAuditLogInput {
    action: String!
    changes: JSON!
    entityId: String!
    entityType: String!
    ipAddress: String
    proposalId: String
    userAgent: String
    userId: String
    userRole: String
}

input CreateCertificationRejectReasonInput {
    certifiedDataId: String
    certifiedEmploymentInfoId: String
    certifiedIndexationStartOfYearId: String
    certifiedPensionCorrectionsId: String
    certifiedPensionInfoId: String
    certifiedPensionParametersId: String
    certifiedPersonalInfoId: String
    certifiedSalaryEntryId: String
    certifiedVoluntaryContributionsId: String
    field: String!
    personalInfoId: String
    reason: String!
    status: CertificationRejectReasonStatus = VALID
    submittedAt: DateTime
    submittedForReview: Boolean = false
}

input CreateCertifiedDataInput {
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    certificationYear: Int!
    certifiedAt: DateTime!
    certifiedBy: UserCreateNestedOneWithoutCertifiedDataInput!
    certifiedEmploymentInfo: CreateCertifiedEmploymentInfoInput
    certifiedIndexationStartOfYear: CreateCertifiedIndexationStartOfYearInput
    certifiedPensionCorrections: CreateCertifiedPensionCorrectionsInput
    certifiedPensionInfo: CreateCertifiedPensionInfoInput
    certifiedPensionParameters: CreateCertifiedPensionParametersInput
    certifiedPersonalInfo: CreateCertifiedPersonalInfoInput
    certifiedVoluntaryContributions: CreateCertifiedVoluntaryContributionsInput
    id: String
    notes: String
    participant: ParticipantCreateNestedOneWithoutCertifiedDataInput!
}

input CreateCertifiedEmploymentInfoInput {
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    department: String
    employeeId: String
    havNum: Int
    position: String
    regNum: Int
    salaryEntries: JSON
    startDate: DateTime
    status: String
}

input CreateCertifiedIndexationStartOfYearInput {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
}

input CreateCertifiedPensionCorrectionsInput {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    attainableGrossAnnualOldAgePension: Float
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    correction: Float
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    year: String
}

input CreateCertifiedPensionInfoInput {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    attainableGrossAnnualOldAgePension: Float
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    code: Int
    codeDescription: String
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    pensionBase: Float
}

input CreateCertifiedPensionParametersInput {
    accrualPercentage: Float
    annualMultiplier: Float
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    effectiveDate: DateTime
    offsetAmount: Float
    partnersPensionPercentage: Float
    retirementAge: Int
    voluntaryContributionInterestRate: Float
    year: String
}

input CreateCertifiedPersonalInfoInput {
    address: JSON
    birthDay: Int
    birthMonth: Int
    birthYear: Int
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    children: JSON
    email: String
    firstName: String
    lastName: String
    maritalStatus: String
    partnerInfo: JSON
    phone: String
}

input CreateCertifiedVoluntaryContributionsInput {
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    contributions: JSON
}

input CreateChangeDataInput {
    changeProposalId: String
    newValue: String!
    oldValue: String
    path: String!
}

input CreateChangeProposalInput {
    changePropagated: Boolean = false
    createdById: String!
    effectiveDate: DateTime!
    employmentInfoId: String
    entityId: String!
    entityType: String!
    isCertificationProposal: Boolean
    participantName: String
    reviewComments: String
    reviewedById: String
    status: ChangeStatus
    type: ChangeType
}

input CreateChildInput {
    dateOfBirth: DateTime
    firstName: String
    isOrphan: Boolean
    isStudying: Boolean
    lastName: String
    personalInfoId: String!
}

input CreateDocumentInput {
    createdBy: String
    documentId: String
    mimeType: String
    name: String
    participantId: String!
    path: String
    size: Int
    type: String
    uploadedAt: DateTime
}

input CreateEmploymentInfoInput {
    department: String
    employeeId: String
    endDate: DateTime
    havNum: Float
    participantId: String!
    position: String
    regNum: Float
    startDate: DateTime
    status: String
}

input CreateNewAddressInput {
    city: String
    country: String
    houseNumber: String
    postalCode: String
    state: String
    street: String
}

input CreateNewChildInput {
    dateOfBirth: String
    firstName: String
    isOrphan: Boolean = false
    isStudying: Boolean = false
    lastName: String
}

input CreateNewEmploymentInfoInput {
    department: String
    employeeId: String
    endDate: String
    havNum: Float
    position: String
    regNum: Float
    salaryEntries: [CreateSalaryEntryInput!]
    startDate: String
    status: String
}

input CreateNewPartnerInfoInput {
    dateOfBirth: String
    firstName: String
    isCurrent: Boolean!
    isDeceased: Boolean = false
    lastName: String
    startDate: String
}

input CreateNewPensionDataInput {
    pensionParameters: [CreatePensionParametersInput!]
    pensionableAmount: Float
    retirementDate: String
    status: String
    totalContributions: Float
}

input CreateNewPensionInfoInput {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    attainableGrossAnnualOldAgePension: Float
    code: Float
    codeDescription: String
    codeEffectiveDate: String
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    pensionBase: Float
}

input CreateNotificationInput {
    entityId: String!
    entityType: String!
    message: String!
    recipientId: String!
    type: String!
}

input CreateParticipantInput {
    approvalStatus: ApprovalStatus = PENDING
    createdBy: String!
    employmentInfo: CreateNewEmploymentInfoInput
    lastModified: String
    pensionData: CreateNewPensionDataInput
    pensionInfo: CreateNewPensionInfoInput
    personalInfo: CreatePersonalInfoInput
    rejectReason: String
    status: String = "active"
    updatedBy: String!
}

input CreatePartnerInfoInput {
    dateOfBirth: DateTime
    firstName: String
    isCurrent: Boolean!
    isDeceased: Boolean!
    lastName: String
    personalInfoId: String!
    startDate: DateTime
}

input CreatePensionCorrectionsInput {
    correction: Float!
    createdById: ID!
    reviewedAt: DateTime!
    reviewedById: ID!
    year: String!
}

input CreatePensionInfoInput {
    code: Int
    codeDescription: String
    codeEffectiveDate: DateTime
    codeImpact: String
    participantId: ID!
    previousCode: Int
    previousCodeEffectiveDate: DateTime
}

input CreatePensionParametersInput {
    accrualPercentage: Float!
    annualMultiplier: Float!
    effectiveDate: DateTime!
    offsetAmount: Float!
    partnersPensionPercentage: Float!
    retirementAge: Int!
    userId: ID!
    voluntaryContributionInterestRate: Float!
    year: String!
}

input CreatePersonalInfoInput {
    address: CreateNewAddressInput
    birthDay: Float
    birthMonth: Float
    birthYear: Float
    children: [CreateNewChildInput!]
    email: String
    firstName: String!
    lastName: String!
    maritalStatus: String
    partnerInfo: [CreateNewPartnerInfoInput!]
    phone: String
}

input CreateRoleInput {
    description: String
    name: String!
}

input CreateSalaryEntryInput {
    amount: Float!
    employmentInfoId: String
    partTimePercentage: Float!
    year: Float!
}

input CreateSystemSettingInput {
    autoApproveChanges: Boolean! = false
    effectiveDate: DateTime!
    passwordExpiryDays: Int! = 90
    requireTwoFactorAuth: Boolean! = true
    sessionTimeout: Int! = 30
    userId: String!
}

input CreateUserInput {
    email: String!
    firebaseUid: String
    firstname: String
    lastname: String
    roleId: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type Document {
    """Example field (placeholder)"""
    exampleField: Int!
}

type EmploymentInfo {
    certificationRejectReason: [CertificationRejectReason!]
    department: String
    employeeId: String
    endDate: DateTime
    havNum: Float
    id: ID!
    participant: Participant!
    pendingChanges: [String!]
    position: String
    regNum: Float
    salaryEntries: [SalaryEntry!]!
    startDate: DateTime
    status: String
}

type ErrorType {
    code: String
    message: String!
}

input FindAllAuditLogsInput {
    action: String
    endDate: DateTime
    entityId: String
    entityType: String
    proposalId: String
    skip: Float
    sortBy: String
    sortOrder: String
    startDate: DateTime
    take: Float
    userId: String
}

input FindAllCertifiedDataInput {
    certificationYear: Int
    participantId: String
    skip: Int
    sortBy: String
    sortOrder: String
    take: Int
}

input FindAllDocumentsInput {
    participantId: String
    searchTerm: String
    skip: Float
    take: Float
    type: String
}

input FindAllParticipantsInput {
    approvalStatus: ApprovalStatus
    searchTerm: String
    skip: Float
    sortBy: String
    sortOrder: String
    status: String
    take: Float
}

input FindOneCertifiedDataInput {
    certificationYear: Int
    id: ID
    participantId: String
}

type FirebaseUserDto {
    aud: String
    auth_time: Float
    email: String
    email_verified: Boolean
    exp: Float
    iat: Float
    iss: String
    name: String
    pensionUserId: String
    role: String
    roleId: String
    sub: String
    uid: String
    user_id: String
}

input GetAutoApproveEligibleInput {
    year: Int!
}

input GetCertificationStatsInput {
    year: Int!
}

input GetCommonChangePatternsInput {
    year: Int!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject

type Mutation {
    approveChangeProposal(changePropagated: Boolean = false, changeProposalId: String!, reviewerId: String!): ChangeProposal!
    bulkApproveCertification(input: BulkApproveCertificationInput!): BulkApproveCertificationResponse!
    bulkRejectCertification(input: BulkRejectCertificationInput!): BulkRejectCertificationResponse!
    bulkStartCertification(input: BulkStartCertificationInput!): BulkStartCertificationResponse!
    createAddress(createAddressInput: CreateAddressInput!): Address!
    createAuditLog(createAuditLogInput: CreateAuditLogInput!): AuditLog!
    createCertificationRejectReason(createCertificationRejectReasonInput: CreateCertificationRejectReasonInput!): CertificationRejectReason!
    createCertifiedData(certifiedById: String!, createCertifiedDataInput: CreateCertifiedDataInput!, participantId: String!): CertifiedData!
    createCertifiedEmploymentInfo(certifiedDataId: String!, createCertifiedEmploymentInfoInput: CreateCertifiedEmploymentInfoInput!): CertifiedEmploymentInfo!
    createCertifiedIndexationStartOfYear(certifiedDataId: String!, createCertifiedIndexationInput: CreateCertifiedIndexationStartOfYearInput!): CertifiedIndexationStartOfYear!
    createCertifiedPensionCorrections(certifiedDataId: String!, createCertifiedCorrectionsInput: CreateCertifiedPensionCorrectionsInput!): CertifiedPensionCorrections!
    createCertifiedPensionInfo(certifiedDataId: String!, createCertifiedPensionInfoInput: CreateCertifiedPensionInfoInput!): CertifiedPensionInfo!
    createCertifiedPensionParameters(certifiedDataId: String!, createCertifiedParametersInput: CreateCertifiedPensionParametersInput!): CertifiedPensionParameters!
    createCertifiedPersonalInfo(certifiedDataId: String!, createCertifiedPersonalInfoInput: CreateCertifiedPersonalInfoInput!): CertifiedPersonalInfo!
    createCertifiedVoluntaryContributions(certifiedDataId: String!, createCertifiedContributionsInput: CreateCertifiedVoluntaryContributionsInput!): CertifiedVoluntaryContributions!
    createChangeData(createChangeDataInput: CreateChangeDataInput!): ChangeData!
    createChangeProposal(createChangeDataInput: CreateChangeDataInput, createChangeProposalInput: CreateChangeProposalInput!): ChangeProposal!
    createChild(createChildInput: CreateChildInput!): Child!
    createDocument(createDocumentInput: CreateDocumentInput!): Document!
    createEmploymentInfo(createEmploymentInfoInput: CreateEmploymentInfoInput!): EmploymentInfo!
    createNotification(createNotificationInput: CreateNotificationInput!): Notification!
    createParticipant(createParticipantInput: CreateParticipantInput!): Participant!
    createPartnerInfo(createPartnerInfoInput: CreatePartnerInfoInput!): PartnerInfo!
    createPensionCorrections(createPensionCorrectionsInput: CreatePensionCorrectionsInput!): PensionCorrections!
    createPensionInfo(createPensionInfoInput: CreatePensionInfoInput!): PensionInfo!
    createPensionParameters(createPensionParametersInput: CreatePensionParametersInput!): PensionParameters!
    createRole(createRoleInput: CreateRoleInput!): Role!
    createSalaryEntry(createSalaryEntryInput: CreateSalaryEntryInput!): SalaryEntry!
    createSystemSetting(createSystemSettingInput: CreateSystemSettingInput!): SystemSetting!
    createUser(createUserInput: CreateUserInput!): User!
    deleteAddress(id: String!): Address!
    deleteChangeData(id: String!): ChangeData!
    deleteChangeProposal(id: String!): ChangeProposal!
    deleteChild(id: String!): Child!
    deleteEmploymentInfo(id: String!): EmploymentInfo!
    deleteSalaryEntry(id: String!): SalaryEntry!
    getLatestApprovedChange(entityType: String!, path: String!): ChangeProposal
    markAllNotificationsAsRead(recipientId: String!): Boolean!
    markNotificationAsRead(id: String!): Notification!
    rejectChangeProposal(changeProposalId: String!, reviewerComments: String!, reviewerId: String!): ChangeProposal!
    rejectField(input: RejectFieldInput!): RejectFieldResponse!
    rejectPersonalInfoField(fieldName: String!, id: ID!, rejectReason: String!, userId: String!): PersonalInfo!
    removeCertificationRejectReason(id: String!): CertificationRejectReason!
    removeDocument(id: String!): Document!
    removeNotification(id: String!): Notification!
    removeParticipant(id: String!): Participant!
    removePartnerInfo(id: ID!): PartnerInfo!
    removePensionCorrections(id: ID!): PensionCorrections!
    removePensionInfo(id: ID!): PensionInfo!
    removePensionParameters(id: ID!): PensionParameters!
    removePersonalInfo(id: ID!): PersonalInfo!
    removeRole(id: ID!): Role!
    removeSystemSetting(id: String!): SystemSetting!
    removeUser(id: String!): User!
    revertApprovedRejectedChanges(certificationYear: Int!): RevertChangesResponse!
    revertSingleField(input: RevertSingleFieldInput!): RevertSingleFieldResponse!
    setUserClaims(firebaseUid: String!): ClaimsResponse!
    updateAddress(updateAddressInput: UpdateAddressInput!): Address!
    updateCertificationStatus(input: UpdateCertificationStatusInput!): CertifiedData!
    updateCertifiedChild(updateCertifiedChildInput: UpdateCertifiedChildInput!): CertifiedChild!
    updateCertifiedDataApprovedChanges(approvedChanges: [String!]!, entityId: String, entityType: String, id: String!): CertifiedData!
    updateCertifiedDataRejectedChanges(entityId: String, entityType: String, id: String!, rejectReason: String, rejectedChanges: [String!]!): CertifiedData!
    updateChangeData(updateChangeDataInput: UpdateChangeDataInput!): ChangeData!
    updateChangeProposal(updateChangeProposalInput: UpdateChangeProposalInput!): ChangeProposal!
    updateChild(updateChildInput: UpdateChildInput!): Child!
    updateDocument(updateDocumentInput: UpdateDocumentInput!): Document!
    updateEmploymentInfo(updateEmploymentInfoInput: UpdateEmploymentInfoInput!): EmploymentInfo!
    updateNotification(updateNotificationInput: UpdateNotificationInput!): Notification!
    updateParticipant(updateParticipantInput: UpdateParticipantInput!): Participant!
    updatePartnerInfo(updatePartnerInfoInput: UpdatePartnerInfoInput!): PartnerInfo!
    updatePensionCorrections(updatePensionCorrectionsInput: UpdatePensionCorrectionsInput!): PensionCorrections!
    updatePensionInfo(updatePensionInfoInput: UpdatePensionInfoInput!): PensionInfo!
    updatePensionParameters(updatePensionParametersInput: UpdatePensionParametersInput!): PensionParameters!
    updateRole(updateRoleInput: UpdateRoleInput!): Role!
    updateSalaryEntry(updateSalaryEntryInput: UpdateSalaryEntryInput!): SalaryEntry!
    updateSystemSetting(updateSystemSettingInput: UpdateSystemSettingInput!): SystemSetting!
    updateUser(updateUserInput: UpdateUserInput!): User!
    updateUserLastLogin(id: String!): User!
}

input NewUserInput {
    email: String!
    name: String
    resetLink: String!
}

type Notification {
    createdAt: DateTime!
    createdBy: User!
    createdById: String!
    entityId: String!
    entityType: String!
    id: ID!
    message: String!
    read: Boolean!
    readAt: DateTime
    recipient: User!
    recipientId: String!
    type: String!
}

type PaginatedAuditLogs {
    items: [AuditLog!]!
    totalCount: Int!
}

type PaginatedCertifiedData {
    items: [CertifiedData!]!
    totalCount: Int!
}

type PaginatedParticipants {
    items: [Participant!]!
    totalCount: Int!
}

type Participant {
    approvalStatus: ApprovalStatus!
    certifiedData: [CertifiedData!]
    changeProposals: [ChangeProposal!]
    createdAt: DateTime!
    createdBy: String!
    documents: [Document!]
    employmentInfo: EmploymentInfo
    id: ID!
    lastModified: DateTime
    pensionInfo: PensionInfo
    personalInfo: PersonalInfo
    rejectReason: String
    status: String!
    updatedAt: DateTime!
    updatedBy: String!
}

input ParticipantCreateNestedOneWithoutCertifiedDataInput {
    connect: String!
}

type PartnerInfo {
    certificationRejectReason: [CertificationRejectReason!]
    dateOfBirth: DateTime
    firstName: String
    id: ID!
    isCurrent: Boolean!
    isDeceased: Boolean!
    lastName: String
    pendingChanges: [String!]
    personalInfoId: String!
    startDate: DateTime
}

type PensionCorrections {
    correction: Float!
    createdAt: DateTime!
    createdBy: User!
    id: ID!
    reviewedAt: DateTime!
    reviewedBy: User!
    updatedAt: DateTime!
    year: String!
}

type PensionInfo {
    accruedGrossAnnualOldAgePension: Float
    accruedGrossAnnualPartnersPension: Float
    accruedGrossAnnualSinglesPension: Float
    attainableGrossAnnualOldAgePension: Float
    certificationRejectReason: [CertificationRejectReason!]
    code: Int
    codeDescription: String
    codeEffectiveDate: DateTime
    codeImpact: String
    extraAccruedGrossAnnualOldAgePension: Float
    extraAccruedGrossAnnualPartnersPension: Float
    grossAnnualDisabilityPension: Float
    grossAnnualPension: Float
    id: ID!
    participant: Participant!
    pendingChanges: [String!]
    pensionBase: Float
    previousCode: Int
    previousCodeEffectiveDate: DateTime
}

type PensionParameters {
    accrualPercentage: Float!
    annualMultiplier: Float!
    createdAt: DateTime!
    effectiveDate: DateTime!
    id: String!
    offsetAmount: Float!
    partnersPensionPercentage: Float!
    pendingChanges: [String!]
    retirementAge: Int!
    updatedAt: DateTime!
    updatedBy: User!
    userId: String!
    voluntaryContributionInterestRate: Float!
    year: String!
}

type PersonalInfo {
    address: Address
    birthDay: Int
    birthMonth: Int
    birthYear: Int
    certificationRejectReason: [CertificationRejectReason!]
    children: [Child!]
    email: String
    firstName: String!
    id: ID!
    lastName: String!
    maritalStatus: String
    participantId: String!
    partnerInfo: [PartnerInfo!]
    pendingChanges: [String!]
    phone: String
}

type Query {
    address(id: String!): Address!
    addresses: [Address!]!
    auditLog(id: String!): AuditLog!
    auditLogs(findAllAuditLogsInput: FindAllAuditLogsInput): PaginatedAuditLogs!
    certificationRejectReason(id: String!): CertificationRejectReason!
    certificationRejectReasons: [CertificationRejectReason!]!
    certifiedAddress(id: String!): CertifiedAddress!
    certifiedContributionsByCertifiedDataId(certifiedDataId: String!): CertifiedVoluntaryContributions!
    certifiedCorrectionsByCertifiedDataId(certifiedDataId: String!): CertifiedPensionCorrections!
    certifiedEmploymentInfo(id: String!): CertifiedEmploymentInfo!
    certifiedEmploymentInfoByCertifiedDataId(certifiedDataId: String!): CertifiedEmploymentInfo!
    certifiedIndexationByCertifiedDataId(certifiedDataId: String!): CertifiedIndexationStartOfYear!
    certifiedIndexationStartOfYear(id: String!): CertifiedIndexationStartOfYear!
    certifiedParametersByCertifiedDataId(certifiedDataId: String!): CertifiedPensionParameters!
    certifiedPartnerInfo(id: String!): CertifiedPartnerInfo!
    certifiedPensionCorrections(id: String!): CertifiedPensionCorrections!
    certifiedPensionInfo(id: String!): CertifiedPensionInfo!
    certifiedPensionInfoByCertifiedDataId(certifiedDataId: String!): CertifiedPensionInfo!
    certifiedPensionParameters(id: String!): CertifiedPensionParameters!
    certifiedPersonalInfo(id: String!): CertifiedPersonalInfo!
    certifiedPersonalInfoByCertifiedDataId(certifiedDataId: String!): CertifiedPersonalInfo!
    certifiedVoluntaryContributions(id: String!): CertifiedVoluntaryContributions!
    child(id: String!): Child!
    document(id: String!): Document!
    documents(findAllDocumentsInput: FindAllDocumentsInput): [Document!]!
    employmentInfo(id: String!): EmploymentInfo!
    employmentInfos: [EmploymentInfo!]!
    entityAuditLogs(entityId: String!, entityType: String!): [AuditLog!]!
    findOneChangeProposal(id: String!): ChangeProposal!
    getAllCertifiedData(findAllCertifiedDataInput: FindAllCertifiedDataInput): PaginatedCertifiedData!
    getAllChangeProposals: [ChangeProposal!]!
    getAllNotifications: [Notification!]!
    getAllParticipantChangeProposals(reviewerId: String!): [ChangeProposal!]!
    getAllParticipantChangeProposalsHistory(reviewerId: String!): [ChangeProposal!]!
    getAllParticipants: [Participant!]!
    getAllParticipantsWithFilter(findAllParticipantsInput: FindAllParticipantsInput): PaginatedParticipants!
    getAllPensionParamChangeProposals(reviewerId: String!): [ChangeProposal!]!
    getAllPensionParamChangeProposalsHistory(reviewerId: String!): [ChangeProposal!]!
    getAllPensionParameters: [PensionParameters!]!
    getAllSystemSettings: [SystemSetting!]!
    getAllUsers: [User!]!
    getAutoApproveEligible(input: GetAutoApproveEligibleInput!): [AutoApproveEligible!]!
    getCertificationStats(input: GetCertificationStatsInput!): CertificationStats!
    getCertifiedDataById(findOneCertifiedDataInput: FindOneCertifiedDataInput!): CertifiedData!
    getCertifiedDataByYearAndYearBefore(certificationYear: Int!): CertifiedDataByYearResponse!
    getCommonChangePatterns(input: GetCommonChangePatternsInput!): [CommonChangePattern!]!
    getLatestSystemSetting: SystemSetting!
    getNotificationsByRecipient(recipientId: String!): [Notification!]!
    getParticipantById(id: String!): Participant!
    getParticipantCertifiedData(participantId: String!): [CertifiedData!]!
    getPensionChildren: [Child!]!
    getPensionParamById(id: ID!): PensionParameters!
    getSystemSettingById(id: String!): SystemSetting!
    getUserByEmail(email: String!): User!
    getUserById(id: String!): User!
    latestParticipantCertification(participantId: String!): CertifiedData
    participantDocuments(participantId: String!): [Document!]!
    partnerInfo(id: ID!): PartnerInfo!
    partnerInfos: [PartnerInfo!]!
    pensionCorrection(id: ID!): PensionCorrections!
    pensionCorrections: [PensionCorrections!]!
    pensionInfo(id: ID!): PensionInfo!
    pensionInfos: [PensionInfo!]!
    personalInfo(id: ID!): PersonalInfo!
    personalInfos: [PersonalInfo!]!
    previewRevertChanges(certificationYear: Int!): RevertChangesPreview!
    role(id: ID!): Role!
    roles: [Role!]!
    salaryEntries: [SalaryEntry!]!
    salaryEntry(id: String!): SalaryEntry!
    sendNewUserEmail(data: NewUserInput!): String!
    sendRestPasswordEmail(data: ResetPasswordInput!): String!
    timeRangeAuditLogs(endDate: DateTime!, startDate: DateTime!): [AuditLog!]!
    totalAuditLogs(entityType: String): Int!
    totalParticipants(status: String): Int!
    unreadNotificationCount: Int!
    unreadNotifications: [Notification!]!
    userAuditLogs(userId: String!): [AuditLog!]!
    userByFirebaseUid(firebaseUid: String!): User!
    yearCertifications(certificationYear: Int!): [CertifiedData!]!
}

input RejectFieldInput {
    entityId: ID!
    entityType: String!
    fieldName: String!
    rejectReason: String!
    userId: ID!
}

type RejectFieldResponse {
    entityId: ID!
    entityType: String!
    fieldName: String!
    message: String!
    success: Boolean!
}

input ResetPasswordInput {
    email: String!
    resetLink: String!
}

type RevertChangesPreview {
    affectedEntities: [RevertChangesPreviewItem!]!
    certificationYear: Int!
    estimatedImpact: String!
    totalCertifiedDataRecords: Int!
    totalEntitiesAffected: Int!
    totalRejectionReasons: Int!
}

type RevertChangesPreviewItem {
    approvedChanges: [String!]!
    approvedChangesCount: Int!
    certifiedDataId: String!
    entityId: String!
    entityType: String!
    participantName: String!
    requestedChanges: [String!]!
    requestedChangesCount: Int!
}

type RevertChangesResponse {
    certificationYear: Int!
    message: String!
    revertedEntities: [RevertedEntityInfo!]
    success: Boolean!
    totalCertifiedDataRecords: Int!
    totalEntitiesReverted: Int!
    totalRejectionReasonsDeleted: Int!
}

input RevertSingleFieldInput {
    entityId: String!
    entityType: String!
    path: String!
}

type RevertSingleFieldResponse {
    entityId: String!
    entityType: String!
    message: String!
    path: String!
    success: Boolean!
}

type RevertedEntityInfo {
    approvedChangesCleared: [String!]!
    certifiedDataId: String!
    entityId: String!
    entityType: String!
    requestedChangesCleared: [String!]!
}

type Role {
    description: String
    id: ID!
    name: String!
}

type SalaryEntry {
    amount: Float!
    certificationRejectReason: [CertificationRejectReason!]
    employmentInfo: EmploymentInfo!
    id: ID!
    partTimePercentage: Float!
    pendingChanges: [String!]
    year: Float!
}

type Subscription {
    userNotifications: Notification!
}

type SystemSetting {
    autoApproveChanges: Boolean!
    createdAt: DateTime!
    effectiveDate: DateTime!
    id: String!
    passwordExpiryDays: Int!
    requireTwoFactorAuth: Boolean!
    sessionTimeout: Int!
    updatedAt: DateTime!
    updatedBy: User!
    userId: String!
}

input UpdateAddressInput {
    city: String
    country: String
    houseNumber: String
    id: String!
    personalInfoId: String
    postalCode: String
    state: String
    street: String
}

input UpdateCertificationStatusInput {
    id: String!
    status: String!
}

input UpdateCertifiedChildInput {
    certificationRejectReason: [CreateCertificationRejectReasonInput!]
    dateOfBirth: DateTime
    firstName: String
    id: String!
    isOrphan: Boolean
    isStudying: Boolean
    lastName: String
}

input UpdateChangeDataInput {
    changeProposalId: String
    id: String!
    newValue: String
    oldValue: String
    path: String
}

input UpdateChangeProposalInput {
    changePropagated: Boolean = false
    createdById: String
    effectiveDate: DateTime
    employmentInfoId: String
    entityId: String
    entityType: String
    id: String!
    isCertificationProposal: Boolean
    participantName: String
    reviewComments: String
    reviewedById: String
    status: ChangeStatus
    type: ChangeType
}

input UpdateChildInput {
    dateOfBirth: DateTime
    firstName: String
    id: String!
    isOrphan: Boolean
    isStudying: Boolean
    lastName: String
    personalInfoId: String
}

input UpdateDocumentInput {
    createdBy: String
    documentId: String
    id: String!
    mimeType: String
    name: String
    participantId: String
    path: String
    size: Int
    type: String
    uploadedAt: DateTime
}

input UpdateEmploymentInfoInput {
    department: String
    employeeId: String
    endDate: DateTime
    havNum: Float
    id: String!
    participantId: String
    position: String
    regNum: Float
    startDate: DateTime
    status: String
}

input UpdateNotificationInput {
    entityId: String
    entityType: String
    id: ID!
    message: String
    read: Boolean
    recipientId: String
    type: String
}

input UpdateParticipantInput {
    approvalStatus: ApprovalStatus = PENDING
    createdBy: String
    employmentInfo: CreateNewEmploymentInfoInput
    id: String!
    lastModified: String
    pensionData: CreateNewPensionDataInput
    pensionInfo: CreateNewPensionInfoInput
    personalInfo: CreatePersonalInfoInput
    rejectReason: String
    status: String = "active"
    updatedBy: String
}

input UpdatePartnerInfoInput {
    dateOfBirth: DateTime
    firstName: String
    id: ID!
    isCurrent: Boolean!
    isDeceased: Boolean!
    lastName: String
    personalInfoId: String!
    startDate: DateTime
}

input UpdatePensionCorrectionsInput {
    correction: Float
    createdById: ID
    id: String!
    reviewedAt: DateTime
    reviewedById: ID
    year: String
}

input UpdatePensionInfoInput {
    code: Int
    codeDescription: String
    codeEffectiveDate: DateTime
    codeImpact: String
    id: String!
    participantId: ID
    previousCode: Int
    previousCodeEffectiveDate: DateTime
}

input UpdatePensionParametersInput {
    accrualPercentage: Float
    annualMultiplier: Float
    effectiveDate: DateTime
    id: String!
    offsetAmount: Float
    partnersPensionPercentage: Float
    retirementAge: Int
    userId: ID
    voluntaryContributionInterestRate: Float
    year: String
}

input UpdateRoleInput {
    description: String
    id: String!
    name: String
}

input UpdateSalaryEntryInput {
    amount: Float
    employmentInfoId: String
    id: String!
    partTimePercentage: Float
    year: Float
}

input UpdateSystemSettingInput {
    autoApproveChanges: Boolean = false
    effectiveDate: DateTime
    id: String!
    passwordExpiryDays: Int = 90
    requireTwoFactorAuth: Boolean = true
    sessionTimeout: Int = 30
    userId: String
}

input UpdateUserInput {
    email: String
    firebaseUid: String
    firstname: String
    id: String!
    lastname: String
    roleId: String
}

type User {
    email: String!
    firebaseUid: String!
    firstname: String
    id: ID!
    lastLogin: DateTime
    lastname: String
    role: Role
}

input UserCreateNestedOneWithoutCertifiedDataInput {
    connect: String!
}