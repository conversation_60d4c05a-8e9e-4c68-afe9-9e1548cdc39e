# Pusher Backend Setup for Public Channels

## Overview
The frontend has been updated to use **public channels only** to avoid authentication complexity and fix the connection error 1006.

## Channel Configuration

### Public Channels Used
- Channel name format: `user-{pensionUserId}`
- No authentication required
- Simpler backend implementation

## Backend Implementation

### Example Node.js/Express Setup

```javascript
const Pusher = require('pusher');

const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID,
  key: process.env.PUSHER_KEY,
  secret: process.env.PUSHER_SECRET,
  cluster: process.env.PUSHER_CLUSTER,
  useTLS: true
});

// Function to send notification to user
async function sendNotificationToUser(pensionUserId, notificationData) {
  const channelName = `user-${pensionUserId}`;

  try {
    await pusher.trigger(channelName, 'notification.created', {
      id: notificationData.id,
      message: notificationData.message,
      type: notificationData.type,
      createdAt: notificationData.createdAt,
      // Add any other notification data
    });

    console.log(`Notification sent to channel: ${channelName}`);
  } catch (error) {
    console.error('Error sending Pusher notification:', error);
  }
}

// Example usage in your notification creation logic
app.post('/api/notifications', async (req, res) => {
  try {
    // Create notification in database
    const notification = await createNotification(req.body);

    // Send real-time notification via Pusher
    await sendNotificationToUser(notification.recipientId, notification);

    res.json(notification);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## Security Considerations

Since we're using public channels, ensure that:

1. **Data Filtering**: Only send necessary notification data (no sensitive information)
2. **Server-side Validation**: Always validate user permissions on the backend
3. **Rate Limiting**: Implement rate limiting for notification endpoints
4. **Channel Naming**: Use consistent channel naming: `user-{pensionUserId}`

## Testing the Setup

1. **Check Backend Logs**: Monitor your backend logs when sending notifications
2. **Use Debug Page**: Navigate to `/debug/pusher` in your frontend to monitor connection status
3. **Test Notification Flow**: Create a test notification and verify it appears in real-time

## Environment Variables Required

Make sure your backend has these environment variables:

```env
PUSHER_APP_ID=*******
PUSHER_KEY=c2c5af4903c08bc3dd29
PUSHER_SECRET=129757dbbdb7fc4ac146
PUSHER_CLUSTER=ap4
```

## Common Issues

1. **Channel Naming**: Ensure channel names match exactly: `user-{pensionUserId}`
2. **Event Naming**: Use the exact event name: `notification.created`
3. **Network**: Check if there are any firewall/proxy issues blocking WebSocket connections
4. **Data Format**: Ensure notification data structure matches what the frontend expects

## Benefits of Public Channels

1. **Simplicity**: No authentication endpoint required
2. **Reliability**: Fewer points of failure
3. **Performance**: Faster connection establishment
4. **Debugging**: Easier to troubleshoot connection issues

## Migration from Private Channels

If you were previously using private channels, update your backend to:

1. Remove the `/pusher/auth` endpoint (no longer needed)
2. Change channel names from `private-user-{id}` to `user-{id}`
3. Remove any channel authorization logic
